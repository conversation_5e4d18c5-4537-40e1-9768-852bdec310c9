﻿using System;
using System.IO;
using System.Windows.Forms;
using DevExpress.DXperience.Demos;
using DevExpress.XtraPrinting;
using DevExpress.XtraPrintingLinks;
using DevExpress.XtraRichEdit;

namespace DevExpress.Docs.Demos {
    public partial class WordRTFAccessiblePDF : TutorialControlBase {
        const string openFileDialogFilter =
            "All Supported Files (*.rtf;*.doc;*.docx;*.html;*.mht;*.txt;*.xml;*.odt;*.epub)|*.rtf;*.doc;*.docx;*.html;*.mht;*.txt;*.xml;*.odt;*.epub|" +
            "Word Documents (*.docx)|*.docx|" +
            "Word 97-2003 Documents (*.doc)|*.doc|" +
            "Rich Text Format (*.rtf)|*.rtf|" +
            "HTML Files (*.html)|*.html|" +
            "MHT Files (*.mht)|*.mht|" +
            "Text Files (*.txt)|*.txt|" +
            "XML Files (*.xml)|*.xml|" +
            "OpenDocument Text (*.odt)|*.odt|" +
            "Electronic Publication (*.epub)|*.epub|" +
            "All files (*.*)|*.*";

        readonly PrintableComponentLinkBase link;
        RichEditDocumentServer documentServer;
        public WordRTFAccessiblePDF() {
            InitializeComponent();

            printPreviewControl.PrintingSystem = new DevExpress.XtraPrinting.PrintingSystem();
            link = new DevExpress.XtraPrintingLinks.PrintableComponentLinkBase(printPreviewControl.PrintingSystem);
            edtFilePath.Text = DemoUtils.GetRelativePath("Scientific.docx");
            edtSaveTo.Text = DevExpress.Data.Utils.SafeEnvironment.MyDocuments;
            LoadDocument();
        }
        void btnExport_Click(object sender, EventArgs e) {
            string format = ((Control)sender).Tag.ToString();
            string filePath = edtFilePath.Text;
            string fileName = Path.GetFileNameWithoutExtension(filePath);
            string pathString = Path.Combine(edtSaveTo.Text, fileName);
            string resultFilePath = Path.GetFullPath(pathString) + ".pdf";
            PdfExportOptions exportOptions = new PdfExportOptions();
            if(format == "pdf/a-1a") {
                exportOptions.PdfACompatibility = PdfACompatibility.PdfA1a;
            }
            else if(format == "pdf/a-2a") {
                exportOptions.PdfACompatibility = PdfACompatibility.PdfA2a;
            }
            else if(format == "pdf/a-3a") {
                exportOptions.PdfACompatibility = PdfACompatibility.PdfA3a;
            }
            else if(format == "pdf/ua") {
                exportOptions.PdfUACompatibility = PdfUACompatibility.PdfUA1;
            }
            documentServer.ExportToPdf(resultFilePath, exportOptions);

            if(!string.IsNullOrEmpty(resultFilePath))
                DemoUtils.PreviewDocument(resultFilePath);
        }
        void LoadDocument() {
            if(documentServer == null) {
                documentServer = new RichEditDocumentServer();
                new RichEditDemoExceptionsHandler(documentServer).Install();
            }
            string path = edtFilePath.Text;
            documentServer.LoadDocument(path);
            link.Component = documentServer;
            link.CreateDocument();
        }
        void edtFilePath_ButtonClick(object sender, XtraEditors.Controls.ButtonPressedEventArgs e) {
            ChooseFileToOpen(String.Empty);
        }
        void ChooseFileToOpen(string initialPath) {
            using(OpenFileDialog openFileDialog = new OpenFileDialog()) {
                openFileDialog.Filter = openFileDialogFilter;
                if(!String.IsNullOrEmpty(initialPath))
                    openFileDialog.InitialDirectory = initialPath;
                if(openFileDialog.ShowDialog() != DialogResult.OK)
                    return;
                edtFilePath.Text = openFileDialog.FileName;
                LoadDocument();
            }
        }
        void edtFilePath_KeyUp(object sender, KeyEventArgs e) {
            if(e.KeyCode != Keys.Enter)
                return;
            FileInfo fileInfo = new FileInfo(edtFilePath.Text);
            if(fileInfo.Exists) {
                LoadDocument();
                return;
            }
            ChooseFileToOpen(edtFilePath.Text);
        }
        void ChooseFolderToSave() {
            using(FolderBrowserDialog openFileDialog = new FolderBrowserDialog()) {
                if(openFileDialog.ShowDialog() != DialogResult.OK)
                    return;
                edtSaveTo.Text = openFileDialog.SelectedPath;
            }
        }
        void edtSaveTo_ButtonClick(object sender, XtraEditors.Controls.ButtonPressedEventArgs e) {
            ChooseFolderToSave();
        }
    }
}
