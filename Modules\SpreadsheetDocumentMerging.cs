﻿using System;
using System.Windows.Forms;
using DevExpress.DXperience.Demos;
using DevExpress.Spreadsheet;
using DevExpress.XtraEditors;

namespace DevExpress.Docs.Demos {
    public partial class SpreadsheetDocumentMerging : TutorialControlBase {
        const string openFileDialogFilter =
            "All Supported Files (*.xlsx;*.xlsm;*.xlsb;*.xls;*.xltx;*.xltm;*.xlt;*.csv;*.txt)|*.xlsx;*.xlsm;*.xlsb;*.xls;*.xltx;*.xltm;*.xlt;*.csv;*.txt|" +
            "Excel Workbook (*.xlsx)|*.xlsx|" +
            "Excel Macro-Enabled Workbook (*.xlsm)|*.xlsm|" +
            "Excel Binary Workbook (*.xlsb)|*.xlsb|" +
            "Excel 97-2003 Workbook (*.xls)|*.xls|" +
            "Excel Template (*.xltx)|*.xltx|" +
            "Excel Macro-Enabled Template (*.xltm)|*.xltm|" +
            "Excel 97-2003 Template (*.xlt)|*.xlt|" +
            "XML Spreadsheet 2003 (*.xml)|*.xml|" +
            "CSV (Comma Delimited) (*.csv)|*.csv|" +
            "Text (Tab Delimited) (*.txt)|*.txt|" +
            "All files (*.*)|*.*";
        const string saveFileDialogFilter =
             "Excel Workbook (*.xlsx)|*.xlsx|" +
             "Excel Macro-Enabled Workbook (*.xlsm)|*.xlsm|" +
             "Excel Binary Workbook (*.xlsb)|*.xlsb|" +
             "Excel 97-2003 Workbook (*.xls)|*.xls";

        readonly Workbook workbook;

        public SpreadsheetDocumentMerging() {
            InitializeComponent();
            workbook = new Workbook();
            workbook.LoadDocument(DemoUtils.GetRelativePath("AnnualSalesReport.xlsx"));
            spreadsheetPreview1.CanShowBorders = true;
            spreadsheetPreview1.Workbook = workbook;
            RefreshPreview();
        }

        private void RefreshPreview() {
            workbook.Worksheets[0].PrintOptions.FitToPage = true;
            spreadsheetPreview1.UpdatePreview();
        }

        private void Open_Click(object sender, EventArgs e) {
            using(OpenFileDialog openFileDialog = new OpenFileDialog()) {
                openFileDialog.Filter = openFileDialogFilter;
                if(openFileDialog.ShowDialog() == DialogResult.OK) {
                    try {
                        workbook.LoadDocument(openFileDialog.FileName);
                        RefreshPreview();
                    }
                    catch(Exception ex) {
                        XtraMessageBox.Show(ex.Message, Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void Append_Click(object sender, EventArgs e) {
            using(OpenFileDialog openFileDialog = new OpenFileDialog()) {
                openFileDialog.Filter = openFileDialogFilter;
                if(openFileDialog.ShowDialog() == DialogResult.OK) {
                    try {
                        using(Workbook source = new Workbook()) {
                            source.LoadDocument(openFileDialog.FileName);
                            workbook.Append(source);
                        }
                        workbook.Calculate();
                        RefreshPreview();
                    }
                    catch(Exception ex) {
                        XtraMessageBox.Show(ex.Message, Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void SaveAs_Click(object sender, EventArgs e) {
            string fileName = DemoUtils.GetSaveFileName(saveFileDialogFilter, "Document");
            if(!string.IsNullOrEmpty(fileName)) {
                try {
                    workbook.SaveDocument(fileName);
                    DemoUtils.ShowFile(fileName, this);
                }
                catch(Exception ex) {
                    XtraMessageBox.Show(ex.Message, Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
    }
}
