﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <RestoreSuccess Condition=" '$(RestoreSuccess)' == '' ">True</RestoreSuccess>
    <RestoreTool Condition=" '$(RestoreTool)' == '' ">NuGet</RestoreTool>
    <ProjectAssetsFile Condition=" '$(ProjectAssetsFile)' == '' ">$(MSBuildThisFileDirectory)project.assets.json</ProjectAssetsFile>
    <NuGetPackageRoot Condition=" '$(NuGetPackageRoot)' == '' ">$(UserProfile)\.nuget\packages\</NuGetPackageRoot>
    <NuGetPackageFolders Condition=" '$(NuGetPackageFolders)' == '' ">C:\Users\<USER>\.nuget\packages\;C:\Program Files\DevExpress 25.1\Components\Offline Packages;C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages</NuGetPackageFolders>
    <NuGetProjectStyle Condition=" '$(NuGetProjectStyle)' == '' ">PackageReference</NuGetProjectStyle>
    <NuGetToolVersion Condition=" '$(NuGetToolVersion)' == '' ">6.14.0</NuGetToolVersion>
  </PropertyGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <SourceRoot Include="C:\Users\<USER>\.nuget\packages\" />
    <SourceRoot Include="C:\Program Files\DevExpress 25.1\Components\Offline Packages\" />
    <SourceRoot Include="C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages\" />
  </ItemGroup>
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)devexpress.data.desktop\25.1.3\build\net8.0-windows\DevExpress.Data.Desktop.props" Condition="Exists('$(NuGetPackageRoot)devexpress.data.desktop\25.1.3\build\net8.0-windows\DevExpress.Data.Desktop.props')" />
    <Import Project="C:\Program Files\DevExpress 25.1\Components\Offline Packages\devexpress.diagram.core\25.1.3\build\net8.0-windows\DevExpress.Diagram.Core.props" Condition="Exists('C:\Program Files\DevExpress 25.1\Components\Offline Packages\devexpress.diagram.core\25.1.3\build\net8.0-windows\DevExpress.Diagram.Core.props')" />
  </ImportGroup>
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <PkgDevExpress_Data Condition=" '$(PkgDevExpress_Data)' == '' ">C:\Users\<USER>\.nuget\packages\devexpress.data\25.1.3</PkgDevExpress_Data>
    <PkgDevExpress_Xpo Condition=" '$(PkgDevExpress_Xpo)' == '' ">C:\Users\<USER>\.nuget\packages\devexpress.xpo\25.1.3</PkgDevExpress_Xpo>
    <PkgDevExpress_Utils Condition=" '$(PkgDevExpress_Utils)' == '' ">C:\Users\<USER>\.nuget\packages\devexpress.utils\25.1.3</PkgDevExpress_Utils>
    <PkgDevExpress_Win_Navigation Condition=" '$(PkgDevExpress_Win_Navigation)' == '' ">C:\Users\<USER>\.nuget\packages\devexpress.win.navigation\25.1.3</PkgDevExpress_Win_Navigation>
    <PkgDevExpress_Win_TreeList Condition=" '$(PkgDevExpress_Win_TreeList)' == '' ">C:\Users\<USER>\.nuget\packages\devexpress.win.treelist\25.1.3</PkgDevExpress_Win_TreeList>
    <PkgDevExpress_Win_Printing Condition=" '$(PkgDevExpress_Win_Printing)' == '' ">C:\Program Files\DevExpress 25.1\Components\Offline Packages\devexpress.win.printing\25.1.3</PkgDevExpress_Win_Printing>
    <PkgDevExpress_Win_VerticalGrid Condition=" '$(PkgDevExpress_Win_VerticalGrid)' == '' ">C:\Program Files\DevExpress 25.1\Components\Offline Packages\devexpress.win.verticalgrid\25.1.3</PkgDevExpress_Win_VerticalGrid>
    <PkgDevExpress_Win_TreeMap Condition=" '$(PkgDevExpress_Win_TreeMap)' == '' ">C:\Program Files\DevExpress 25.1\Components\Offline Packages\devexpress.win.treemap\25.1.3</PkgDevExpress_Win_TreeMap>
    <PkgDevExpress_Win_Grid Condition=" '$(PkgDevExpress_Win_Grid)' == '' ">C:\Program Files\DevExpress 25.1\Components\Offline Packages\devexpress.win.grid\25.1.3</PkgDevExpress_Win_Grid>
    <PkgDevExpress_Win_RichEdit Condition=" '$(PkgDevExpress_Win_RichEdit)' == '' ">C:\Program Files\DevExpress 25.1\Components\Offline Packages\devexpress.win.richedit\25.1.3</PkgDevExpress_Win_RichEdit>
    <PkgDevExpress_Win_PivotGrid Condition=" '$(PkgDevExpress_Win_PivotGrid)' == '' ">C:\Program Files\DevExpress 25.1\Components\Offline Packages\devexpress.win.pivotgrid\25.1.3</PkgDevExpress_Win_PivotGrid>
    <PkgDevExpress_Win_Diagram Condition=" '$(PkgDevExpress_Win_Diagram)' == '' ">C:\Program Files\DevExpress 25.1\Components\Offline Packages\devexpress.win.diagram\25.1.3</PkgDevExpress_Win_Diagram>
    <PkgDevExpress_Win Condition=" '$(PkgDevExpress_Win)' == '' ">C:\Program Files\DevExpress 25.1\Components\Offline Packages\devexpress.win\25.1.3</PkgDevExpress_Win>
    <PkgDevExpress_DataAccess Condition=" '$(PkgDevExpress_DataAccess)' == '' ">C:\Users\<USER>\.nuget\packages\devexpress.dataaccess\25.1.3</PkgDevExpress_DataAccess>
    <PkgDevExpress_DataAccess_UI Condition=" '$(PkgDevExpress_DataAccess_UI)' == '' ">C:\Program Files\DevExpress 25.1\Components\Offline Packages\devexpress.dataaccess.ui\25.1.3</PkgDevExpress_DataAccess_UI>
    <PkgDevExpress_Win_Spreadsheet Condition=" '$(PkgDevExpress_Win_Spreadsheet)' == '' ">C:\Program Files\DevExpress 25.1\Components\Offline Packages\devexpress.win.spreadsheet\25.1.3</PkgDevExpress_Win_Spreadsheet>
    <PkgDevExpress_Win_SpellChecker Condition=" '$(PkgDevExpress_Win_SpellChecker)' == '' ">C:\Program Files\DevExpress 25.1\Components\Offline Packages\devexpress.win.spellchecker\25.1.3</PkgDevExpress_Win_SpellChecker>
    <PkgDevExpress_Win_Scheduler Condition=" '$(PkgDevExpress_Win_Scheduler)' == '' ">C:\Program Files\DevExpress 25.1\Components\Offline Packages\devexpress.win.scheduler\25.1.3</PkgDevExpress_Win_Scheduler>
    <PkgDevExpress_Win_SchedulerReporting Condition=" '$(PkgDevExpress_Win_SchedulerReporting)' == '' ">C:\Program Files\DevExpress 25.1\Components\Offline Packages\devexpress.win.schedulerreporting\25.1.3</PkgDevExpress_Win_SchedulerReporting>
    <PkgDevExpress_Win_Charts Condition=" '$(PkgDevExpress_Win_Charts)' == '' ">C:\Program Files\DevExpress 25.1\Components\Offline Packages\devexpress.win.charts\25.1.3</PkgDevExpress_Win_Charts>
    <PkgDevExpress_Win_Reporting Condition=" '$(PkgDevExpress_Win_Reporting)' == '' ">C:\Program Files\DevExpress 25.1\Components\Offline Packages\devexpress.win.reporting\25.1.3</PkgDevExpress_Win_Reporting>
    <PkgDevExpress_Win_SchedulerExtensions Condition=" '$(PkgDevExpress_Win_SchedulerExtensions)' == '' ">C:\Program Files\DevExpress 25.1\Components\Offline Packages\devexpress.win.schedulerextensions\25.1.3</PkgDevExpress_Win_SchedulerExtensions>
    <PkgDevExpress_Win_PdfViewer Condition=" '$(PkgDevExpress_Win_PdfViewer)' == '' ">C:\Program Files\DevExpress 25.1\Components\Offline Packages\devexpress.win.pdfviewer\25.1.3</PkgDevExpress_Win_PdfViewer>
    <PkgDevExpress_Win_Map Condition=" '$(PkgDevExpress_Win_Map)' == '' ">C:\Program Files\DevExpress 25.1\Components\Offline Packages\devexpress.win.map\25.1.3</PkgDevExpress_Win_Map>
    <PkgDevExpress_Win_Gauges Condition=" '$(PkgDevExpress_Win_Gauges)' == '' ">C:\Program Files\DevExpress 25.1\Components\Offline Packages\devexpress.win.gauges\25.1.3</PkgDevExpress_Win_Gauges>
    <PkgDevExpress_Win_Gantt Condition=" '$(PkgDevExpress_Win_Gantt)' == '' ">C:\Program Files\DevExpress 25.1\Components\Offline Packages\devexpress.win.gantt\25.1.3</PkgDevExpress_Win_Gantt>
    <PkgDevExpress_Win_Dialogs Condition=" '$(PkgDevExpress_Win_Dialogs)' == '' ">C:\Program Files\DevExpress 25.1\Components\Offline Packages\devexpress.win.dialogs\25.1.3</PkgDevExpress_Win_Dialogs>
  </PropertyGroup>
</Project>