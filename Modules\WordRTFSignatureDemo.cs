﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Windows.Forms;
using DevExpress.Office.DigitalSignatures;
using DevExpress.Office.Services;
using DevExpress.XtraEditors;
using DevExpress.XtraRichEdit;
using DevExpress.XtraRichEdit.Localization;
using DevExpress.XtraSpreadsheet.Services;

namespace DevExpress.Docs.Demos {
    public partial class WordRTFSignatureDemo : SignatureTutorialControl {
        public WordRTFSignatureDemo() {
            OfficeCharts.Instance.ActivateCrossPlatformCharts();
            InitializeComponent();
            PopulateHashAlgorithms(hashAlgorithmComboBox);
            PopulateCertificates(panelOptions, lbCerts);
            PopulateCommitmentTypes(edtReason);
            LoadDocument();
        }
        public override bool NoGap {
            get { return true; }
        }
        void LoadDocument() {
            richEditControl.LoadDocument(DemoUtils.GetRelativePath("Scientific.docx"));
        }
        void OnButtonSignClick(object sender, EventArgs e) {
            if(!IsSigningPossible()) {
                XtraMessageBox.Show("Before you sign a document, you must save it in DOCX, DOC, or ODT format.", "Signature", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            try {
                string saveFileName = ShowSignAndSaveFileDialog();
                if(!string.IsNullOrEmpty(saveFileName)) {
                    string fileName = richEditControl.Options.DocumentSaveOptions.CurrentFileName;
                    SignAndOpen(saveFileName, fileName, CreateSignatureOptions(), CreateSignatureInfo());
                }
            }
            catch(Exception exception) {
                XtraMessageBox.Show(exception.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        SignatureOptions CreateSignatureOptions() {
            var certificate = ((CertificateItem)lbCerts.SelectedItem);
            var digestMethod = (HashAlgorithmType)hashAlgorithmComboBox.SelectedItem;
            return CreateSignatureOptions(certificate, tsaURITextEdit.Text, digestMethod);
        }
        SignatureInfo CreateSignatureInfo() {
            SignatureInfo signatureInfo = new SignatureInfo();
            signatureInfo.CommitmentType = (CommitmentType)edtReason.EditValue;
            signatureInfo.Time = DateTime.UtcNow;
            signatureInfo.ClaimedRoles.Clear();
            signatureInfo.ClaimedRoles.Add(edtRole.Text);
            signatureInfo.Country = edtCountry.Text;
            signatureInfo.City = edtCity.Text;
            signatureInfo.StateOrProvince = edtState.Text;
            signatureInfo.Address1 = edtAddress1.Text;
            signatureInfo.Address2 = edtAddress2.Text;
            signatureInfo.PostalCode = edtPostalCode.Text;
            signatureInfo.Comments = edtComments.Text;
            return signatureInfo;
        }
        void OnButtonNewCertificateClick(object sender, EventArgs e) {
            RegisterCertificate(lbCerts);
        }
        static readonly Dictionary<DocumentFormat, string> filters = CreateFilterStringTable();
        protected override string CreateFilterString() {
            string filter = string.Empty;
            if(!filters.TryGetValue(richEditControl.Options.DocumentSaveOptions.CurrentFormat, out filter))
                return string.Empty;
            return filter;
        }
        static Dictionary<DocumentFormat, string> CreateFilterStringTable() {
            Dictionary<DocumentFormat, string> result = new Dictionary<DocumentFormat, string>();
            result.Add(DocumentFormat.OpenXml, XtraRichEditLocalizer.GetString(XtraRichEditStringId.FileFilterDescription_OpenXmlFiles) + " (*.docx)|*.docx");
            result.Add(DocumentFormat.Doc, XtraRichEditLocalizer.GetString(XtraRichEditStringId.FileFilterDescription_DocFiles) + " (*.doc)|*.doc");
            result.Add(DocumentFormat.OpenDocument, XtraRichEditLocalizer.GetString(XtraRichEditStringId.FileFilterDescription_OpenDocumentFiles) + " (*.odt)|*.odt");
            result.Add(DocumentFormat.Docm, XtraRichEditLocalizer.GetString(XtraRichEditStringId.FileFilterDescription_DocmFiles) + " (*.docm)|*.docm");
            result.Add(DocumentFormat.Dotx, XtraRichEditLocalizer.GetString(XtraRichEditStringId.FileFilterDescription_DotxFiles) + " (*.dotx)|*.dotx");
            result.Add(DocumentFormat.Dotm, XtraRichEditLocalizer.GetString(XtraRichEditStringId.FileFilterDescription_DotmFiles) + " (*.dotm)|*.dotm");
            result.Add(DocumentFormat.Dot, XtraRichEditLocalizer.GetString(XtraRichEditStringId.FileFilterDescription_DotFiles) + " (*.dot)|*.dot");
            return result;
        }
        protected override string GetFileNameForSaving() {
            DocumentSaveOptions options = richEditControl.Options.DocumentSaveOptions;
            string fileName = options.CurrentFileName;
            if(string.IsNullOrEmpty(fileName))
                fileName = options.DefaultFileName;
            return Path.GetFileNameWithoutExtension(fileName) + "-signed" + Path.GetExtension(fileName);
        }
        static readonly HashSet<DocumentFormat> formatsForSigning = CreateFormatsForSigningTable();
        static HashSet<DocumentFormat> CreateFormatsForSigningTable() {
            HashSet<DocumentFormat> result = new HashSet<DocumentFormat>();
            result.Add(DocumentFormat.Doc);
            result.Add(DocumentFormat.OpenDocument);
            result.Add(DocumentFormat.OpenXml);
            result.Add(DocumentFormat.Docm);
            result.Add(DocumentFormat.Dotm);
            result.Add(DocumentFormat.Dotx);
            result.Add(DocumentFormat.Dot);
            return result;
        }
        bool IsSigningPossible() {
            DocumentSaveOptions options = richEditControl.Options.DocumentSaveOptions;
            if(string.IsNullOrEmpty(options.CurrentFileName))
                return false;
            if(lbCerts.Items.Count <= 0)
                return false;
            return formatsForSigning.Contains(options.CurrentFormat);
        }
        protected override void Dispose(bool disposing) {
            if(disposing) {
                if(components != null)
                    components.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
