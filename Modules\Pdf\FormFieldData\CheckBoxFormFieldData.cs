﻿using System;
using System.ComponentModel;
using DevExpress.Pdf;

namespace DevExpress.Docs.Demos {
    public class CheckBoxFormFieldData : FormFieldData {
        string fExportValue = "Yes";
        PdfAcroFormButtonStyle buttonStyle;
        bool fIsChecked;
        bool fShouldGeneratePressedAppearance;

        [Category(AppearanceCategory)]
        public PdfAcroFormButtonStyle Style {
            get { return buttonStyle; }
            set {
                buttonStyle = value;
                UpdateModel();
            }
        }

        [Category(AppearanceCategory)]
        public bool IsChecked {
            get { return fIsChecked; }
            set {
                fIsChecked = value;
                UpdateModel();
            }
        }

        [Category(AppearanceCategory)]
        public bool ShouldGeneratePressedAppearance {
            get { return fShouldGeneratePressedAppearance; }
            set {
                fShouldGeneratePressedAppearance = value;
                UpdateModel();
            }
        }

        [Category(BehaviorCategory)]
        public string ExportValue {
            get { return fExportValue; }
            set {
                if(string.IsNullOrEmpty(value))
                    throw new ArgumentException("The export value can't be null or empty string.");
                fExportValue = value;
                UpdateModel();
            }
        }

        public CheckBoxFormFieldData(PdfDocumentPosition position, DocumentFormController controller)
            : base(position, controller) {
            Style = PdfAcroFormButtonStyle.Check;
            ShouldGeneratePressedAppearance = true;
        }
        protected override PdfAcroFormCommonVisualField CreateVisualFormField() {
            return new PdfAcroFormCheckBoxField(Name, PageNumber, Rectangle.InnerRectangle) {
                ButtonStyle = Style,
                IsChecked = IsChecked,
                ShouldGeneratePressedAppearance = ShouldGeneratePressedAppearance,
                ExportValue = ExportValue
            };
        }
    }
}
