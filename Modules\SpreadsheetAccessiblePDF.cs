﻿using System;
using System.IO;
using System.Windows.Forms;
using DevExpress.DXperience.Demos;
using DevExpress.Spreadsheet;
using DevExpress.XtraEditors;
using DevExpress.XtraPrinting;

namespace DevExpress.Docs.Demos {
    public partial class SpreadsheetAccessiblePDF : TutorialControlBase {
        const string openFileDialogFilter =
            "All Supported Files (*.xlsx;*.xlsm;*.xlsb;*.xls;*.xltx;*.xltm;*.xlt;*.xml)|*.xlsx;*.xlsm;*.xlsb;*.xls;*.xltx;*.xltm;*.xlt;*.xml|" +
            "Excel Workbook (*.xlsx)|*.xlsx|" +
            "Excel Macro-Enabled Workbook (*.xlsm)|*.xlsm|" +
            "Excel Binary Workbook (*.xlsb)|*.xlsb|" +
            "Excel 97-2003 Workbook (*.xls)|*.xls|" +
            "Excel Template (*.xltx)|*.xltx|" +
            "Excel Macro-Enabled Template (*.xltm)|*.xltm|" +
            "Excel 97-2003 Template (*.xlt)|*.xlt|" +
            "XML Spreadsheet 2003 (*.xml)|*.xml|" +
            "All files (*.*)|*.*";

        IWorkbook workbook;

        public SpreadsheetAccessiblePDF() {
            InitializeComponent();
            spreadsheetPreview1.CanShowBorders = true;
            edFilePath.Text = DemoUtils.GetRelativePath("BreakevenAnalysis.xlsx");
            edSaveTo.Text = DevExpress.Data.Utils.SafeEnvironment.MyDocuments;
            LoadDocument();
        }

        void LoadDocument() {
            if(workbook == null)
                workbook = new Workbook();
            try {
                workbook.LoadDocument(edFilePath.Text);
                workbook.Worksheets[0].PrintOptions.FitToPage = true;
                spreadsheetPreview1.Workbook = workbook;
                spreadsheetPreview1.UpdatePreview();
            }
            catch(Exception ex) {
                XtraMessageBox.Show(ex.Message, Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        void ConvertTo_Click(object sender, EventArgs e) {
            try {
                string format = ((Control)sender).Tag.ToString();
                string filePath = edFilePath.Text;
                string fileName = Path.GetFileNameWithoutExtension(filePath);
                string pathString = Path.Combine(edSaveTo.Text, fileName);
                string resultFilePath = pathString + ".pdf";
                PdfExportOptions exportOptions = new PdfExportOptions();
                if(format == "pdf/a-1a") {
                    exportOptions.PdfACompatibility = PdfACompatibility.PdfA1a;
                }
                else if(format == "pdf/a-2a") {
                    exportOptions.PdfACompatibility = PdfACompatibility.PdfA2a;
                }
                else if(format == "pdf/a-3a") {
                    exportOptions.PdfACompatibility = PdfACompatibility.PdfA3a;
                }
                else if(format == "pdf/ua") {
                    exportOptions.PdfUACompatibility = PdfUACompatibility.PdfUA1;
                }
                exportOptions.ConvertImagesToJpeg = false;
                workbook.ExportToPdf(resultFilePath, exportOptions);
                if(!string.IsNullOrEmpty(resultFilePath))
                    DemoUtils.PreviewDocument(resultFilePath);
            }
            catch(Exception ex) {
                XtraMessageBox.Show(ex.Message, Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        void edFilePath_ButtonClick(object sender, XtraEditors.Controls.ButtonPressedEventArgs e) {
            ChooseFileToOpen(String.Empty);
        }

        void edFilePath_KeyUp(object sender, KeyEventArgs e) {
            if(e.KeyCode == Keys.Enter) {
                FileInfo fileInfo = new FileInfo(edFilePath.Text);
                if(fileInfo.Exists)
                    LoadDocument();
                else
                    ChooseFileToOpen(edFilePath.Text);
            }
        }

        void edSaveTo_ButtonClick(object sender, XtraEditors.Controls.ButtonPressedEventArgs e) {
            using(FolderBrowserDialog folderBrowserDialog = new FolderBrowserDialog()) {
                if(folderBrowserDialog.ShowDialog() == DialogResult.OK)
                    edSaveTo.Text = folderBrowserDialog.SelectedPath;
            }
        }

        void ChooseFileToOpen(string initialPath) {
            using(OpenFileDialog openFileDialog = new OpenFileDialog()) {
                openFileDialog.Filter = openFileDialogFilter;
                if(!string.IsNullOrEmpty(initialPath))
                    openFileDialog.InitialDirectory = initialPath;
                if(openFileDialog.ShowDialog() == DialogResult.OK) {
                    edFilePath.Text = openFileDialog.FileName;
                    LoadDocument();
                }
            }
        }

    }
}
