﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Design;
using System.Windows.Forms;

namespace DevExpress.Docs.Demos {
    public class FormFieldFontEditor : UITypeEditor {
        public override UITypeEditorEditStyle GetEditStyle(ITypeDescriptorContext context) {
            return UITypeEditorEditStyle.Modal;
        }

        public override object EditValue(ITypeDescriptorContext context, IServiceProvider provider, object value) {
            using(FontDialog dialog = new FontDialog()) {
                dialog.ShowEffects = false;
                Font font = value as Font;
                if(font != null)
                    dialog.Font = font;
                if(dialog.ShowDialog() == DialogResult.OK)
                    return dialog.Font;
                return base.EditValue(context, provider, value);
            }
        }
    }
}
