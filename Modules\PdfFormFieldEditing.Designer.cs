﻿using System.IO;
namespace DevExpress.Docs.Demos
{
    partial class PdfFormFieldEditing {
		/// <summary> 
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary> 
		/// Clean up any resources being used.
		/// </summary>
		/// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
		protected override void Dispose(bool disposing)
		{
            if (disposing) {
                UnsubscribeFromEvents();
                if (pdfViewer != null)
                    pdfViewer.CloseDocument();
                if (components != null)
                    components.Dispose();
            }
			base.Dispose(disposing);
		}

		#region Component Designer generated code

		/// <summary> 
		/// Required method for Designer support - do not modify 
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            this.widgetPropertyGridControl = new DevExpress.XtraVerticalGrid.PropertyGridControl();
            this.formFieldPropertyGridControl = new DevExpress.XtraVerticalGrid.PropertyGridControl();
            this.pdfViewer = new DevExpress.XtraPdfViewer.PdfViewer();
            this.accordionContentContainer1 = new DevExpress.XtraBars.Navigation.AccordionContentContainer();
            this.accordionControlElement2 = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.sidePanel = new DevExpress.XtraEditors.SidePanel();
            this.sidePanelLayout = new DevExpress.Utils.Layout.TablePanel();
            this.openBtn = new DevExpress.XtraEditors.SimpleButton();
            this.saveAsBtn = new DevExpress.XtraEditors.SimpleButton();
            this.repositoryItemColorPickEdit1 = new DevExpress.Docs.Demos.RepositoryItemPdfRGBColorPickEdit();
            this.formFieldPropertiesGroupControl = new DevExpress.XtraEditors.GroupControl();
            this.widgetPropertiesGroupControl = new DevExpress.XtraEditors.GroupControl();
            ((System.ComponentModel.ISupportInitialize)(this.widgetPropertyGridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.formFieldPropertyGridControl)).BeginInit();
            this.sidePanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.sidePanelLayout)).BeginInit();
            this.sidePanelLayout.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemColorPickEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.formFieldPropertiesGroupControl)).BeginInit();
            this.formFieldPropertiesGroupControl.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.widgetPropertiesGroupControl)).BeginInit();
            this.widgetPropertiesGroupControl.SuspendLayout();
            this.SuspendLayout();
            // 
            // widgetPropertyGridControl
            // 
            this.widgetPropertyGridControl.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.widgetPropertyGridControl.Cursor = System.Windows.Forms.Cursors.Default;
            this.widgetPropertyGridControl.DefaultEditors.AddRange(new DevExpress.XtraVerticalGrid.Rows.DefaultEditor[] {
            new DevExpress.XtraVerticalGrid.Rows.DefaultEditor(typeof(DevExpress.Pdf.PdfRGBColor), this.repositoryItemColorPickEdit1)});
            this.widgetPropertyGridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.widgetPropertyGridControl.Location = new System.Drawing.Point(2, 23);
            this.widgetPropertyGridControl.Name = "widgetPropertyGridControl";
            this.widgetPropertyGridControl.OptionsView.AllowReadOnlyRowAppearance = DevExpress.Utils.DefaultBoolean.True;
            this.widgetPropertyGridControl.OptionsView.ShowRootCategories = false;
            this.widgetPropertyGridControl.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemColorPickEdit1});
            this.widgetPropertyGridControl.Size = new System.Drawing.Size(311, 208);
            this.widgetPropertyGridControl.TabIndex = 5;
            // 
            // formFieldPropertyGridControl
            // 
            this.formFieldPropertyGridControl.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.formFieldPropertyGridControl.Cursor = System.Windows.Forms.Cursors.Default;
            this.formFieldPropertyGridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.formFieldPropertyGridControl.Location = new System.Drawing.Point(2, 23);
            this.formFieldPropertyGridControl.Name = "formFieldPropertyGridControl";
            this.formFieldPropertyGridControl.OptionsView.AllowReadOnlyRowAppearance = DevExpress.Utils.DefaultBoolean.True;
            this.formFieldPropertyGridControl.OptionsView.ShowRootCategories = false;
            this.formFieldPropertyGridControl.Size = new System.Drawing.Size(311, 209);
            this.formFieldPropertyGridControl.TabIndex = 2;
            // 
            // pdfViewer
            // 
            this.pdfViewer.AllowDrop = true;
            this.pdfViewer.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pdfViewer.Location = new System.Drawing.Point(0, 0);
            this.pdfViewer.Name = "pdfViewer";
            this.pdfViewer.NavigationPanePageVisibility = DevExpress.XtraPdfViewer.PdfNavigationPanePageVisibility.None;
            this.pdfViewer.Size = new System.Drawing.Size(391, 564);
            this.pdfViewer.TabIndex = 0;
            this.pdfViewer.ZoomMode = DevExpress.XtraPdfViewer.PdfZoomMode.PageLevel;
            // 
            // accordionContentContainer1
            // 
            this.accordionContentContainer1.Appearance.BackColor = System.Drawing.SystemColors.Control;
            this.accordionContentContainer1.Appearance.Options.UseBackColor = true;
            this.accordionContentContainer1.Name = "accordionContentContainer1";
            this.accordionContentContainer1.Size = new System.Drawing.Size(216, 76);
            this.accordionContentContainer1.TabIndex = 1;
            // 
            // accordionControlElement2
            // 
            this.accordionControlElement2.ContentContainer = this.accordionContentContainer1;
            this.accordionControlElement2.Name = "accordionControlElement2";
            this.accordionControlElement2.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlElement2.Text = "Element2";
            // 
            // sidePanel
            // 
            this.sidePanel.Controls.Add(this.sidePanelLayout);
            this.sidePanel.Dock = System.Windows.Forms.DockStyle.Right;
            this.sidePanel.Location = new System.Drawing.Point(391, 0);
            this.sidePanel.MinimumSize = new System.Drawing.Size(200, 0);
            this.sidePanel.Name = "sidePanel";
            this.sidePanel.Size = new System.Drawing.Size(346, 564);
            this.sidePanel.TabIndex = 6;
            this.sidePanel.Text = "sidePanel1";
            // 
            // sidePanelLayout
            // 
            this.sidePanelLayout.AutoSize = true;
            this.sidePanelLayout.Columns.AddRange(new DevExpress.Utils.Layout.TablePanelColumn[] {
            new DevExpress.Utils.Layout.TablePanelColumn(DevExpress.Utils.Layout.TablePanelEntityStyle.AutoSize, 5F)});
            this.sidePanelLayout.Controls.Add(this.openBtn);
            this.sidePanelLayout.Controls.Add(this.saveAsBtn);
            this.sidePanelLayout.Controls.Add(this.widgetPropertiesGroupControl);
            this.sidePanelLayout.Controls.Add(this.formFieldPropertiesGroupControl);
            this.sidePanelLayout.Dock = System.Windows.Forms.DockStyle.Fill;
            this.sidePanelLayout.Location = new System.Drawing.Point(1, 0);
            this.sidePanelLayout.Name = "sidePanelLayout";
            this.sidePanelLayout.Padding = new System.Windows.Forms.Padding(12);
            this.sidePanelLayout.Rows.AddRange(new DevExpress.Utils.Layout.TablePanelRow[] {
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.AutoSize, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.AutoSize, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Relative, 50F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Relative, 50F)});
            this.sidePanelLayout.Size = new System.Drawing.Size(345, 564);
            this.sidePanelLayout.TabIndex = 10;
            // 
            // openBtn
            // 
            this.sidePanelLayout.SetColumn(this.openBtn, 0);
            this.openBtn.Dock = System.Windows.Forms.DockStyle.Fill;
            this.openBtn.Location = new System.Drawing.Point(15, 15);
            this.openBtn.Name = "openBtn";
            this.sidePanelLayout.SetRow(this.openBtn, 0);
            this.openBtn.Size = new System.Drawing.Size(315, 23);
            this.openBtn.TabIndex = 8;
            this.openBtn.Text = "Load Document...";
            this.openBtn.Click += new System.EventHandler(this.openBtn_Click);
            // 
            // saveAsBtn
            // 
            this.sidePanelLayout.SetColumn(this.saveAsBtn, 0);
            this.saveAsBtn.Dock = System.Windows.Forms.DockStyle.Fill;
            this.saveAsBtn.Location = new System.Drawing.Point(15, 44);
            this.saveAsBtn.Name = "saveAsBtn";
            this.sidePanelLayout.SetRow(this.saveAsBtn, 1);
            this.saveAsBtn.Size = new System.Drawing.Size(315, 26);
            this.saveAsBtn.TabIndex = 9;
            this.saveAsBtn.Text = "Save As...";
            this.saveAsBtn.Click += new System.EventHandler(this.saveAsBtn_Click);
            // 
            // repositoryItemColorPickEdit1
            // 
            this.repositoryItemColorPickEdit1.AutoHeight = false;
            this.repositoryItemColorPickEdit1.AutomaticColor = System.Drawing.Color.Black;
            this.repositoryItemColorPickEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemColorPickEdit1.Name = "repositoryItemColorPickEdit1";
            // 
            // formFieldPropertiesGroupControl
            // 
            this.sidePanelLayout.SetColumn(this.formFieldPropertiesGroupControl, 0);
            this.formFieldPropertiesGroupControl.Controls.Add(this.formFieldPropertyGridControl);
            this.formFieldPropertiesGroupControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.formFieldPropertiesGroupControl.Location = new System.Drawing.Point(15, 76);
            this.formFieldPropertiesGroupControl.Name = "formFieldPropertiesGroupControl";
            this.sidePanelLayout.SetRow(this.formFieldPropertiesGroupControl, 2);
            this.formFieldPropertiesGroupControl.Size = new System.Drawing.Size(315, 234);
            this.formFieldPropertiesGroupControl.TabIndex = 7;
            this.formFieldPropertiesGroupControl.Text = "Form Field Properties";
            // 
            // widgetPropertiesGroupControl
            // 
            this.sidePanelLayout.SetColumn(this.widgetPropertiesGroupControl, 0);
            this.widgetPropertiesGroupControl.Controls.Add(this.widgetPropertyGridControl);
            this.widgetPropertiesGroupControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.widgetPropertiesGroupControl.Location = new System.Drawing.Point(15, 316);
            this.widgetPropertiesGroupControl.Name = "widgetPropertiesGroupControl";
            this.sidePanelLayout.SetRow(this.widgetPropertiesGroupControl, 3);
            this.widgetPropertiesGroupControl.Size = new System.Drawing.Size(315, 233);
            this.widgetPropertiesGroupControl.TabIndex = 7;
            this.widgetPropertiesGroupControl.Text = "Widget Properties";
            // 
            // PdfFormFieldEditing
            // 
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.pdfViewer);
            this.Controls.Add(this.sidePanel);
            this.Margin = new System.Windows.Forms.Padding(0);
            this.Name = "PdfFormFieldEditing";
            this.Size = new System.Drawing.Size(737, 564);
            ((System.ComponentModel.ISupportInitialize)(this.widgetPropertyGridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.formFieldPropertyGridControl)).EndInit();
            this.sidePanel.ResumeLayout(false);
            this.sidePanel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.sidePanelLayout)).EndInit();
            this.sidePanelLayout.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemColorPickEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.formFieldPropertiesGroupControl)).EndInit();
            this.formFieldPropertiesGroupControl.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.widgetPropertiesGroupControl)).EndInit();
            this.widgetPropertiesGroupControl.ResumeLayout(false);
            this.ResumeLayout(false);

		}

		#endregion
        private XtraPdfViewer.PdfViewer pdfViewer;
        private XtraBars.Navigation.AccordionContentContainer accordionContentContainer1;
        private XtraBars.Navigation.AccordionControlElement accordionControlElement2;
        private XtraVerticalGrid.PropertyGridControl formFieldPropertyGridControl;
        private XtraVerticalGrid.PropertyGridControl widgetPropertyGridControl;
        private RepositoryItemPdfRGBColorPickEdit repositoryItemColorPickEdit1;
        private XtraEditors.SidePanel sidePanel;
        private XtraEditors.SimpleButton saveAsBtn;
        private XtraEditors.SimpleButton openBtn;
        private Utils.Layout.TablePanel sidePanelLayout;
        private XtraEditors.GroupControl widgetPropertiesGroupControl;
        private XtraEditors.GroupControl formFieldPropertiesGroupControl;
    }
}
