﻿using System;
using System.Drawing;
using System.Windows.Forms;
using DevExpress.DXperience.Demos;
using DevExpress.XtraEditors;
using DevExpress.XtraPrinting;
using DevExpress.XtraPrintingLinks;
using DevExpress.XtraRichEdit;
using DevExpress.XtraRichEdit.API.Native;

namespace DevExpress.Docs.Demos {
    public partial class WordRtfDocumentWatermark : TutorialControlBase {
        const string saveFileDialogFilter = "Portable Document Format (*.pdf)|*.pdf";
        static readonly string watermarkImagePath = DemoUtils.GetRelativePath("WatermarkImage.png");
        readonly string pathToFile = DemoUtils.GetRelativePath("Watermarks.docx");
        readonly PrintableComponentLinkBase link;
        readonly RichEditDocumentServer documentServer;
        Bitmap image;

        public WordRtfDocumentWatermark() {
            InitializeComponent();
            LoadImage();
            InitializeDemo();
            documentServer = new RichEditDocumentServer();
            printPreviewControl.PrintingSystem = new PrintingSystem();
            link = new PrintableComponentLinkBase(printPreviewControl.PrintingSystem);
            documentServer.LoadDocument(pathToFile);
            link.Component = documentServer;
            link.CreateDocument();
        }
        void InitializeDemo() {
            FillCmbboxWatermarkText();
            FillClredtTextColor();
            FillFntedtFontName();
            FillCmbboxFontSize();
        }
        void FillCmbboxWatermarkText() {
            cmbboxWatermarkText.Properties.Items.Add("SAMPLE");
            cmbboxWatermarkText.Properties.Items.Add("DRAFT");
            cmbboxWatermarkText.Properties.Items.Add("CONFIDENTIAL");
            cmbboxWatermarkText.Properties.Items.Add("DO NOT COPY");
            cmbboxWatermarkText.Properties.Items.Add("TOP SECRET");
            cmbboxWatermarkText.EditValue = cmbboxWatermarkText.Properties.Items[0];
        }
        void FillFntedtFontName() {
            fntedtFontName.EditValue = "Calibri";
        }
        void FillClredtTextColor() {
            clredtTextColor.EditValue = Color.Silver;
        }
        void FillCmbboxFontSize() {
            cmbboxFontSize.Properties.Items.Add("Auto");
            for(int i = 36; i <= 144; i += 4)
                cmbboxFontSize.Properties.Items.Add(i);
            cmbboxFontSize.EditValue = cmbboxFontSize.Properties.Items[0];
        }
        void LoadImage() {
            try {
                image = new Bitmap(watermarkImagePath);
            }
            catch {
                XtraMessageBox.Show(PdfFileHelper.DemoOpeningErrorMessage, "Error");
                Enabled = false;
            }
        }
        private void OnButtonExportToPdfClick(object sender, EventArgs e) {
            using(SaveFileDialog saveFileDialog = new SaveFileDialog()) {
                saveFileDialog.Filter = saveFileDialogFilter;
                saveFileDialog.FileName = "Watermarks.pdf";
                if(saveFileDialog.ShowDialog() == DialogResult.OK)
                    SaveDocumentToPdf(saveFileDialog.FileName);
            }
        }
        void SaveDocumentToPdf(string filePath) {
            try {
                documentServer.ExportToPdf(filePath);
                DemoUtils.PreviewDocument(filePath);
            }
            catch(Exception e) {
                XtraMessageBox.Show(e.Message, Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        TextWatermarkOptions GetTextWatermarkOptions() {
            TextWatermarkOptions result = new TextWatermarkOptions();
            result.Color = clredtTextColor.Color;
            string fontName = fntedtFontName.EditValue.ToString();
            if(!string.IsNullOrWhiteSpace(fontName))
                result.FontFamily = fontName;
            result.Layout = chckedtDiagonal.Checked ? WatermarkLayout.Diagonal : WatermarkLayout.Horizontal;
            result.Semitransparent = chckedtSemitransparent.Checked;
            result.FontSize = GetFontSize();
            return result;
        }
        int GetFontSize() {
            try {
                return Convert.ToInt32(cmbboxFontSize.EditValue.ToString());
            }
            catch {
                return 0;
            }
        }

        private void OnButtonSetImageWatermarkClick(object sender, EventArgs e) {
            try {
                ImageWatermarkOptions options = new ImageWatermarkOptions() { Washout = chckedtWashout.Checked };
                documentServer.Document.WatermarkManager.SetImage(image, options);
                LinkDocument();
            }
            catch(Exception exception) {
                XtraMessageBox.Show(exception.Message, Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        void LinkDocument() {
            link.Component = documentServer;
            link.CreateDocument();
        }

        private void OnButtonSetTextWatermarkClick(object sender, EventArgs e) {
            string text = cmbboxWatermarkText.EditValue.ToString();
            if(string.IsNullOrWhiteSpace(text))
                text = "Sample";
            try {
                TextWatermarkOptions options = GetTextWatermarkOptions();
                documentServer.Document.WatermarkManager.SetText(text, options);
                LinkDocument();
            }
            catch(Exception exception) {
                XtraMessageBox.Show(exception.Message, Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OnButtonDeleteWatermarkClick(object sender, EventArgs e) {
            try {
                documentServer.Document.WatermarkManager.Remove();
                LinkDocument();
            }
            catch(Exception exception) {
                XtraMessageBox.Show(exception.Message, Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
