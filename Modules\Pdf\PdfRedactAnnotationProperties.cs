﻿using System;
using System.ComponentModel;
using System.Drawing;
using DevExpress.Pdf;

namespace DevExpress.Docs.Demos {
    public class PdfRedactAnnotationProperties {
        [Category("Redaction Appearance")]
        public Color FillColor { get; set; }
        [Category("Redaction Appearance")]
        public string OverlayText { get; set; }
        [Category("Redaction Appearance")]
        public bool RepeatText { get; set; }
        [Category("Redaction Appearance")]
        public PdfTextJustification TextJustification { get; set; }
        [Category("Redaction Appearance")]
        public string FontName { get; set; }
        [Category("Redaction Appearance")]
        public bool FontBold { get; set; }
        [Category("Redaction Appearance")]
        public bool FontItalic { get; set; }
        [Category("Redaction Appearance")]
        public double FontSize { get; set; }
        [Category("Redaction Appearance")]
        public Color FontColor { get; set; }
        public PdfRedactAnnotationProperties() {
        }
    }
}
