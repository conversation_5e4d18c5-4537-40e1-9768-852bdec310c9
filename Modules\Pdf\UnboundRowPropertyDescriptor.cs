﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using DevExpress.Pdf;

namespace DevExpress.Docs.Demos {
    public class UnboundRowPropertyDescriptor : PropertyDescriptor {
        object value;
        readonly Type valueType;
        readonly PdfButtonFormFieldFacade facade;
        public UnboundRowPropertyDescriptor(string name, Type valueType, PdfButtonFormFieldFacade facade)
            : base(name, null) {
            this.valueType = valueType;
            this.facade = facade;
        }
        protected UnboundRowPropertyDescriptor(string name, Attribute[] attrs)
            : base(name, attrs) {
        }
        protected UnboundRowPropertyDescriptor(MemberDescriptor descr)
            : base(descr) {
        }
        protected UnboundRowPropertyDescriptor(MemberDescriptor descr, Attribute[] attrs)
            : base(descr, attrs) {
        }
        public object MyValue {
            get { return value; }
            set { this.@value = value; }
        }
        public override string Category {
            get { return string.Empty; }
        }
        public override bool CanResetValue(object component) {
            return false;
        }
        public override Type ComponentType {
            get { return valueType; }
        }
        public override object GetValue(object component) {
            return MyValue;
        }
        public override bool IsReadOnly {
            get { return false; }
        }
        public override Type PropertyType {
            get { return valueType; }
        }
        public override void ResetValue(object component) {
        }
        public override bool ShouldSerializeValue(object component) {
            return false;
        }
        public override void SetValue(object component, object value) {
            MyValue = value;
            Bitmap bmp = value as Bitmap;
            if(bmp != null) {
                using(MemoryStream stream = new MemoryStream()) {
                    bmp.Save(stream, ImageFormat.Png);
                    facade.Widgets[0].SetNormalIcon(stream.ToArray());
                }
            }
        }
    }
}
