﻿using System;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using DevExpress.DXperience.Demos;
using DevExpress.Pdf;
using DevExpress.XtraEditors;

namespace DevExpress.Docs.Demos {
    public partial class PdfADocumentConversion : TutorialControlBase {
        const string openFileDialogFilter =
            "PDF Files (*.pdf)|*.pdf|" +
            "All files (*.*)|*.*";
        readonly PdfDocumentConverterFileHelper fileHelper;
        string filePath;

        public PdfADocumentConversion() {
            InitializeComponent();
            filePath = DemoUtils.GetRelativePath("PdfAConversionDemo.pdf");
            fileHelper = new PdfDocumentConverterFileHelper(pdfViewer);
            FillTargetFileTypes();
            LoadDocument();
        }

        void FillTargetFileTypes() {
            cmbboxFileType.Properties.Items.Add("PDF/A-1b");
            cmbboxFileType.Properties.Items.Add("PDF/A-2b");
            cmbboxFileType.Properties.Items.Add("PDF/A-3b");
            cmbboxFileType.SelectedItem = "PDF/A-3b";
        }

        void LoadDocument() {
            try {
                fileHelper.LoadDocument(filePath);
                lblFormat.Text = PdfSCompatibilityToString(fileHelper.PdfACompatibility);
            }
            catch(Exception ex) {
                XtraMessageBox.Show(ex.Message, Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        void convertButton_Click(object sender, EventArgs e) {
            try {
                string format = cmbboxFileType.SelectedItem.ToString().ToLower();
                PdfCompatibility compatibility = GetPdfCompatibility(format);
                fileHelper.ConvertDocument(compatibility);
                if(fileHelper.ConversionReport != null) {
                    StringBuilder stringBuilder = new StringBuilder();
                    stringBuilder.AppendLine($"Status: {fileHelper.ConversionReport.ConversionStatus}");
                    if(fileHelper.ConversionReport.Issues.Any())
                        stringBuilder.AppendLine($"Issues:");
                    foreach(PdfConversionIssue issue in fileHelper.ConversionReport.Issues) {
                        stringBuilder.AppendLine($"{issue.Severity}: {issue.Message}");
                    }
                    memoEdit.Text = stringBuilder.ToString();
                }
            }
            catch(Exception ex) {
                XtraMessageBox.Show(ex.Message, Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        PdfCompatibility GetPdfCompatibility(string format) {
            switch(format) {
                case "pdf/a-1b": return PdfCompatibility.PdfA1b;
                case "pdf/a-2b": return PdfCompatibility.PdfA2b;
                case "pdf/a-3b": return PdfCompatibility.PdfA3b;
                default: throw new ArgumentOutOfRangeException("Unknown PDF format");
            }
        }

        string PdfSCompatibilityToString(PdfACompatibility compatibility) {
            switch(compatibility) {
                case PdfACompatibility.PdfA1a: return "PDF/A-1a";
                case PdfACompatibility.PdfA1b: return "PDF/A-1b";
                case PdfACompatibility.PdfA2a: return "PDF/A-2a";
                case PdfACompatibility.PdfA2b: return "PDF/A-2b";
                case PdfACompatibility.PdfA2u: return "PDF/A-2u";
                case PdfACompatibility.PdfA3a: return "PDF/A-3a";
                case PdfACompatibility.PdfA3b: return "PDF/A-3b";
                case PdfACompatibility.PdfA3u: return "PDF/A-3u";
                case PdfACompatibility.PdfA4: return "PDF/A-4";
                case PdfACompatibility.PdfA4e: return "PDF/A-4e";
                case PdfACompatibility.PdfA4f: return "PDF/A-4f";
                default: return "None";
            }
        }

        void buttonOpen_Click(object sender, EventArgs e) {
            ChooseFileToOpen(String.Empty);
        }

        void edFilePath_KeyUp(object sender, KeyEventArgs e) {
            if(e.KeyCode == Keys.Enter) {
                FileInfo fileInfo = new FileInfo(filePath);
                if(fileInfo.Exists)
                    LoadDocument();
                else
                    ChooseFileToOpen(filePath);
            }
        }

        void ChooseFileToOpen(string initialPath) {
            using(OpenFileDialog openFileDialog = new OpenFileDialog()) {
                openFileDialog.Filter = openFileDialogFilter;
                if(!string.IsNullOrEmpty(initialPath))
                    openFileDialog.InitialDirectory = initialPath;
                if(openFileDialog.ShowDialog() == DialogResult.OK) {
                    filePath = openFileDialog.FileName;
                    LoadDocument();
                    memoEdit.Clear();
                }
            }
        }
    }
}
