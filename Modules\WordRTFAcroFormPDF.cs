﻿using System;
using System.IO;
using System.Windows.Forms;
using DevExpress.DXperience.Demos;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraPrinting;
using DevExpress.XtraPrintingLinks;
using DevExpress.XtraRichEdit;

namespace DevExpress.Docs.Demos {
    public partial class WordRTFAcroFormPDF : TutorialControlBase {
        const string openFileDialogFilter =
            "Word Documents (*.docx)|*.docx|" +
            "All files (*.*)|*.*";

        readonly PrintableComponentLinkBase link;
        RichEditDocumentServer documentServer;
        public WordRTFAcroFormPDF() {
            InitializeComponent();
            printPreviewControl.PrintingSystem = new PrintingSystem();
            link = new PrintableComponentLinkBase(printPreviewControl.PrintingSystem);
            edtFilePath.Text = DemoUtils.GetRelativePath("AppointmentFormFilled.docx");
            edtSaveTo.Text = Data.Utils.SafeEnvironment.MyDocuments;
            LoadDocument();
        }
        void btnExport_Click(object sender, EventArgs e) {
            string filePath = edtFilePath.Text;
            string fileName = Path.GetFileNameWithoutExtension(filePath);
            string pathString = Path.Combine(edtSaveTo.Text, fileName);
            string resultFilePath = Path.GetFullPath(pathString) + ".pdf";
            PdfExportOptions exportOptions = new PdfExportOptions();
            exportOptions.ExportEditingFieldsToAcroForms = true;
            documentServer.ExportToPdf(resultFilePath, exportOptions);

            if(!string.IsNullOrEmpty(resultFilePath))
                DemoUtils.PreviewDocument(resultFilePath);
        }
        void LoadDocument() {
            if(documentServer == null) {
                documentServer = new RichEditDocumentServer();
                new RichEditDemoExceptionsHandler(documentServer).Install();
            }
            string filePath = edtFilePath.Text;
            documentServer.LoadDocument(filePath);
            link.Component = documentServer;
            link.CreateDocument();
        }
        void edtFilePath_ButtonClick(object sender, ButtonPressedEventArgs e) {
            ChooseFileToOpen(String.Empty);
        }
        void ChooseFileToOpen(string initialPath) {
            using(OpenFileDialog openFileDialog = new OpenFileDialog()) {
                openFileDialog.Filter = openFileDialogFilter;
                if(!String.IsNullOrEmpty(initialPath))
                    openFileDialog.InitialDirectory = initialPath;
                if(openFileDialog.ShowDialog() != DialogResult.OK)
                    return;
                edtFilePath.Text = openFileDialog.FileName;
                LoadDocument();
            }
        }
        void edtFilePath_KeyUp(object sender, KeyEventArgs e) {
            if(e.KeyCode != Keys.Enter)
                return;
            FileInfo fileInfo = new FileInfo(edtFilePath.Text);
            if(fileInfo.Exists) {
                LoadDocument();
                return;
            }
            ChooseFileToOpen(edtFilePath.Text);
        }
        void ChooseFolderToSave() {
            using(FolderBrowserDialog openFileDialog = new FolderBrowserDialog()) {
                if(openFileDialog.ShowDialog() != DialogResult.OK)
                    return;
                edtSaveTo.Text = openFileDialog.SelectedPath;
            }
        }
        void edtSaveTo_ButtonClick(object sender, ButtonPressedEventArgs e) {
            ChooseFolderToSave();
        }
    }
}
