using System;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace DevExpress.Docs.Demos {
    public partial class PinEntryForm : XtraForm {
        public string Pin { get; private set; }
        
        private TextEdit pinTextEdit;
        private SimpleButton okButton;
        private SimpleButton cancelButton;
        private LabelControl instructionLabel;
        
        public PinEntryForm() {
            InitializeComponent();
        }
        
        private void InitializeComponent() {
            this.pinTextEdit = new TextEdit();
            this.okButton = new SimpleButton();
            this.cancelButton = new SimpleButton();
            this.instructionLabel = new LabelControl();
            this.SuspendLayout();
            
            // instructionLabel
            this.instructionLabel.Location = new System.Drawing.Point(12, 12);
            this.instructionLabel.Name = "instructionLabel";
            this.instructionLabel.Size = new System.Drawing.Size(260, 16);
            this.instructionLabel.TabIndex = 0;
            this.instructionLabel.Text = "Please enter your USB token PIN:";
            
            // pinTextEdit
            this.pinTextEdit.Location = new System.Drawing.Point(12, 34);
            this.pinTextEdit.Name = "pinTextEdit";
            this.pinTextEdit.Properties.UseSystemPasswordChar = true;
            this.pinTextEdit.Size = new System.Drawing.Size(260, 22);
            this.pinTextEdit.TabIndex = 1;
            this.pinTextEdit.KeyPress += PinTextEdit_KeyPress;
            
            // okButton
            this.okButton.DialogResult = DialogResult.OK;
            this.okButton.Location = new System.Drawing.Point(116, 70);
            this.okButton.Name = "okButton";
            this.okButton.Size = new System.Drawing.Size(75, 23);
            this.okButton.TabIndex = 2;
            this.okButton.Text = "OK";
            this.okButton.Click += OkButton_Click;
            
            // cancelButton
            this.cancelButton.DialogResult = DialogResult.Cancel;
            this.cancelButton.Location = new System.Drawing.Point(197, 70);
            this.cancelButton.Name = "cancelButton";
            this.cancelButton.Size = new System.Drawing.Size(75, 23);
            this.cancelButton.TabIndex = 3;
            this.cancelButton.Text = "Cancel";
            
            // PinEntryForm
            this.AcceptButton = this.okButton;
            this.CancelButton = this.cancelButton;
            this.ClientSize = new System.Drawing.Size(284, 105);
            this.Controls.Add(this.cancelButton);
            this.Controls.Add(this.okButton);
            this.Controls.Add(this.pinTextEdit);
            this.Controls.Add(this.instructionLabel);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "PinEntryForm";
            this.ShowInTaskbar = false;
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "USB Token PIN";
            this.ResumeLayout(false);
        }
        
        private void PinTextEdit_KeyPress(object sender, KeyPressEventArgs e) {
            if (e.KeyChar == (char)Keys.Enter) {
                OkButton_Click(sender, e);
            }
        }
        
        private void OkButton_Click(object sender, EventArgs e) {
            Pin = pinTextEdit.Text;
            if (string.IsNullOrEmpty(Pin)) {
                XtraMessageBox.Show("Please enter a PIN.", "PIN Required", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                pinTextEdit.Focus();
                return;
            }
            this.DialogResult = DialogResult.OK;
            this.Close();
        }
        
        protected override void OnShown(EventArgs e) {
            base.OnShown(e);
            pinTextEdit.Focus();
        }
    }
}
