﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Windows.Forms;
using DevExpress.DXperience.Demos;
using DevExpress.XtraEditors;
using DevExpress.XtraPrinting;
using DevExpress.XtraPrintingLinks;
using DevExpress.XtraRichEdit;
using DevExpress.XtraRichEdit.API.Native;

namespace DevExpress.Docs.Demos {
    public partial class WordRTFDocumentEncryption : TutorialControlBase {
        const string saveFileDialogFilter =
            "Word Documents (*.docx)|*.docx|" +
            "Word 97-2003 Documents (*.doc)|*.doc";
        readonly Dictionary<string, DocumentFormat> fileExtensionToFormat = new Dictionary<string, DocumentFormat>();
        readonly string pathToFile = DemoUtils.GetRelativePath("DocumentForProtection.docx");
        readonly PrintableComponentLinkBase link;
        readonly RichEditDocumentServer documentServer;

        public WordRTFDocumentEncryption() {
            InitializeComponent();
            InitializeFileExtensionToFormat();
            documentServer = new RichEditDocumentServer();
            printPreviewControl.PrintingSystem = new PrintingSystem();
            link = new PrintableComponentLinkBase(printPreviewControl.PrintingSystem);
            documentServer.LoadDocument(pathToFile);
            link.Component = documentServer;
            link.CreateDocument();
            InitializeEncryptionOptions();
        }
        void InitializeEncryptionOptions() {
            edPasswordToOpen.Text = "test";

            foreach(EncryptionType currentValue in EnumHelper.GetValues<EncryptionType>())
                edEncryptionType.Properties.Items.Add(currentValue.ToString());
            edEncryptionType.SelectedItem = EncryptionType.Strong.ToString();
        }
        private void btnSave_Click(object sender, EventArgs e) {
            using(SaveFileDialog saveFileDialog = new SaveFileDialog()) {
                saveFileDialog.Filter = saveFileDialogFilter;
                saveFileDialog.FileName = "EncryptedDocument.docx";
                if(saveFileDialog.ShowDialog() == DialogResult.OK)
                    EncryptedAndSaveDocument(saveFileDialog.FileName);
            }
        }
        void EncryptedAndSaveDocument(string filePath) {
            try {
                using(RichEditDocumentServer server = new RichEditDocumentServer()) {
                    server.LoadDocument(pathToFile);
                    server.Document.Encryption.Password = edPasswordToOpen.Text;
                    server.Document.Encryption.Type = (EncryptionType)Enum.Parse(typeof(EncryptionType), edEncryptionType.Text);
                    string fileExtension = Path.GetExtension(filePath);
                    DocumentFormat format;
                    if(fileExtensionToFormat.TryGetValue(fileExtension, out format))
                        format = DocumentFormat.OpenXml;
                    server.SaveDocument(filePath, format);
                }
                DemoUtils.PreviewDocument(filePath);
            }
            catch(Exception e) {
                XtraMessageBox.Show(e.Message, Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        void InitializeFileExtensionToFormat() {
            fileExtensionToFormat.Add(".doc", DocumentFormat.Doc);
            fileExtensionToFormat.Add(".docx", DocumentFormat.OpenXml);
        }
        protected override void Dispose(bool disposing) {
            if(disposing) {
                if(components != null)
                    components.Dispose();
                if(documentServer != null)
                    documentServer.Dispose();
                if(link != null)
                    link.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
