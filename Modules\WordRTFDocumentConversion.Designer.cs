﻿namespace DevExpress.Docs.Demos {
    partial class WordRTFDocumentConversion {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing) {
            if (disposing && (components != null)) {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent() {
            this.printPreviewControl = new DevExpress.XtraPrinting.Control.PrintControl();
            this.sidePanel1 = new DevExpress.XtraEditors.SidePanel();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.btnTxt = new DevExpress.XtraEditors.SimpleButton();
            this.btnHtml = new DevExpress.XtraEditors.SimpleButton();
            this.btnMht = new DevExpress.XtraEditors.SimpleButton();
            this.btnDocx = new DevExpress.XtraEditors.SimpleButton();
            this.btnOdt = new DevExpress.XtraEditors.SimpleButton();
            this.btnXml = new DevExpress.XtraEditors.SimpleButton();
            this.btnPdf = new DevExpress.XtraEditors.SimpleButton();
            this.btnDoc = new DevExpress.XtraEditors.SimpleButton();
            this.btnEpub = new DevExpress.XtraEditors.SimpleButton();
            this.btnRtf = new DevExpress.XtraEditors.SimpleButton();
            this.edtSaveTo = new DevExpress.XtraEditors.ButtonEdit();
            this.edtFilePath = new DevExpress.XtraEditors.ButtonEdit();
            this.sidePanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edtSaveTo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtFilePath.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // printPreviewControl
            // 
            this.printPreviewControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.printPreviewControl.IsMetric = false;
            this.printPreviewControl.Location = new System.Drawing.Point(0, 81);
            this.printPreviewControl.Name = "printPreviewControl";
            this.printPreviewControl.Size = new System.Drawing.Size(784, 351);
            this.printPreviewControl.TabIndex = 0;
            // 
            // sidePanel1
            // 
            this.sidePanel1.AllowResize = false;
            this.sidePanel1.Controls.Add(this.labelControl2);
            this.sidePanel1.Controls.Add(this.labelControl3);
            this.sidePanel1.Controls.Add(this.labelControl1);
            this.sidePanel1.Controls.Add(this.btnTxt);
            this.sidePanel1.Controls.Add(this.btnHtml);
            this.sidePanel1.Controls.Add(this.btnMht);
            this.sidePanel1.Controls.Add(this.btnDocx);
            this.sidePanel1.Controls.Add(this.btnOdt);
            this.sidePanel1.Controls.Add(this.btnXml);
            this.sidePanel1.Controls.Add(this.btnPdf);
            this.sidePanel1.Controls.Add(this.btnDoc);
            this.sidePanel1.Controls.Add(this.btnEpub);
            this.sidePanel1.Controls.Add(this.btnRtf);
            this.sidePanel1.Controls.Add(this.edtSaveTo);
            this.sidePanel1.Controls.Add(this.edtFilePath);
            this.sidePanel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.sidePanel1.Location = new System.Drawing.Point(0, 0);
            this.sidePanel1.Name = "sidePanel1";
            this.sidePanel1.Size = new System.Drawing.Size(784, 81);
            this.sidePanel1.TabIndex = 1;
            this.sidePanel1.Text = "sidePanel1";
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(18, 47);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(56, 13);
            this.labelControl2.TabIndex = 74;
            this.labelControl2.Text = "Convert to:";
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(348, 19);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(41, 13);
            this.labelControl3.TabIndex = 75;
            this.labelControl3.Text = "Save to:";
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(18, 19);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(20, 13);
            this.labelControl1.TabIndex = 76;
            this.labelControl1.Text = "File:";
            // 
            // btnTxt
            // 
            this.btnTxt.Location = new System.Drawing.Point(305, 42);
            this.btnTxt.Name = "btnTxt";
            this.btnTxt.Size = new System.Drawing.Size(50, 23);
            this.btnTxt.TabIndex = 7;
            this.btnTxt.Tag = "txt";
            this.btnTxt.Text = "TXT";
            this.btnTxt.Click += new System.EventHandler(this.btnExport_Click);
            // 
            // btnHtml
            // 
            this.btnHtml.Location = new System.Drawing.Point(360, 42);
            this.btnHtml.Name = "btnHtml";
            this.btnHtml.Size = new System.Drawing.Size(50, 23);
            this.btnHtml.TabIndex = 8;
            this.btnHtml.Tag = "html";
            this.btnHtml.Text = "HTML";
            this.btnHtml.Click += new System.EventHandler(this.btnExport_Click);
            // 
            // btnMht
            // 
            this.btnMht.Location = new System.Drawing.Point(416, 42);
            this.btnMht.Name = "btnMht";
            this.btnMht.Size = new System.Drawing.Size(50, 23);
            this.btnMht.TabIndex = 9;
            this.btnMht.Tag = "mht";
            this.btnMht.Text = "MHT";
            this.btnMht.Click += new System.EventHandler(this.btnExport_Click);
            // 
            // btnDocx
            // 
            this.btnDocx.Location = new System.Drawing.Point(193, 42);
            this.btnDocx.Name = "btnDocx";
            this.btnDocx.Size = new System.Drawing.Size(50, 23);
            this.btnDocx.TabIndex = 6;
            this.btnDocx.Tag = "docx";
            this.btnDocx.Text = "DOCX";
            this.btnDocx.Click += new System.EventHandler(this.btnExport_Click);
            // 
            // btnOdt
            // 
            this.btnOdt.Location = new System.Drawing.Point(472, 42);
            this.btnOdt.Name = "btnOdt";
            this.btnOdt.Size = new System.Drawing.Size(50, 23);
            this.btnOdt.TabIndex = 10;
            this.btnOdt.Tag = "odt";
            this.btnOdt.Text = "ODT";
            this.btnOdt.Click += new System.EventHandler(this.btnExport_Click);
            // 
            // btnXml
            // 
            this.btnXml.Location = new System.Drawing.Point(528, 42);
            this.btnXml.Name = "btnXml";
            this.btnXml.Size = new System.Drawing.Size(50, 23);
            this.btnXml.TabIndex = 11;
            this.btnXml.Tag = "xml";
            this.btnXml.Text = "XML";
            this.btnXml.Click += new System.EventHandler(this.btnExport_Click);
            // 
            // btnPdf
            // 
            this.btnPdf.Location = new System.Drawing.Point(249, 42);
            this.btnPdf.Name = "btnPdf";
            this.btnPdf.Size = new System.Drawing.Size(50, 23);
            this.btnPdf.TabIndex = 7;
            this.btnPdf.Tag = "pdf";
            this.btnPdf.Text = "PDF";
            this.btnPdf.Click += new System.EventHandler(this.btnExport_Click);
            // 
            // btnDoc
            // 
            this.btnDoc.Location = new System.Drawing.Point(137, 42);
            this.btnDoc.Name = "btnDoc";
            this.btnDoc.Size = new System.Drawing.Size(50, 23);
            this.btnDoc.TabIndex = 5;
            this.btnDoc.Tag = "doc";
            this.btnDoc.Text = "DOC";
            this.btnDoc.Click += new System.EventHandler(this.btnExport_Click);
            // 
            // btnEpub
            // 
            this.btnEpub.Location = new System.Drawing.Point(584, 42);
            this.btnEpub.Name = "btnEpub";
            this.btnEpub.Size = new System.Drawing.Size(50, 23);
            this.btnEpub.TabIndex = 12;
            this.btnEpub.Tag = "epub";
            this.btnEpub.Text = "EPUB";
            this.btnEpub.Click += new System.EventHandler(this.btnExport_Click);
            // 
            // btnRtf
            // 
            this.btnRtf.Location = new System.Drawing.Point(81, 42);
            this.btnRtf.Name = "btnRtf";
            this.btnRtf.Size = new System.Drawing.Size(50, 23);
            this.btnRtf.TabIndex = 4;
            this.btnRtf.Tag = "rtf";
            this.btnRtf.Text = "RTF";
            this.btnRtf.Click += new System.EventHandler(this.btnExport_Click);
            // 
            // edtSaveTo
            // 
            this.edtSaveTo.Location = new System.Drawing.Point(395, 16);
            this.edtSaveTo.Name = "edtSaveTo";
            this.edtSaveTo.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.edtSaveTo.Size = new System.Drawing.Size(239, 20);
            this.edtSaveTo.TabIndex = 3;
            this.edtSaveTo.ButtonClick += new DevExpress.XtraEditors.Controls.ButtonPressedEventHandler(this.edtSaveTo_ButtonClick);
            // 
            // edtFilePath
            // 
            this.edtFilePath.Location = new System.Drawing.Point(81, 16);
            this.edtFilePath.Name = "edtFilePath";
            this.edtFilePath.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.edtFilePath.Size = new System.Drawing.Size(240, 20);
            this.edtFilePath.TabIndex = 2;
            this.edtFilePath.ButtonClick += new DevExpress.XtraEditors.Controls.ButtonPressedEventHandler(this.edtFilePath_ButtonClick);
            // 
            // WordRTFDocumentConversion
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.printPreviewControl);
            this.Controls.Add(this.sidePanel1);
            this.Name = "WordRTFDocumentConversion";
            this.sidePanel1.ResumeLayout(false);
            this.sidePanel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edtSaveTo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtFilePath.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private XtraPrinting.Control.PrintControl printPreviewControl;
        private XtraEditors.SidePanel sidePanel1;
        private XtraEditors.LabelControl labelControl2;
        private XtraEditors.LabelControl labelControl3;
        private XtraEditors.LabelControl labelControl1;
        private XtraEditors.SimpleButton btnTxt;
        private XtraEditors.SimpleButton btnHtml;
        private XtraEditors.SimpleButton btnMht;
        private XtraEditors.SimpleButton btnDocx;
        private XtraEditors.SimpleButton btnOdt;
        private XtraEditors.SimpleButton btnXml;
        private XtraEditors.SimpleButton btnPdf;
        private XtraEditors.SimpleButton btnDoc;
        private XtraEditors.SimpleButton btnEpub;
        private XtraEditors.SimpleButton btnRtf;
        private XtraEditors.ButtonEdit edtSaveTo;
        private XtraEditors.ButtonEdit edtFilePath;
    }
}
