﻿using System;
using DevExpress.DXperience.Demos;
using DevExpress.Pdf;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;

namespace DevExpress.Docs.Demos {
    public partial class PdfPasswordProtection : TutorialControlBase {
        static readonly string path = "PasswordProtection.pdf";

        public override bool NoGap { get { return true; } }

        public PdfPasswordProtection() {
            InitializeComponent();
            cbAlgorithm.Properties.Items.Add(new ImageComboBoxItem("ARC4", PdfEncryptionAlgorithm.ARC4, 0));
            cbAlgorithm.Properties.Items.Add(new ImageComboBoxItem("128-bit AES", PdfEncryptionAlgorithm.AES128, 0));
            cbAlgorithm.Properties.Items.Add(new ImageComboBoxItem("256-bit AES", PdfEncryptionAlgorithm.AES256, 0));
            cbAlgorithm.SelectedIndex = 1;
            cbPrinting.Properties.Items.Add(new ImageComboBoxItem("Not allowed", PdfDocumentPrintingPermissions.NotAllowed, 0));
            cbPrinting.Properties.Items.Add(new ImageComboBoxItem("Low quality", PdfDocumentPrintingPermissions.LowQuality, 0));
            cbPrinting.Properties.Items.Add(new ImageComboBoxItem("Allowed", PdfDocumentPrintingPermissions.Allowed, 0));
            cbPrinting.SelectedIndex = 2;
            cbDataExtraction.Properties.Items.Add(new ImageComboBoxItem("Not allowed", PdfDocumentDataExtractionPermissions.NotAllowed, 0));
            cbDataExtraction.Properties.Items.Add(new ImageComboBoxItem("Accessibility", PdfDocumentDataExtractionPermissions.Accessibility, 0));
            cbDataExtraction.Properties.Items.Add(new ImageComboBoxItem("Allowed", PdfDocumentDataExtractionPermissions.Allowed, 0));
            cbDataExtraction.SelectedIndex = 2;
            cbModifying.Properties.Items.Add(new ImageComboBoxItem("Not allowed", PdfDocumentModificationPermissions.NotAllowed, 0));
            cbModifying.Properties.Items.Add(new ImageComboBoxItem("Document assembling", PdfDocumentModificationPermissions.DocumentAssembling, 0));
            cbModifying.Properties.Items.Add(new ImageComboBoxItem("Allowed", PdfDocumentModificationPermissions.Allowed, 0));
            cbModifying.SelectedIndex = 2;
            cbInteractivity.Properties.Items.Add(new ImageComboBoxItem("Not allowed", PdfDocumentInteractivityPermissions.NotAllowed, 0));
            cbInteractivity.Properties.Items.Add(new ImageComboBoxItem("Form filling and signing", PdfDocumentInteractivityPermissions.FormFillingAndSigning, 0));
            cbInteractivity.Properties.Items.Add(new ImageComboBoxItem("Allowed", PdfDocumentInteractivityPermissions.Allowed, 0));
            cbInteractivity.SelectedIndex = 2;
            Enabled = new PdfViewerFileHelper(pdfViewer).LoadDemoDocument(path);
        }
        void OnProtectButtonClick(object sender, EventArgs e) {
            string ownerPassword = teOwnerPassword.Text;
            string userPassword = teUserPassword.Text;
            if(String.IsNullOrEmpty(ownerPassword) && String.IsNullOrEmpty(userPassword))
                XtraMessageBox.Show(this, "Please specify at least one password to protect the document.", "PDF Password Protection");
            else
                using(PdfDocumentProcessor documentProcessor = new PdfDocumentProcessor()) {
                    PdfDocumentProcessorFileHelper document = new PdfDocumentProcessorFileHelper(documentProcessor);
                    document.LoadDemoDocument(path);
                    PdfEncryptionOptions encryptionOptions = new PdfEncryptionOptions();
                    encryptionOptions.OwnerPasswordString = ownerPassword;
                    encryptionOptions.UserPasswordString = userPassword;
                    encryptionOptions.Algorithm = (PdfEncryptionAlgorithm)cbAlgorithm.EditValue;
                    encryptionOptions.PrintingPermissions = (PdfDocumentPrintingPermissions)cbPrinting.EditValue;
                    encryptionOptions.DataExtractionPermissions = (PdfDocumentDataExtractionPermissions)cbDataExtraction.EditValue;
                    encryptionOptions.ModificationPermissions = (PdfDocumentModificationPermissions)cbModifying.EditValue;
                    encryptionOptions.InteractivityPermissions = (PdfDocumentInteractivityPermissions)cbInteractivity.EditValue;
                    document.SaveDocument(new PdfSaveOptions() { EncryptionOptions = encryptionOptions });
                }
        }
    }
}
