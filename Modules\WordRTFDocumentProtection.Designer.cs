﻿namespace DevExpress.Docs.Demos {
    partial class WordRTFDocumentProtection {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent() {
            this.printPreviewControl = new DevExpress.XtraPrinting.Control.PrintControl();
            this.sidePanel1 = new DevExpress.XtraEditors.SidePanel();
            this.lblConfirmPassword = new DevExpress.XtraEditors.LabelControl();
            this.edConfirmPassword = new DevExpress.XtraEditors.TextEdit();
            this.btnProtectAndSave = new DevExpress.XtraEditors.SimpleButton();
            this.lblDocumentPassword = new DevExpress.XtraEditors.LabelControl();
            this.cbAllowComments = new DevExpress.XtraEditors.CheckEdit();
            this.cbReadOnly = new DevExpress.XtraEditors.CheckEdit();
            this.edDocumentPassword = new DevExpress.XtraEditors.TextEdit();
            this.sidePanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edConfirmPassword.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbAllowComments.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbReadOnly.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edDocumentPassword.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // printPreviewControl
            // 
            this.printPreviewControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.printPreviewControl.IsMetric = false;
            this.printPreviewControl.Location = new System.Drawing.Point(0, 0);
            this.printPreviewControl.Name = "printPreviewControl";
            this.printPreviewControl.Size = new System.Drawing.Size(577, 565);
            this.printPreviewControl.TabIndex = 35;
            // 
            // sidePanel1
            // 
            this.sidePanel1.AllowResize = false;
            this.sidePanel1.Controls.Add(this.lblConfirmPassword);
            this.sidePanel1.Controls.Add(this.edConfirmPassword);
            this.sidePanel1.Controls.Add(this.btnProtectAndSave);
            this.sidePanel1.Controls.Add(this.lblDocumentPassword);
            this.sidePanel1.Controls.Add(this.cbAllowComments);
            this.sidePanel1.Controls.Add(this.cbReadOnly);
            this.sidePanel1.Controls.Add(this.edDocumentPassword);
            this.sidePanel1.Dock = System.Windows.Forms.DockStyle.Right;
            this.sidePanel1.Location = new System.Drawing.Point(577, 0);
            this.sidePanel1.Name = "sidePanel1";
            this.sidePanel1.Size = new System.Drawing.Size(204, 565);
            this.sidePanel1.TabIndex = 76;
            this.sidePanel1.Text = "sidePanel1";
            // 
            // lblConfirmPassword
            // 
            this.lblConfirmPassword.Location = new System.Drawing.Point(18, 110);
            this.lblConfirmPassword.Name = "lblConfirmPassword";
            this.lblConfirmPassword.Size = new System.Drawing.Size(90, 13);
            this.lblConfirmPassword.TabIndex = 93;
            this.lblConfirmPassword.Text = "Confirm password:";
            // 
            // edConfirmPassword
            // 
            this.edConfirmPassword.Enabled = false;
            this.edConfirmPassword.Location = new System.Drawing.Point(18, 129);
            this.edConfirmPassword.Name = "edConfirmPassword";
            this.edConfirmPassword.Properties.MaxLength = 255;
            this.edConfirmPassword.Properties.PasswordChar = '*';
            this.edConfirmPassword.Size = new System.Drawing.Size(166, 20);
            this.edConfirmPassword.TabIndex = 92;
            this.edConfirmPassword.TextChanged += new System.EventHandler(this.edConfirmPassword_TextChanged);
            // 
            // btnProtectAndSave
            // 
            this.btnProtectAndSave.Location = new System.Drawing.Point(18, 160);
            this.btnProtectAndSave.Name = "btnProtectAndSave";
            this.btnProtectAndSave.Size = new System.Drawing.Size(166, 23);
            this.btnProtectAndSave.TabIndex = 91;
            this.btnProtectAndSave.Text = "Protect and Save...";
            this.btnProtectAndSave.Click += new System.EventHandler(this.btnProtectAndSave_Click);
            // 
            // lblDocumentPassword
            // 
            this.lblDocumentPassword.Location = new System.Drawing.Point(18, 65);
            this.lblDocumentPassword.Name = "lblDocumentPassword";
            this.lblDocumentPassword.Size = new System.Drawing.Size(99, 13);
            this.lblDocumentPassword.TabIndex = 89;
            this.lblDocumentPassword.Text = "Password (optional):";
            // 
            // cbAllowComments
            // 
            this.cbAllowComments.Location = new System.Drawing.Point(18, 40);
            this.cbAllowComments.Name = "cbAllowComments";
            this.cbAllowComments.Properties.Caption = "Allow Comments";
            this.cbAllowComments.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Radio;
            this.cbAllowComments.Properties.RadioGroupIndex = 0;
            this.cbAllowComments.Size = new System.Drawing.Size(99, 20);
            this.cbAllowComments.TabIndex = 88;
            this.cbAllowComments.TabStop = false;
            // 
            // cbReadOnly
            // 
            this.cbReadOnly.EditValue = true;
            this.cbReadOnly.Location = new System.Drawing.Point(18, 15);
            this.cbReadOnly.Name = "cbReadOnly";
            this.cbReadOnly.Properties.Caption = "Read Only";
            this.cbReadOnly.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Radio;
            this.cbReadOnly.Properties.RadioGroupIndex = 0;
            this.cbReadOnly.Size = new System.Drawing.Size(99, 20);
            this.cbReadOnly.TabIndex = 87;
            // 
            // edDocumentPassword
            // 
            this.edDocumentPassword.Location = new System.Drawing.Point(18, 84);
            this.edDocumentPassword.Name = "edDocumentPassword";
            this.edDocumentPassword.Properties.MaxLength = 255;
            this.edDocumentPassword.Properties.PasswordChar = '*';
            this.edDocumentPassword.Size = new System.Drawing.Size(166, 20);
            this.edDocumentPassword.TabIndex = 90;
            this.edDocumentPassword.TextChanged += new System.EventHandler(this.edDocumentPassword_TextChanged);
            // 
            // WordRTFDocumentProtection
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.printPreviewControl);
            this.Controls.Add(this.sidePanel1);
            this.Name = "WordRTFDocumentProtection";
            this.Size = new System.Drawing.Size(781, 565);
            this.sidePanel1.ResumeLayout(false);
            this.sidePanel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edConfirmPassword.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbAllowComments.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbReadOnly.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edDocumentPassword.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraPrinting.Control.PrintControl printPreviewControl;
        private XtraEditors.SidePanel sidePanel1;
        private XtraEditors.LabelControl lblConfirmPassword;
        private XtraEditors.TextEdit edConfirmPassword;
        protected XtraEditors.SimpleButton btnProtectAndSave;
        private XtraEditors.LabelControl lblDocumentPassword;
        protected XtraEditors.CheckEdit cbAllowComments;
        protected XtraEditors.CheckEdit cbReadOnly;
        private XtraEditors.TextEdit edDocumentPassword;
    }
}
