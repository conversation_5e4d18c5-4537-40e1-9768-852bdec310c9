﻿using System.IO;
namespace DevExpress.Docs.Demos
{
    partial class PdfFormCreation
	{
		/// <summary> 
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary> 
		/// Clean up any resources being used.
		/// </summary>
		/// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
		protected override void Dispose(bool disposing)
		{
            if (disposing) {
                UnsubscribeFromEvents();
                if (pdfViewer != null)
                    pdfViewer.CloseDocument();
                DemoUtils.DeleteTempFile(sourceTempFilePath);
                DemoUtils.DeleteTempFile(editingTempFilePath);
                if (components != null)
                    components.Dispose();
                if (controller != null)
                    controller.Dispose();
            }
			base.Dispose(disposing);
		}

		#region Component Designer generated code

		/// <summary> 
		/// Required method for Designer support - do not modify 
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(PdfFormCreation));
            DevExpress.Utils.SuperToolTip superToolTip1 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipTitleItem toolTipTitleItem1 = new DevExpress.Utils.ToolTipTitleItem();
            DevExpress.Utils.SuperToolTip superToolTip2 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipTitleItem toolTipTitleItem2 = new DevExpress.Utils.ToolTipTitleItem();
            this.propertyGridControl = new DevExpress.XtraVerticalGrid.PropertyGridControl();
            this.accordionControl = new DevExpress.XtraBars.Navigation.AccordionControl();
            this.accordionControlFileElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlOpenElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlSaveElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlRemoveElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlFormFieldsElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlTextFieldElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlComboBoxElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlListBoxElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlCheckBoxElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlSignatureElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.pdfViewer = new DevExpress.XtraPdfViewer.PdfViewer();
            this.accordionContentContainer1 = new DevExpress.XtraBars.Navigation.AccordionContentContainer();
            this.accordionControlElement2 = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.sidePanel = new DevExpress.XtraEditors.SidePanel();
            this.layoutControl1 = new DevExpress.XtraLayout.LayoutControl();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem1 = new DevExpress.XtraLayout.LayoutControlItem();
            this.splitterItem1 = new DevExpress.XtraLayout.SplitterItem();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.layoutControlItem3 = new DevExpress.XtraLayout.LayoutControlItem();
            ((System.ComponentModel.ISupportInitialize)(this.propertyGridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.accordionControl)).BeginInit();
            this.sidePanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl1)).BeginInit();
            this.layoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitterItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem3)).BeginInit();
            this.SuspendLayout();
            // 
            // propertyGridControl
            // 
            this.propertyGridControl.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.propertyGridControl.Cursor = System.Windows.Forms.Cursors.Default;
            this.propertyGridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.propertyGridControl.Location = new System.Drawing.Point(2, 23);
            this.propertyGridControl.Name = "propertyGridControl";
            this.propertyGridControl.OptionsView.AllowReadOnlyRowAppearance = DevExpress.Utils.DefaultBoolean.True;
            this.propertyGridControl.OptionsView.ShowRootCategories = false;
            this.propertyGridControl.Size = new System.Drawing.Size(360, 238);
            this.propertyGridControl.TabIndex = 2;
            // 
            // accordionControl
            // 
            this.accordionControl.AllowDrop = true;
            this.accordionControl.AllowItemSelection = true;
            this.accordionControl.Elements.AddRange(new DevExpress.XtraBars.Navigation.AccordionControlElement[] {
            this.accordionControlFileElement,
            this.accordionControlFormFieldsElement});
            this.accordionControl.Location = new System.Drawing.Point(12, 12);
            this.accordionControl.Margin = new System.Windows.Forms.Padding(9);
            this.accordionControl.Name = "accordionControl";
            this.accordionControl.ScrollBarMode = DevExpress.XtraBars.Navigation.ScrollBarMode.Hidden;
            this.accordionControl.Size = new System.Drawing.Size(364, 263);
            this.accordionControl.StyleController = this.layoutControl1;
            this.accordionControl.TabIndex = 1;
            // 
            // accordionControlFileElement
            // 
            this.accordionControlFileElement.Elements.AddRange(new DevExpress.XtraBars.Navigation.AccordionControlElement[] {
            this.accordionControlOpenElement,
            this.accordionControlSaveElement,
            this.accordionControlRemoveElement});
            this.accordionControlFileElement.Expanded = true;
            this.accordionControlFileElement.Name = "accordionControlFileElement";
            this.accordionControlFileElement.Text = "File";
            // 
            // accordionControlOpenElement
            // 
            this.accordionControlOpenElement.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("accordionControlOpenElement.ImageOptions.Image")));
            this.accordionControlOpenElement.Name = "accordionControlOpenElement";
            this.accordionControlOpenElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            toolTipTitleItem1.Text = "Open... (Ctrl+O)";
            superToolTip1.Items.Add(toolTipTitleItem1);
            this.accordionControlOpenElement.SuperTip = superToolTip1;
            this.accordionControlOpenElement.Text = "Open...";
            this.accordionControlOpenElement.Click += new System.EventHandler(this.AccordionControlOpenElementClick);
            // 
            // accordionControlSaveElement
            // 
            this.accordionControlSaveElement.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("accordionControlSaveElement.ImageOptions.Image")));
            this.accordionControlSaveElement.Name = "accordionControlSaveElement";
            this.accordionControlSaveElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            toolTipTitleItem2.Text = "Save as... (Ctrl+S)";
            superToolTip2.Items.Add(toolTipTitleItem2);
            this.accordionControlSaveElement.SuperTip = superToolTip2;
            this.accordionControlSaveElement.Text = "Save As...";
            this.accordionControlSaveElement.Click += new System.EventHandler(this.AccordionControlSaveElementClick);
            // 
            // accordionControlRemoveElement
            // 
            this.accordionControlRemoveElement.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("accordionControlRemoveElement.ImageOptions.Image")));
            this.accordionControlRemoveElement.Name = "accordionControlRemoveElement";
            this.accordionControlRemoveElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlRemoveElement.Text = "Remove Interactive Form";
            this.accordionControlRemoveElement.Click += new System.EventHandler(this.AccordionControlRemoveElementClick);
            // 
            // accordionControlFormFieldsElement
            // 
            this.accordionControlFormFieldsElement.Elements.AddRange(new DevExpress.XtraBars.Navigation.AccordionControlElement[] {
            this.accordionControlTextFieldElement,
            this.accordionControlComboBoxElement,
            this.accordionControlListBoxElement,
            this.accordionControlCheckBoxElement,
            this.accordionControlSignatureElement});
            this.accordionControlFormFieldsElement.Expanded = true;
            this.accordionControlFormFieldsElement.Name = "accordionControlFormFieldsElement";
            this.accordionControlFormFieldsElement.Text = "Form Fields";
            // 
            // accordionControlTextFieldElement
            // 
            this.accordionControlTextFieldElement.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("accordionControlTextFieldElement.ImageOptions.Image")));
            this.accordionControlTextFieldElement.Name = "accordionControlTextFieldElement";
            this.accordionControlTextFieldElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlTextFieldElement.Text = "TextBox Field";
            // 
            // accordionControlComboBoxElement
            // 
            this.accordionControlComboBoxElement.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("accordionControlComboBoxElement.ImageOptions.Image")));
            this.accordionControlComboBoxElement.Name = "accordionControlComboBoxElement";
            this.accordionControlComboBoxElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlComboBoxElement.Text = "ComboBox Field";
            // 
            // accordionControlListBoxElement
            // 
            this.accordionControlListBoxElement.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("accordionControlListBoxElement.ImageOptions.Image")));
            this.accordionControlListBoxElement.Name = "accordionControlListBoxElement";
            this.accordionControlListBoxElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlListBoxElement.Text = "ListBox Field";
            // 
            // accordionControlCheckBoxElement
            // 
            this.accordionControlCheckBoxElement.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("accordionControlCheckBoxElement.ImageOptions.Image")));
            this.accordionControlCheckBoxElement.Name = "accordionControlCheckBoxElement";
            this.accordionControlCheckBoxElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlCheckBoxElement.Text = "CheckBox Field";
            // 
            // accordionControlSignatureElement
            // 
            this.accordionControlSignatureElement.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("accordionControlSignatureElement.ImageOptions.Image")));
            this.accordionControlSignatureElement.Name = "accordionControlSignatureElement";
            this.accordionControlSignatureElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlSignatureElement.Text = "Signature Field";
            // 
            // pdfViewer
            // 
            this.pdfViewer.AllowDrop = true;
            this.pdfViewer.CursorMode = DevExpress.XtraPdfViewer.PdfCursorMode.Custom;
            this.pdfViewer.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pdfViewer.Location = new System.Drawing.Point(0, 0);
            this.pdfViewer.Margin = new System.Windows.Forms.Padding(0);
            this.pdfViewer.Name = "pdfViewer";
            this.pdfViewer.NavigationPanePageVisibility = DevExpress.XtraPdfViewer.PdfNavigationPanePageVisibility.None;
            this.pdfViewer.Size = new System.Drawing.Size(348, 564);
            this.pdfViewer.TabIndex = 0;
            this.pdfViewer.ZoomMode = DevExpress.XtraPdfViewer.PdfZoomMode.PageLevel;
            // 
            // accordionContentContainer1
            // 
            this.accordionContentContainer1.Appearance.BackColor = System.Drawing.SystemColors.Control;
            this.accordionContentContainer1.Appearance.Options.UseBackColor = true;
            this.accordionContentContainer1.Name = "accordionContentContainer1";
            this.accordionContentContainer1.Size = new System.Drawing.Size(216, 76);
            this.accordionContentContainer1.TabIndex = 1;
            // 
            // accordionControlElement2
            // 
            this.accordionControlElement2.ContentContainer = this.accordionContentContainer1;
            this.accordionControlElement2.Name = "accordionControlElement2";
            this.accordionControlElement2.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlElement2.Text = "Element2";
            // 
            // sidePanel
            // 
            this.sidePanel.Controls.Add(this.layoutControl1);
            this.sidePanel.Dock = System.Windows.Forms.DockStyle.Right;
            this.sidePanel.Location = new System.Drawing.Point(348, 0);
            this.sidePanel.MinimumSize = new System.Drawing.Size(200, 0);
            this.sidePanel.Name = "sidePanel";
            this.sidePanel.Size = new System.Drawing.Size(389, 564);
            this.sidePanel.TabIndex = 1;
            this.sidePanel.Text = "sidePanel1";
            // 
            // layoutControl1
            // 
            this.layoutControl1.Controls.Add(this.groupControl1);
            this.layoutControl1.Controls.Add(this.accordionControl);
            this.layoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.layoutControl1.Location = new System.Drawing.Point(1, 0);
            this.layoutControl1.Name = "layoutControl1";
            this.layoutControl1.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = new System.Drawing.Rectangle(1058, 279, 650, 400);
            this.layoutControl1.Root = this.Root;
            this.layoutControl1.Size = new System.Drawing.Size(388, 564);
            this.layoutControl1.TabIndex = 2;
            this.layoutControl1.Text = "layoutControl1";
            // 
            // Root
            // 
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem1,
            this.splitterItem1,
            this.layoutControlItem3});
            this.Root.Name = "Root";
            this.Root.Size = new System.Drawing.Size(388, 564);
            this.Root.TextVisible = false;
            // 
            // layoutControlItem1
            // 
            this.layoutControlItem1.Control = this.accordionControl;
            this.layoutControlItem1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem1.Name = "layoutControlItem1";
            this.layoutControlItem1.Size = new System.Drawing.Size(368, 267);
            this.layoutControlItem1.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem1.TextVisible = false;
            // 
            // splitterItem1
            // 
            this.splitterItem1.AllowHotTrack = true;
            this.splitterItem1.Location = new System.Drawing.Point(0, 267);
            this.splitterItem1.Name = "splitterItem1";
            this.splitterItem1.ShowSplitGlyph = DevExpress.Utils.DefaultBoolean.True;
            this.splitterItem1.Size = new System.Drawing.Size(368, 10);
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.propertyGridControl);
            this.groupControl1.Location = new System.Drawing.Point(12, 289);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(364, 263);
            this.groupControl1.TabIndex = 4;
            this.groupControl1.Text = "Form Field Properties";
            // 
            // layoutControlItem3
            // 
            this.layoutControlItem3.Control = this.groupControl1;
            this.layoutControlItem3.Location = new System.Drawing.Point(0, 277);
            this.layoutControlItem3.Name = "layoutControlItem3";
            this.layoutControlItem3.Size = new System.Drawing.Size(368, 267);
            this.layoutControlItem3.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem3.TextVisible = false;
            // 
            // PdfFormCreation
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.pdfViewer);
            this.Controls.Add(this.sidePanel);
            this.Margin = new System.Windows.Forms.Padding(0);
            this.Name = "PdfFormCreation";
            this.Size = new System.Drawing.Size(737, 564);
            ((System.ComponentModel.ISupportInitialize)(this.propertyGridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.accordionControl)).EndInit();
            this.sidePanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl1)).EndInit();
            this.layoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitterItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem3)).EndInit();
            this.ResumeLayout(false);

		}

		#endregion
        private XtraBars.Navigation.AccordionControl accordionControl;
        private XtraBars.Navigation.AccordionControlElement accordionControlFileElement;
        private XtraPdfViewer.PdfViewer pdfViewer;
        private XtraBars.Navigation.AccordionControlElement accordionControlOpenElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlSaveElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlFormFieldsElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlTextFieldElement;
        private XtraBars.Navigation.AccordionContentContainer accordionContentContainer1;
        private XtraBars.Navigation.AccordionControlElement accordionControlElement2;
        private XtraBars.Navigation.AccordionControlElement accordionControlComboBoxElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlListBoxElement;
        private XtraVerticalGrid.PropertyGridControl propertyGridControl;
        private XtraBars.Navigation.AccordionControlElement accordionControlRemoveElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlCheckBoxElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlSignatureElement;
        private XtraEditors.SidePanel sidePanel;
        private XtraLayout.LayoutControl layoutControl1;
        private XtraEditors.GroupControl groupControl1;
        private XtraLayout.LayoutControlGroup Root;
        private XtraLayout.LayoutControlItem layoutControlItem1;
        private XtraLayout.SplitterItem splitterItem1;
        private XtraLayout.LayoutControlItem layoutControlItem3;
    }
}
