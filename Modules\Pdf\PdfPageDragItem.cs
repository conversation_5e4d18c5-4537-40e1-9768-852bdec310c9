﻿using System;
using System.Drawing;
using System.Windows.Forms;
using DevExpress.Pdf;
using DevExpress.XtraPdfViewer;

namespace DevExpress.Docs.Demos {
    public class PdfPageDragItem {
        readonly PdfContentData item;
        DragDirection dragDirection;
        readonly PdfViewer viewer;

        protected PdfContentData Item { get { return item; } }
        protected DragPoint[] DragPoints { get; set; }
        protected PdfPoint StartDragLocation { get; set; }
        protected int DragIndex { get; set; }
        protected PdfViewer Viewer { get { return viewer; } }
        protected bool IsDragging { get { return dragDirection != DragDirection.None; } }
        protected IDocumentController Controller { get; }

        public PdfPageDragItem(PdfViewer viewer, PdfContentData item, IDocumentController controller) {
            this.item = item;
            this.viewer = viewer;
            Controller = controller;
            Update();
        }

        public void UpdateCursor(PointF clientPoint) {
            PdfDocumentPosition position = viewer.GetDocumentPosition(clientPoint);
            if(position.PageNumber == item.PageNumber) {
                foreach(DragPoint point in DragPoints)
                    if(point.Contains(position.Point)) {
                        switch(point.Direction) {
                            case DragDirection.Move:
                            case DragDirection.MovePoint:
                                viewer.Cursor = Cursors.SizeAll;
                                break;
                            case DragDirection.ResizeBottom:
                            case DragDirection.ResizeTop:
                                viewer.Cursor = Cursors.SizeNS;
                                break;
                            case DragDirection.ResizeTopLeft:
                            case DragDirection.ResizeBottomRight:
                                viewer.Cursor = Cursors.SizeNWSE;
                                break;
                            case DragDirection.ResizeBottomLeft:
                            case DragDirection.ResizeTopRight:
                                viewer.Cursor = Cursors.SizeNESW;
                                break;
                            case DragDirection.ResizeLeft:
                            case DragDirection.ResizeRight:
                                viewer.Cursor = Cursors.SizeWE;
                                break;
                        }
                        return;
                    }
                viewer.Cursor = Cursors.Default;
            }
        }
        public virtual void Update() {
            DragPoints = DragPoint.CreateDragPoints(item.Rectangle.InnerRectangle);
            viewer.Invalidate();
        }
        public virtual void Click(PointF clientPoint) {

        }
        public bool StartDrag(PointF clientPoint) {
            PdfDocumentPosition position = viewer.GetDocumentPosition(clientPoint, false);
            if(position != null && position.PageNumber == item.PageNumber) {
                StartDragLocation = position.Point;
                foreach(DragPoint dragPoint in DragPoints)
                    if(dragPoint.Contains(StartDragLocation)) {
                        dragDirection = dragPoint.Direction;
                        DragIndex = dragPoint.Index;
                        return true;
                    }
            }
            return false;
        }
        public virtual void ContinueDrag(PointF clientPoint) {
            if(IsDragging) {
                PdfDocumentPosition position = viewer.GetDocumentPosition(clientPoint, true);
                if(position.PageNumber == item.PageNumber) {
                    double dx = position.Point.X - StartDragLocation.X;
                    double dy = position.Point.Y - StartDragLocation.Y;
                    StartDragLocation = position.Point;
                    PdfRectangle rectangle = item.Rectangle.InnerRectangle;
                    double left = rectangle.Left;
                    double bottom = rectangle.Bottom;
                    double right = rectangle.Right;
                    double top = rectangle.Top;
                    switch(dragDirection) {
                        case DragDirection.Move:
                            rectangle = new PdfRectangle(left + dx, bottom + dy, right + dx, top + dy);
                            break;
                        case DragDirection.ResizeBottom:
                            rectangle = new PdfRectangle(left, Math.Min(bottom + dy, top - 1), right, top);
                            break;
                        case DragDirection.ResizeBottomLeft:
                            rectangle = new PdfRectangle(Math.Min(left + dx, right - 1), Math.Min(bottom + dy, top - 1), right, top);
                            break;
                        case DragDirection.ResizeBottomRight:
                            rectangle = new PdfRectangle(left, Math.Min(bottom + dy, top - 1), Math.Max(left + 1, right + dx), top);
                            break;
                        case DragDirection.ResizeLeft:
                            rectangle = new PdfRectangle(Math.Min(left + dx, right - 1), bottom, right, top);
                            break;
                        case DragDirection.ResizeRight:
                            rectangle = new PdfRectangle(left, bottom, Math.Max(left + 1, right + dx), top);
                            break;
                        case DragDirection.ResizeTop:
                            rectangle = new PdfRectangle(left, bottom, right, Math.Max(bottom + 1, top + dy));
                            break;
                        case DragDirection.ResizeTopLeft:
                            rectangle = new PdfRectangle(Math.Min(left + dx, right - 1), bottom, right, Math.Max(bottom + 1, top + dy));
                            break;
                        case DragDirection.ResizeTopRight:
                            rectangle = new PdfRectangle(left, bottom, Math.Max(left + 1, right + dx), Math.Max(bottom + 1, top + dy));
                            break;
                        default:
                            return;
                    }
                    item.Rectangle.InnerRectangle = rectangle;
                    Update();
                }
            }
        }
        public void EndDrag(PointF clientPoint) {
            if(IsDragging) {
                dragDirection = DragDirection.None;
                Controller.UpdateDocument();
            }
        }

        public virtual void DrawSelectedItem(Graphics g) {
            PdfRectangle dragRect = item.Rectangle.InnerRectangle;
            PointF topLeft = viewer.GetClientPoint(new PdfDocumentPosition(item.PageNumber, dragRect.TopLeft));
            PointF bottomRight = viewer.GetClientPoint(new PdfDocumentPosition(item.PageNumber, dragRect.BottomRight));
            Rectangle clientRect = Rectangle.FromLTRB((int)topLeft.X, (int)topLeft.Y, (int)bottomRight.X, (int)bottomRight.Y);
            g.DrawRectangle(PageContentController.highlight, clientRect);
        }
        public void DrawDragPoints(Graphics g, Brush foreBrush) {
            for(int i = 0; i < DragPoints.Length; i++) {
                if(DragPoints[i].Direction == DragDirection.Move)
                    continue;
                PdfRectangle dragRect = DragPoints[i].Rectangle;
                PointF topLeft = viewer.GetClientPoint(new PdfDocumentPosition(item.PageNumber, dragRect.TopLeft));
                PointF bottomRight = viewer.GetClientPoint(new PdfDocumentPosition(item.PageNumber, dragRect.BottomRight));
                RectangleF clientRect = RectangleF.FromLTRB(topLeft.X, topLeft.Y, bottomRight.X, bottomRight.Y);
                g.FillRectangle(foreBrush, clientRect);
            }
        }
    }
}
