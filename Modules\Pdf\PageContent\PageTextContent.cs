﻿using System.ComponentModel;
using System.ComponentModel.Design;
using System.Drawing;
using System.Drawing.Design;
using DevExpress.Pdf;

namespace DevExpress.Docs.Demos {
    public class PageTextContent : PageContentData {
        string fText;
        Font fFont = new Font("Microsoft Sans Serif", 24);
        Color fForeColor;
        PdfStringFormat fStringFormat;

        [Editor(typeof(MultilineStringEditor), typeof(UITypeEditor))]
        [Category(BehaviorCategory)]
        public string Text {
            get { return fText; }
            set {
                fText = value;
                UpdateModel();
            }
        }

        [Editor(typeof(FormFieldFontEditor), typeof(UITypeEditor))]
        [TypeConverter(typeof(FontConverter))]
        public Font Font {
            get { return fFont; }
            set {
                if(fFont != null)
                    fFont.Dispose();
                fFont = value;
                Controller.UpdateDocument();
            }
        }

        [TypeConverter(typeof(ColorConverter))]
        public Color ForeColor {
            get { return fForeColor; }
            set {
                fForeColor = value;
                Controller.UpdateDocument();
            }
        }

        [TypeConverter(typeof(PdfStringFormatConverter))]
        public PdfStringFormat StringFormat {
            get { return fStringFormat; }
            set {
                fStringFormat = value;
                Controller.UpdateDocument();
            }
        }

        public PageTextContent(PdfDocumentPosition position, PageContentController controller) : base(position, controller) {
            fText = "text";
            fForeColor = Color.Black;
            StringFormat = new PdfStringFormat() { Alignment = PdfStringAlignment.Center, LineAlignment = PdfStringAlignment.Center };
        }
        public override void FillContent(PdfGraphics graphics, PdfRectangle pageCropBox) {
            graphics.DrawString(Text, Font, new SolidBrush(ForeColor), new RectangleF(Rectangle.Left, (float)(pageCropBox.Height - Rectangle.Top), (float)Rectangle.InnerRectangle.Width, (float)Rectangle.InnerRectangle.Height), StringFormat);
        }
    }
}
