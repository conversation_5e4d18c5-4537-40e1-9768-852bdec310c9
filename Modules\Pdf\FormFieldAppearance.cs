﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Design;
using DevExpress.Pdf;

namespace DevExpress.Docs.Demos {
    public class FormFieldAppearance : IDisposable {
        static PdfRGBColor ToPdfColor(Color color) {
            return color.IsEmpty ? null : new PdfRGBColor(color.R / 255.0, color.G / 255.0, color.B / 255.0);
        }

        readonly IDocumentController controller;
        Font fFont = new Font("Microsoft Sans Serif", 12);
        Color fBackgroundColor;
        Color fForeColor;
        Color fBorderColor;
        double fBorderWidth;
        PdfAcroFormBorderStyle fBorderStyle;

        [TypeConverter(typeof(ColorConverter))]
        public Color BackgroundColor {
            get { return fBackgroundColor; }
            set {
                fBackgroundColor = value;
                controller.UpdateDocument();
            }
        }

        [TypeConverter(typeof(ColorConverter))]
        public Color ForeColor {
            get { return fForeColor; }
            set {
                fForeColor = value;
                controller.UpdateDocument();
            }
        }

        [TypeConverter(typeof(ColorConverter))]
        public Color BorderColor {
            get { return fBorderColor; }
            set {
                fBorderColor = value;
                controller.UpdateDocument();
            }
        }

        [Editor(typeof(FormFieldFontEditor), typeof(UITypeEditor))]
        [TypeConverter(typeof(FontConverter))]
        public Font Font {
            get { return fFont; }
            set {
                if(fFont != null)
                    fFont.Dispose();
                fFont = value;
                controller.UpdateDocument();
            }
        }
        public double BorderWidth {
            get { return fBorderWidth; }
            set {
                fBorderWidth = value;
                controller.UpdateDocument();
            }
        }
        public PdfAcroFormBorderStyle BorderStyle {
            get { return fBorderStyle; }
            set {
                fBorderStyle = value;
                controller.UpdateDocument();
            }
        }

        public FormFieldAppearance(IDocumentController controller) {
            this.controller = controller;
        }
        public PdfAcroFormFieldAppearance CreateAcroFormFieldAppearance() {
            PdfAcroFormFieldAppearance acroFormFieldAppearance = new PdfAcroFormFieldAppearance();
            acroFormFieldAppearance.BackgroundColor = ToPdfColor(BackgroundColor);
            acroFormFieldAppearance.ForeColor = ToPdfColor(ForeColor);
            acroFormFieldAppearance.BorderAppearance = new PdfAcroFormBorderAppearance() {
                Color = ToPdfColor(BorderColor),
                Width = BorderWidth,
                Style = BorderStyle
            };
            Font font = Font;
            if(font == null) {
                acroFormFieldAppearance.FontFamily = null;
                acroFormFieldAppearance.FontSize = 0;
                acroFormFieldAppearance.FontStyle = PdfFontStyle.Regular;
            }
            else {
                acroFormFieldAppearance.FontFamily = font.FontFamily.Name;
                acroFormFieldAppearance.FontSize = font.SizeInPoints;
                acroFormFieldAppearance.FontStyle = (PdfFontStyle)font.Style;
            }
            return acroFormFieldAppearance;
        }
        public void Dispose() {
            if(fFont != null)
                fFont.Dispose();
        }
        public override string ToString() {
            return "Appearance";
        }
    }
}
