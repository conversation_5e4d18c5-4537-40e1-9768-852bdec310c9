﻿namespace DevExpress.Docs.Demos
{
    partial class PdfPasswordProtection
	{
		/// <summary> 
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary> 
		/// Clean up any resources being used.
		/// </summary>
		/// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
		protected override void Dispose(bool disposing)
		{
			if (disposing && (components != null))
			{
				components.Dispose();
			}
			base.Dispose(disposing);
		}

		#region Component Designer generated code

		/// <summary> 
		/// Required method for Designer support - do not modify 
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
            this.cbAlgorithm = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.labelAlgorithm = new DevExpress.XtraEditors.LabelControl();
            this.buttonProtect = new DevExpress.XtraEditors.SimpleButton();
            this.cbDataExtraction = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.cbModifying = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.labelInteractivity = new DevExpress.XtraEditors.LabelControl();
            this.cbInteractivity = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.labelModifying = new DevExpress.XtraEditors.LabelControl();
            this.cbPrinting = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.labelDataExtraction = new DevExpress.XtraEditors.LabelControl();
            this.labelPrinting = new DevExpress.XtraEditors.LabelControl();
            this.teOwnerPassword = new DevExpress.XtraEditors.TextEdit();
            this.labelUserPassword = new DevExpress.XtraEditors.LabelControl();
            this.labelOwnerPassword = new DevExpress.XtraEditors.LabelControl();
            this.teUserPassword = new DevExpress.XtraEditors.TextEdit();
            this.pdfViewer = new DevExpress.XtraPdfViewer.PdfViewer();
            this.sidePanel = new DevExpress.XtraEditors.SidePanel();
            this.tablePanel1 = new DevExpress.Utils.Layout.TablePanel();
            ((System.ComponentModel.ISupportInitialize)(this.cbAlgorithm.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbDataExtraction.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbModifying.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbInteractivity.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbPrinting.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.teOwnerPassword.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.teUserPassword.Properties)).BeginInit();
            this.sidePanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tablePanel1)).BeginInit();
            this.tablePanel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // cbAlgorithm
            // 
            this.tablePanel1.SetColumn(this.cbAlgorithm, 1);
            this.cbAlgorithm.Location = new System.Drawing.Point(123, 67);
            this.cbAlgorithm.Name = "cbAlgorithm";
            this.cbAlgorithm.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.tablePanel1.SetRow(this.cbAlgorithm, 2);
            this.cbAlgorithm.Size = new System.Drawing.Size(149, 20);
            this.cbAlgorithm.TabIndex = 1;
            // 
            // labelAlgorithm
            // 
            this.tablePanel1.SetColumn(this.labelAlgorithm, 0);
            this.labelAlgorithm.Location = new System.Drawing.Point(15, 70);
            this.labelAlgorithm.Name = "labelAlgorithm";
            this.tablePanel1.SetRow(this.labelAlgorithm, 2);
            this.labelAlgorithm.Size = new System.Drawing.Size(102, 13);
            this.labelAlgorithm.TabIndex = 0;
            this.labelAlgorithm.Text = "Encryption algorithm:";
            // 
            // buttonProtect
            // 
            this.tablePanel1.SetColumn(this.buttonProtect, 1);
            this.buttonProtect.Location = new System.Drawing.Point(123, 197);
            this.buttonProtect.Name = "buttonProtect";
            this.tablePanel1.SetRow(this.buttonProtect, 7);
            this.buttonProtect.Size = new System.Drawing.Size(149, 20);
            this.buttonProtect.TabIndex = 0;
            this.buttonProtect.Text = "Protect and Save...";
            this.buttonProtect.Click += new System.EventHandler(this.OnProtectButtonClick);
            // 
            // cbDataExtraction
            // 
            this.tablePanel1.SetColumn(this.cbDataExtraction, 1);
            this.cbDataExtraction.Location = new System.Drawing.Point(123, 119);
            this.cbDataExtraction.Name = "cbDataExtraction";
            this.cbDataExtraction.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.tablePanel1.SetRow(this.cbDataExtraction, 4);
            this.cbDataExtraction.Size = new System.Drawing.Size(149, 20);
            this.cbDataExtraction.TabIndex = 3;
            // 
            // cbModifying
            // 
            this.tablePanel1.SetColumn(this.cbModifying, 1);
            this.cbModifying.Location = new System.Drawing.Point(123, 145);
            this.cbModifying.Name = "cbModifying";
            this.cbModifying.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.tablePanel1.SetRow(this.cbModifying, 5);
            this.cbModifying.Size = new System.Drawing.Size(149, 20);
            this.cbModifying.TabIndex = 5;
            // 
            // labelInteractivity
            // 
            this.tablePanel1.SetColumn(this.labelInteractivity, 0);
            this.labelInteractivity.Location = new System.Drawing.Point(15, 174);
            this.labelInteractivity.Name = "labelInteractivity";
            this.tablePanel1.SetRow(this.labelInteractivity, 6);
            this.labelInteractivity.Size = new System.Drawing.Size(63, 13);
            this.labelInteractivity.TabIndex = 6;
            this.labelInteractivity.Text = "Interactivity:";
            // 
            // cbInteractivity
            // 
            this.tablePanel1.SetColumn(this.cbInteractivity, 1);
            this.cbInteractivity.Location = new System.Drawing.Point(123, 171);
            this.cbInteractivity.Name = "cbInteractivity";
            this.cbInteractivity.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.tablePanel1.SetRow(this.cbInteractivity, 6);
            this.cbInteractivity.Size = new System.Drawing.Size(149, 20);
            this.cbInteractivity.TabIndex = 7;
            // 
            // labelModifying
            // 
            this.tablePanel1.SetColumn(this.labelModifying, 0);
            this.labelModifying.Location = new System.Drawing.Point(15, 148);
            this.labelModifying.Name = "labelModifying";
            this.tablePanel1.SetRow(this.labelModifying, 5);
            this.labelModifying.Size = new System.Drawing.Size(50, 13);
            this.labelModifying.TabIndex = 4;
            this.labelModifying.Text = "Modifying:";
            // 
            // cbPrinting
            // 
            this.tablePanel1.SetColumn(this.cbPrinting, 1);
            this.cbPrinting.Location = new System.Drawing.Point(123, 93);
            this.cbPrinting.Name = "cbPrinting";
            this.cbPrinting.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.tablePanel1.SetRow(this.cbPrinting, 3);
            this.cbPrinting.Size = new System.Drawing.Size(149, 20);
            this.cbPrinting.TabIndex = 1;
            // 
            // labelDataExtraction
            // 
            this.tablePanel1.SetColumn(this.labelDataExtraction, 0);
            this.labelDataExtraction.Location = new System.Drawing.Point(15, 122);
            this.labelDataExtraction.Name = "labelDataExtraction";
            this.tablePanel1.SetRow(this.labelDataExtraction, 4);
            this.labelDataExtraction.Size = new System.Drawing.Size(79, 13);
            this.labelDataExtraction.TabIndex = 2;
            this.labelDataExtraction.Text = "Data extraction:";
            // 
            // labelPrinting
            // 
            this.tablePanel1.SetColumn(this.labelPrinting, 0);
            this.labelPrinting.Location = new System.Drawing.Point(15, 96);
            this.labelPrinting.Name = "labelPrinting";
            this.tablePanel1.SetRow(this.labelPrinting, 3);
            this.labelPrinting.Size = new System.Drawing.Size(40, 13);
            this.labelPrinting.TabIndex = 0;
            this.labelPrinting.Text = "Printing:";
            // 
            // teOwnerPassword
            // 
            this.tablePanel1.SetColumn(this.teOwnerPassword, 1);
            this.teOwnerPassword.Location = new System.Drawing.Point(122, 15);
            this.teOwnerPassword.Margin = new System.Windows.Forms.Padding(2);
            this.teOwnerPassword.Name = "teOwnerPassword";
            this.teOwnerPassword.Properties.PasswordChar = '*';
            this.tablePanel1.SetRow(this.teOwnerPassword, 0);
            this.teOwnerPassword.Size = new System.Drawing.Size(151, 20);
            this.teOwnerPassword.TabIndex = 1;
            // 
            // labelUserPassword
            // 
            this.tablePanel1.SetColumn(this.labelUserPassword, 0);
            this.labelUserPassword.Location = new System.Drawing.Point(15, 44);
            this.labelUserPassword.Name = "labelUserPassword";
            this.tablePanel1.SetRow(this.labelUserPassword, 1);
            this.labelUserPassword.Size = new System.Drawing.Size(75, 13);
            this.labelUserPassword.TabIndex = 2;
            this.labelUserPassword.Text = "User password:";
            // 
            // labelOwnerPassword
            // 
            this.tablePanel1.SetColumn(this.labelOwnerPassword, 0);
            this.labelOwnerPassword.Location = new System.Drawing.Point(15, 18);
            this.labelOwnerPassword.Name = "labelOwnerPassword";
            this.tablePanel1.SetRow(this.labelOwnerPassword, 0);
            this.labelOwnerPassword.Size = new System.Drawing.Size(85, 13);
            this.labelOwnerPassword.TabIndex = 0;
            this.labelOwnerPassword.Text = "Owner password:";
            // 
            // teUserPassword
            // 
            this.tablePanel1.SetColumn(this.teUserPassword, 1);
            this.teUserPassword.Location = new System.Drawing.Point(122, 41);
            this.teUserPassword.Margin = new System.Windows.Forms.Padding(2);
            this.teUserPassword.Name = "teUserPassword";
            this.teUserPassword.Properties.PasswordChar = '*';
            this.tablePanel1.SetRow(this.teUserPassword, 1);
            this.teUserPassword.Size = new System.Drawing.Size(151, 20);
            this.teUserPassword.TabIndex = 3;
            // 
            // pdfViewer
            // 
            this.pdfViewer.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pdfViewer.Location = new System.Drawing.Point(0, 0);
            this.pdfViewer.Name = "pdfViewer";
            this.pdfViewer.NavigationPaneInitialVisibility = DevExpress.XtraPdfViewer.PdfNavigationPaneVisibility.Hidden;
            this.pdfViewer.ReadOnly = true;
            this.pdfViewer.Size = new System.Drawing.Size(449, 564);
            this.pdfViewer.TabIndex = 7;
            this.pdfViewer.TabStop = false;
            this.pdfViewer.ZoomMode = DevExpress.XtraPdfViewer.PdfZoomMode.FitToWidth;
            // 
            // sidePanel
            // 
            this.sidePanel.Controls.Add(this.tablePanel1);
            this.sidePanel.Dock = System.Windows.Forms.DockStyle.Right;
            this.sidePanel.Location = new System.Drawing.Point(449, 0);
            this.sidePanel.MinimumSize = new System.Drawing.Size(200, 0);
            this.sidePanel.Name = "sidePanel";
            this.sidePanel.Size = new System.Drawing.Size(288, 564);
            this.sidePanel.TabIndex = 8;
            this.sidePanel.Text = "sidePanel1";
            // 
            // tablePanel1
            // 
            this.tablePanel1.Columns.AddRange(new DevExpress.Utils.Layout.TablePanelColumn[] {
            new DevExpress.Utils.Layout.TablePanelColumn(DevExpress.Utils.Layout.TablePanelEntityStyle.AutoSize, 5F),
            new DevExpress.Utils.Layout.TablePanelColumn(DevExpress.Utils.Layout.TablePanelEntityStyle.Relative, 55F)});
            this.tablePanel1.Controls.Add(this.labelInteractivity);
            this.tablePanel1.Controls.Add(this.labelAlgorithm);
            this.tablePanel1.Controls.Add(this.labelModifying);
            this.tablePanel1.Controls.Add(this.cbAlgorithm);
            this.tablePanel1.Controls.Add(this.labelDataExtraction);
            this.tablePanel1.Controls.Add(this.cbDataExtraction);
            this.tablePanel1.Controls.Add(this.labelPrinting);
            this.tablePanel1.Controls.Add(this.buttonProtect);
            this.tablePanel1.Controls.Add(this.labelUserPassword);
            this.tablePanel1.Controls.Add(this.teOwnerPassword);
            this.tablePanel1.Controls.Add(this.labelOwnerPassword);
            this.tablePanel1.Controls.Add(this.cbPrinting);
            this.tablePanel1.Controls.Add(this.cbModifying);
            this.tablePanel1.Controls.Add(this.cbInteractivity);
            this.tablePanel1.Controls.Add(this.teUserPassword);
            this.tablePanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tablePanel1.Location = new System.Drawing.Point(1, 0);
            this.tablePanel1.Name = "tablePanel1";
            this.tablePanel1.Padding = new System.Windows.Forms.Padding(12);
            this.tablePanel1.Rows.AddRange(new DevExpress.Utils.Layout.TablePanelRow[] {
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F)});
            this.tablePanel1.Size = new System.Drawing.Size(287, 564);
            this.tablePanel1.TabIndex = 7;
            // 
            // PdfPasswordProtection
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.pdfViewer);
            this.Controls.Add(this.sidePanel);
            this.Name = "PdfPasswordProtection";
            this.Size = new System.Drawing.Size(737, 564);
            ((System.ComponentModel.ISupportInitialize)(this.cbAlgorithm.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbDataExtraction.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbModifying.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbInteractivity.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbPrinting.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.teOwnerPassword.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.teUserPassword.Properties)).EndInit();
            this.sidePanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.tablePanel1)).EndInit();
            this.tablePanel1.ResumeLayout(false);
            this.tablePanel1.PerformLayout();
            this.ResumeLayout(false);

		}

		#endregion
        private XtraEditors.TextEdit teOwnerPassword;
        private XtraEditors.LabelControl labelUserPassword;
        private XtraEditors.LabelControl labelOwnerPassword;
        private XtraEditors.TextEdit teUserPassword;
        private XtraEditors.SimpleButton buttonProtect;
        private XtraPdfViewer.PdfViewer pdfViewer;
        private XtraEditors.LabelControl labelDataExtraction;
        private XtraEditors.LabelControl labelPrinting;
        private XtraEditors.LabelControl labelInteractivity;
        private XtraEditors.LabelControl labelModifying;
        private XtraEditors.ImageComboBoxEdit cbPrinting;
        private XtraEditors.ImageComboBoxEdit cbInteractivity;
        private XtraEditors.ImageComboBoxEdit cbModifying;
        private XtraEditors.ImageComboBoxEdit cbDataExtraction;
        private XtraEditors.ImageComboBoxEdit cbAlgorithm;
        private XtraEditors.LabelControl labelAlgorithm;
        private Utils.Layout.TablePanel tablePanel1;
        private XtraEditors.SidePanel sidePanel;
    }
}
