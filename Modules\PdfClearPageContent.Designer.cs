﻿
namespace DevExpress.Docs.Demos
{
    partial class PdfClearPageContent
	{
		/// <summary> 
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		#region Component Designer generated code

		/// <summary> 
		/// Required method for Designer support - do not modify 
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
            this.pdfViewer = new DevExpress.XtraPdfViewer.PdfViewer();
            this.optionsGroup = new DevExpress.XtraEditors.GroupControl();
            this.optionsPanel = new DevExpress.Utils.Layout.TablePanel();
            this.checkGraphicsOpt = new DevExpress.XtraEditors.CheckEdit();
            this.checkTextOpt = new DevExpress.XtraEditors.CheckEdit();
            this.checkImagesOpt = new DevExpress.XtraEditors.CheckEdit();
            this.checkAnnotationsOpt = new DevExpress.XtraEditors.CheckEdit();
            this.clearContentBtn = new DevExpress.XtraEditors.SimpleButton();
            this.redactContentBtn = new DevExpress.XtraEditors.SimpleButton();
            this.saveAsBtn = new DevExpress.XtraEditors.SimpleButton();
            this.openBtn = new DevExpress.XtraEditors.SimpleButton();
            this.sidePanel = new DevExpress.XtraEditors.SidePanel();
            this.sidePanelLayout = new DevExpress.Utils.Layout.TablePanel();
            this.labelFile = new DevExpress.XtraEditors.LabelControl();
            this.labelSeparator = new System.Windows.Forms.Label();
            this.labelProperties = new DevExpress.XtraEditors.LabelControl();
            this.propertiesGroup = new DevExpress.XtraEditors.GroupControl();
            this.propertyGridControl = new DevExpress.XtraVerticalGrid.PropertyGridControl();
            this.viewerPanel = new DevExpress.XtraEditors.PanelControl();
            ((System.ComponentModel.ISupportInitialize)(this.optionsGroup)).BeginInit();
            this.optionsGroup.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.optionsPanel)).BeginInit();
            this.optionsPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.checkGraphicsOpt.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkTextOpt.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkImagesOpt.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkAnnotationsOpt.Properties)).BeginInit();
            this.sidePanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.sidePanelLayout)).BeginInit();
            this.sidePanelLayout.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.propertiesGroup)).BeginInit();
            this.propertiesGroup.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.propertyGridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.viewerPanel)).BeginInit();
            this.viewerPanel.SuspendLayout();
            this.SuspendLayout();
            // 
            // pdfViewer
            // 
            this.pdfViewer.DetachStreamAfterLoadComplete = true;
            this.pdfViewer.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pdfViewer.Location = new System.Drawing.Point(0, 0);
            this.pdfViewer.Name = "pdfViewer";
            this.pdfViewer.NavigationPaneInitialVisibility = DevExpress.XtraPdfViewer.PdfNavigationPaneVisibility.Hidden;
            this.pdfViewer.Size = new System.Drawing.Size(518, 564);
            this.pdfViewer.TabIndex = 5;
            this.pdfViewer.TabStop = false;
            this.pdfViewer.ZoomMode = DevExpress.XtraPdfViewer.PdfZoomMode.FitToWidth;
            // 
            // optionsGroup
            // 
            this.optionsGroup.AutoSize = true;
            this.sidePanelLayout.SetColumn(this.optionsGroup, 0);
            this.optionsGroup.Controls.Add(this.optionsPanel);
            this.optionsGroup.Dock = System.Windows.Forms.DockStyle.Fill;
            this.optionsGroup.GroupStyle = DevExpress.Utils.GroupStyle.Light;
            this.optionsGroup.Name = "optionsGroup";
            this.sidePanelLayout.SetRow(this.optionsGroup, 7);
            this.optionsGroup.TabIndex = 7;
            this.optionsGroup.Text = "Clear Content Options";
            // 
            // optionsPanel
            // 
            this.optionsPanel.AutoSize = true;
            this.optionsPanel.Columns.AddRange(new DevExpress.Utils.Layout.TablePanelColumn[] {
            new DevExpress.Utils.Layout.TablePanelColumn(DevExpress.Utils.Layout.TablePanelEntityStyle.AutoSize, 55F)});
            this.optionsPanel.Controls.Add(this.checkGraphicsOpt);
            this.optionsPanel.Controls.Add(this.checkTextOpt);
            this.optionsPanel.Controls.Add(this.checkImagesOpt);
            this.optionsPanel.Controls.Add(this.checkAnnotationsOpt);
            this.optionsPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.optionsPanel.Location = new System.Drawing.Point(2, 23);
            this.optionsPanel.Name = "optionsPanel";
            this.optionsPanel.Padding = new System.Windows.Forms.Padding(9, 0, 9, 5);
            this.optionsPanel.Rows.AddRange(new DevExpress.Utils.Layout.TablePanelRow[] {
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.AutoSize, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.AutoSize, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.AutoSize, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.AutoSize, 26F)});
            this.optionsPanel.TabIndex = 4;
            // 
            // checkGraphicsOpt
            // 
            this.optionsPanel.SetColumn(this.checkGraphicsOpt, 0);
            this.checkGraphicsOpt.Location = new System.Drawing.Point(12, 3);
            this.checkGraphicsOpt.Name = "checkGraphicsOpt";
            this.checkGraphicsOpt.Properties.Caption = "Keep Graphics";
            this.optionsPanel.SetRow(this.checkGraphicsOpt, 0);
            this.checkGraphicsOpt.Size = new System.Drawing.Size(160, 20);
            this.checkGraphicsOpt.TabIndex = 2;
            // 
            // checkTextOpt
            // 
            this.optionsPanel.SetColumn(this.checkTextOpt, 0);
            this.checkTextOpt.Location = new System.Drawing.Point(12, 29);
            this.checkTextOpt.Name = "checkTextOpt";
            this.checkTextOpt.Properties.Caption = "Keep Text";
            this.optionsPanel.SetRow(this.checkTextOpt, 1);
            this.checkTextOpt.Size = new System.Drawing.Size(160, 20);
            this.checkTextOpt.TabIndex = 1;
            // 
            // checkImagesOpt
            // 
            this.optionsPanel.SetColumn(this.checkImagesOpt, 0);
            this.checkImagesOpt.Location = new System.Drawing.Point(12, 55);
            this.checkImagesOpt.Name = "checkImagesOpt";
            this.checkImagesOpt.Properties.Caption = "Keep Images";
            this.optionsPanel.SetRow(this.checkImagesOpt, 2);
            this.checkImagesOpt.Size = new System.Drawing.Size(160, 20);
            this.checkImagesOpt.TabIndex = 3;
            // 
            // checkAnnotationsOpt
            // 
            this.optionsPanel.SetColumn(this.checkAnnotationsOpt, 0);
            this.checkAnnotationsOpt.Name = "checkAnnotationsOpt";
            this.checkAnnotationsOpt.Properties.Caption = "Keep Annotations";
            this.optionsPanel.SetRow(this.checkAnnotationsOpt, 3);
            this.checkAnnotationsOpt.Size = new System.Drawing.Size(160, 20);
            this.checkAnnotationsOpt.TabIndex = 0;
            // 
            // clearContentBtn
            // 
            this.sidePanelLayout.SetColumn(this.clearContentBtn, 0);
            this.clearContentBtn.Dock = System.Windows.Forms.DockStyle.Fill;
            this.clearContentBtn.Name = "clearContentBtn";
            this.sidePanelLayout.SetRow(this.clearContentBtn, 11);
            this.clearContentBtn.TabIndex = 6;
            this.clearContentBtn.Text = "Clear Selected Content";
            this.clearContentBtn.Click += new System.EventHandler(this.OnButtonClearContentClick);
            // 
            // redactContentBtn
            // 
            this.sidePanelLayout.SetColumn(this.redactContentBtn, 0);
            this.redactContentBtn.Dock = System.Windows.Forms.DockStyle.Fill;
            this.redactContentBtn.Name = "redactContentBtn";
            this.sidePanelLayout.SetRow(this.redactContentBtn, 12);
            this.redactContentBtn.TabIndex = 6;
            this.redactContentBtn.Text = "Redact Selected Content";
            this.redactContentBtn.Click += new System.EventHandler(this.OnButtonRedactAnnotationClick);
            // 
            // saveAsBtn
            // 
            this.sidePanelLayout.SetColumn(this.saveAsBtn, 0);
            this.saveAsBtn.Dock = System.Windows.Forms.DockStyle.Fill;
            this.saveAsBtn.Name = "saveAsBtn";
            this.sidePanelLayout.SetRow(this.saveAsBtn, 2);
            this.saveAsBtn.TabIndex = 5;
            this.saveAsBtn.Text = "Save As...";
            this.saveAsBtn.Click += new System.EventHandler(this.OnSaveButtonClick);
            // 
            // openBtn
            // 
            this.sidePanelLayout.SetColumn(this.openBtn, 0);
            this.openBtn.Dock = System.Windows.Forms.DockStyle.Fill;
            this.openBtn.Name = "openBtn";
            this.sidePanelLayout.SetRow(this.openBtn, 1);
            this.openBtn.TabIndex = 0;
            this.openBtn.Text = "Load Document...";
            this.openBtn.Click += new System.EventHandler(this.OnButtonOpenClick);
            // 
            // sidePanel
            // 
            this.sidePanel.Controls.Add(this.sidePanelLayout);
            this.sidePanel.Dock = System.Windows.Forms.DockStyle.Right;
            this.sidePanel.Location = new System.Drawing.Point(518, 0);
            this.sidePanel.MinimumSize = new System.Drawing.Size(200, 0);
            this.sidePanel.Name = "sidePanel";
            this.sidePanel.Size = new System.Drawing.Size(219, 564);
            this.sidePanel.TabIndex = 4;
            this.sidePanel.Text = "sidePanel1";
            // 
            // sidePanelLayout
            // 
            this.sidePanelLayout.Columns.AddRange(new DevExpress.Utils.Layout.TablePanelColumn[] {
            new DevExpress.Utils.Layout.TablePanelColumn(DevExpress.Utils.Layout.TablePanelEntityStyle.Relative, 100F)});
            this.sidePanelLayout.Controls.Add(this.saveAsBtn);
            this.sidePanelLayout.Controls.Add(this.openBtn);
            this.sidePanelLayout.Controls.Add(this.labelFile);
            this.sidePanelLayout.Controls.Add(this.labelSeparator);
            this.sidePanelLayout.Controls.Add(this.labelProperties);
            this.sidePanelLayout.Controls.Add(this.clearContentBtn);
            this.sidePanelLayout.Controls.Add(this.redactContentBtn);
            this.sidePanelLayout.Controls.Add(this.optionsGroup);
            this.sidePanelLayout.Controls.Add(this.propertiesGroup);
            this.sidePanelLayout.Dock = System.Windows.Forms.DockStyle.Fill;
            this.sidePanelLayout.Location = new System.Drawing.Point(1, 0);
            this.sidePanelLayout.Name = "sidePanelLayout";
            this.sidePanelLayout.Padding = new System.Windows.Forms.Padding(12);
            this.sidePanelLayout.Rows.AddRange(new DevExpress.Utils.Layout.TablePanelRow[] {
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 10F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 1F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 10F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 130F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 13F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 197F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 13F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Relative, 10F)});
            this.sidePanelLayout.Size = new System.Drawing.Size(218, 564);
            this.sidePanelLayout.TabIndex = 6;
            // 
            // labelFile
            // 
            this.labelFile.Name = "labelFile";
            this.sidePanelLayout.SetRow(this.labelFile, 0);
            this.sidePanelLayout.SetColumn(this.labelFile, 0);
            this.labelFile.TabIndex = 6;
            this.labelFile.Text = "File";
            // 
            // labelSeparator
            // 
            this.labelSeparator.BackColor = System.Drawing.Color.FromArgb(255,210,210,210);
            this.sidePanelLayout.SetColumn(this.labelSeparator, 0);
            this.labelSeparator.Dock = System.Windows.Forms.DockStyle.Fill;
            this.labelSeparator.Name = "labelSeparator";
            this.sidePanelLayout.SetRow(this.labelSeparator, 4);
            this.labelSeparator.TabIndex = 7;
            // 
            // labelProperties
            // 
            this.labelProperties.Name = "labelProperties";
            this.sidePanelLayout.SetColumn(this.labelProperties, 0);
            this.sidePanelLayout.SetRow(this.labelProperties, 6);
            this.labelProperties.TabIndex = 8;
            this.labelProperties.Text = "Properties";
            // 
            // propertiesGroup
            // 
            this.sidePanelLayout.SetColumn(this.propertiesGroup, 0);
            this.propertiesGroup.Controls.Add(this.propertyGridControl);
            this.propertiesGroup.Dock = System.Windows.Forms.DockStyle.Fill;
            this.propertiesGroup.Name = "propertiesGroup";
            this.sidePanelLayout.SetRow(this.propertiesGroup, 9);
            this.propertiesGroup.TabIndex = 1;
            this.propertiesGroup.Text = "Redaction Appearance";
            // 
            // propertyGridControl
            // 
            this.propertyGridControl.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.propertyGridControl.Cursor = System.Windows.Forms.Cursors.Default;
            this.propertyGridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.propertyGridControl.Margin = new System.Windows.Forms.Padding(0);
            this.propertyGridControl.Name = "propertyGridControl";
            this.propertyGridControl.OptionsView.AllowReadOnlyRowAppearance = DevExpress.Utils.DefaultBoolean.True;
            this.propertyGridControl.OptionsView.ShowRootCategories = false;
            this.propertyGridControl.TabIndex = 2;
            // 
            // viewerPanel
            // 
            this.viewerPanel.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.viewerPanel.Controls.Add(this.pdfViewer);
            this.viewerPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.viewerPanel.Location = new System.Drawing.Point(0, 0);
            this.viewerPanel.Name = "viewerPanel";
            this.viewerPanel.Size = new System.Drawing.Size(518, 564);
            this.viewerPanel.TabIndex = 6;
            // 
            // PdfClearPageContent
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.viewerPanel);
            this.Controls.Add(this.sidePanel);
            this.Name = "PdfClearPageContent";
            this.Size = new System.Drawing.Size(737, 564);
            ((System.ComponentModel.ISupportInitialize)(this.optionsGroup)).EndInit();
            this.optionsGroup.ResumeLayout(false);
            this.optionsGroup.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.optionsPanel)).EndInit();
            this.optionsPanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.checkGraphicsOpt.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkTextOpt.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkImagesOpt.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkAnnotationsOpt.Properties)).EndInit();
            this.sidePanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.sidePanelLayout)).EndInit();
            this.sidePanelLayout.ResumeLayout(false);
            this.sidePanelLayout.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.propertiesGroup)).EndInit();
            this.propertiesGroup.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.propertyGridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.viewerPanel)).EndInit();
            this.viewerPanel.ResumeLayout(false);
            this.ResumeLayout(false);

		}

		#endregion
        private XtraPdfViewer.PdfViewer pdfViewer;
        private XtraEditors.SimpleButton clearContentBtn;
        private XtraEditors.SimpleButton redactContentBtn;
        private XtraEditors.SimpleButton saveAsBtn;
        private XtraEditors.SimpleButton openBtn;
        private XtraEditors.GroupControl optionsGroup;
        private XtraEditors.CheckEdit checkGraphicsOpt;
        private XtraEditors.CheckEdit checkTextOpt;
        private XtraEditors.CheckEdit checkImagesOpt;
        private XtraEditors.CheckEdit checkAnnotationsOpt;
        private Utils.Layout.TablePanel sidePanelLayout;
        private XtraEditors.SidePanel sidePanel;
        private XtraEditors.PanelControl viewerPanel;
        private Utils.Layout.TablePanel optionsPanel;
        private XtraEditors.GroupControl propertiesGroup;
        private XtraVerticalGrid.PropertyGridControl propertyGridControl;
        private XtraEditors.LabelControl labelFile;
        private System.Windows.Forms.Label labelSeparator;
        private XtraEditors.LabelControl labelProperties;
    }
}
