﻿using System.IO;
using DevExpress.Pdf;
using DevExpress.XtraPdfViewer;

namespace DevExpress.Docs.Demos {
    public class PdfViewerFileHelper : PdfFileHelper {
        readonly PdfViewer viewer;

        protected override string Creator {
            get { return viewer.DocumentCreator; }
            set { viewer.DocumentCreator = value; }
        }
        protected override string Producer {
            get { return viewer.DocumentProducer; }
            set { viewer.DocumentProducer = value; }
        }
        public PdfViewerFileHelper(PdfViewer processor) {
            viewer = processor;
        }
        public override void LoadDocument(string path, bool detach) {
            viewer.LoadDocument(path);
        }
        protected override void SaveDocument(string filePath, PdfSaveOptions options) {
            viewer.SaveDocument(filePath, options);
        }
        public override void LoadDocument(Stream stream) {
            viewer.LoadDocument(stream);
        }
    }
}
