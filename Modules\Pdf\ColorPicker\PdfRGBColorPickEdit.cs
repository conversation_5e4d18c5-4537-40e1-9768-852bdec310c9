﻿using System.ComponentModel;
using DevExpress.XtraEditors;

namespace DevExpress.Docs.Demos {
    [ToolboxItem(false)]
    public class PdfRGBColorPickEdit : ColorPickEdit {
        static PdfRGBColorPickEdit() { RepositoryItemPdfRGBColorPickEdit.RegisterCustomEdit(); }
        public PdfRGBColorPickEdit() { }
        public override string EditorTypeName {
            get { return RepositoryItemPdfRGBColorPickEdit.CustomEditName; }
        }
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Content)]
        public new RepositoryItemPdfRGBColorPickEdit Properties {
            get { return base.Properties as RepositoryItemPdfRGBColorPickEdit; }
        }
        [Browsable(false)]
        public override object EditValue {
            get { return base.EditValue; }
            set { base.EditValue = PdfRGBColorConverter.ToColor(value); }
        }
        public bool ShouldSerializeEditValue() {
            return EditValue != null;
        }
        public void ResetEditValue() {
            EditValue = null;
        }
    }
}
