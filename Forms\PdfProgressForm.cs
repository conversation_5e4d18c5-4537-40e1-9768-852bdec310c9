﻿using System;
using DevExpress.XtraWaitForm;

namespace DevExpress.Docs.Demos.Pdf.Forms {
    public enum WaitFormCommand { CmdId, CancelId }

    public partial class PdfProgressForm : WaitForm {
        bool cancel = false;

        protected override bool HasSystemShadow { get { return true; } }

        public PdfProgressForm() {
            InitializeComponent();
        }
        void CancelClick(object sender, EventArgs e) {
            cancel = true;
        }
        public override void SetCaption(string caption) {
            base.SetCaption(caption);
            waitLabel.Text = caption;
        }
        public override void ProcessCommand(Enum cmd, object arg) {
            WaitFormCommand command = (WaitFormCommand)cmd;
            if(command == WaitFormCommand.CmdId)
                progressBar.EditValue = (int)arg;
            if(command == WaitFormCommand.CancelId && cancel)
                ((PdfTextMarkupAnnotations)arg).CancelAnnotationsRetrieving();
            base.ProcessCommand(cmd, arg);
        }
    }
}
