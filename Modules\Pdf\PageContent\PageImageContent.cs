﻿using System.Drawing;
using DevExpress.Drawing;
using DevExpress.Pdf;

namespace DevExpress.Docs.Demos {
    public class PageImageContent : PageContentData {
        DXImage fImage;

        public DXImage Image {
            get { return fImage; }
            set {
                fImage = value;
                Controller.UpdateDocument();
            }
        }

        public PageImageContent(PdfDocumentPosition position, PageContentController controller) : base(position, controller) {
        }
        public override void FillContent(PdfGraphics graphics, PdfRectangle pageCropBox) {
            if(Image == null)
                graphics.DrawString("No Image", new Font("Microsoft Sans Serif", 12), new SolidBrush(Color.Red), new RectangleF(Rectangle.Left, (float)(pageCropBox.Height - Rectangle.Top), (float)Rectangle.InnerRectangle.Width, (float)Rectangle.InnerRectangle.Height), new PdfStringFormat() { Alignment = PdfStringAlignment.Center, LineAlignment = PdfStringAlignment.Center });
            else
                graphics.DrawImage(Image, new RectangleF(Rectangle.Left, (float)(pageCropBox.Height - Rectangle.Top), (float)Rectangle.InnerRectangle.Width, (float)Rectangle.InnerRectangle.Height));
        }
    }
}
