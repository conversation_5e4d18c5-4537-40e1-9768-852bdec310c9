﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Globalization;

namespace DevExpress.Docs.Demos {
    public class FontConverter : StringConverter {
        public override bool CanConvertFrom(ITypeDescriptorContext context, Type sourceType) {
            return false;
        }
        public override object ConvertTo(ITypeDescriptorContext context, CultureInfo culture, object value, Type destinationType) {
            Font font = value as Font;
            return font.Name + ", Size=" + font.Size + (font.Bold ? ", Bold" : "") + (font.Italic ? ", Italic" : "");
        }
    }
}
