﻿using System.IO;
using System.Net;
using System.Windows.Forms;
using DevExpress.Data.Utils.Registry;
using DevExpress.DXperience.Demos;
using DevExpress.Security;
using DevExpress.XtraEditors;

namespace DevExpress.Docs.Demos {
    public class DemoUtils {
        public static string GetSaveFileName(string filter, string defaulName) {
            using(SaveFileDialog dialog = new SaveFileDialog { Filter = filter, FileName = defaulName }) {
                if(dialog.ShowDialog() == DialogResult.OK)
                    return dialog.FileName;
            }
            return null;
        }
        public static void ShowFile(string fileName, TutorialControlBase control) {
            if(!File.Exists(fileName))
                return;
            DialogResult dResult = XtraMessageBox.Show(control.LookAndFeel, control, "Do you want to open the resulting file?",
                control.TutorialName, MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if(dResult == DialogResult.Yes)
                Data.Utils.SafeProcess.Start(fileName);
        }
        //
        public static string GetRelativePath(string name) {
            name = "Data\\" + name;
            string path = System.Windows.Forms.Application.StartupPath;
            string s = "\\";
            for(int i = 0; i <= 10; i++) {
                if(File.Exists(path + s + name))
                    return (path + s + name);
                else s += "..\\";
            }
            return s;
        }
        public static string GetRelativeDirectoryPath(string name) {
            name = "Data\\" + name;
            string path = Application.StartupPath;
            string s = "\\";
            for(int i = 0; i <= 10; i++) {
                if(Directory.Exists(path + s + name))
                    return (path + s + name);
                else s += "..\\";
            }
            return string.Empty;
        }
        public static void SetConnectionString(System.Data.OleDb.OleDbConnection oleDbConnection, string path) {
            oleDbConnection.ConnectionString = string.Format(@"Provider=Microsoft.Jet.OLEDB.4.0;User ID=Admin;Data Source={0};Mode=Share Deny None;Extended Properties="""";Jet OLEDB:System database="""";Jet OLEDB:Registry Path="""";Jet OLEDB:Database Password="""";Jet OLEDB:Engine Type=5;Jet OLEDB:Database Locking Mode=1;Jet OLEDB:Global Partial Bulk Ops=2;Jet OLEDB:Global Bulk Transactions=1;Jet OLEDB:New Database Password="""";Jet OLEDB:Create System Database=False;Jet OLEDB:Encrypt Database=False;Jet OLEDB:Don't Copy Locale on Compact=False;Jet OLEDB:Compact Without Replica Repair=False;Jet OLEDB:SFP=False", path);
        }
        public static void PreviewDocument(string fileName) {
            string extension = Path.GetExtension(fileName);
            string value = SafeRegistry.GetValue<string>(SafeRegistry.Hive.ClassesRoot, extension, string.Empty, string.Empty);
            string execSwitch = (string.IsNullOrEmpty(value)) ? "/select" : "/p";
            Data.Utils.SafeProcess.Start("explorer.exe", string.Format("{0}, {1}", execSwitch, fileName));
        }
        public static string GetLanguageString() {
            string result = DemoHelper.GetLanguageString(typeof(frmMain).Assembly);
            if(result == "CS")
                result = "C#";
            return result;
        }
        public static string GetTempFileName() {
            return SafePath.GetTempFileName();
        }
        public static void DeleteTempFile(string tmpFileName) {
            if(SafePath.IsExistingTempFile(tmpFileName)) {
                try { File.Delete(tmpFileName); }
                catch { }
            }
        }
    }
}
