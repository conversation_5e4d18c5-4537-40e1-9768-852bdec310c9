﻿using DevExpress.Pdf;
using DevExpress.XtraPdfViewer;

namespace DevExpress.Docs.Demos {
    public static class PageContentType {
        public const string Text = "text";
        public const string Image = "image";
        public const string Shape = "shape";
        public const string Path = "path";
    }
    public abstract class PageContentData : PdfContentData {
        public static PageContentData Create(string contentType, PdfDocumentPosition position, PageContentController controller) {
            if(position != null) {
                switch(contentType) {
                    case PageContentType.Text:
                        return new PageTextContent(position, controller);
                    case PageContentType.Image:
                        return new PageImageContent(position, controller);
                    case PageContentType.Shape:
                        return new PageShapeContent(position, controller);
                    case PageContentType.Path:
                        return new PagePathContent(position, controller);
                }
            }
            return null;
        }
        public virtual PdfPageDragItem CreateDragItem(PdfViewer pdfViewer) {
            return new PdfPageDragItem(pdfViewer, this, Controller);
        }
        public PageContentData(PdfDocumentPosition position, PageContentController controller) : base(position, controller) {
        }
        public abstract void FillContent(PdfGraphics graphics, PdfRectangle pageCropBox);
    }
}
