﻿using System;
using System.ComponentModel;
using System.Globalization;
using DevExpress.Pdf;

namespace DevExpress.Docs.Demos {
    public class PdfButtonWidgetIconOptionsTypeConverter : ExpandableObjectConverter {
        public override object ConvertTo(ITypeDescriptorContext context, CultureInfo culture, object value, Type destinationType) {
            PdfButtonWidgetIconOptions color = value as PdfButtonWidgetIconOptions;
            if(destinationType == typeof(String) && color != null)
                return "{Options}";
            return base.ConvertTo(context, culture, value, destinationType);
        }
    }
}
