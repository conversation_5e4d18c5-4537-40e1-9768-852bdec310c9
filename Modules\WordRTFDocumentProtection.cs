﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Windows.Forms;
using DevExpress.DXperience.Demos;
using DevExpress.XtraEditors;
using DevExpress.XtraPrinting;
using DevExpress.XtraPrintingLinks;
using DevExpress.XtraRichEdit;
using DevExpress.XtraRichEdit.API.Native;

namespace DevExpress.Docs.Demos {
    public partial class WordRTFDocumentProtection : TutorialControlBase {
        const string saveFileDialogFilter =
            "Word Documents (*.docx)|*.docx|" +
            "Word 97-2003 Documents (*.doc)|*.doc|" +
            "Rich Text Format (*.rtf)|*.rtf";
        readonly Dictionary<string, DocumentFormat> fileExtensionToFormat = new Dictionary<string, DocumentFormat>();
        readonly string pathToFile = DemoUtils.GetRelativePath("DocumentForProtection.docx");
        readonly PrintableComponentLinkBase link;
        readonly RichEditDocumentServer documentServer;

        public WordRTFDocumentProtection() {
            InitializeComponent();
            InitializeFileExtensionToFormat();
            documentServer = new RichEditDocumentServer();
            printPreviewControl.PrintingSystem = new PrintingSystem();
            link = new PrintableComponentLinkBase(printPreviewControl.PrintingSystem);
            documentServer.LoadDocument(pathToFile);
            link.Component = documentServer;
            link.CreateDocument();
        }

        void btnProtectAndSave_Click(object sender, EventArgs e) {
            using(SaveFileDialog saveFileDialog = new SaveFileDialog()) {
                saveFileDialog.Filter = saveFileDialogFilter;
                saveFileDialog.FileName = "ProtectedDocument.docx";
                if(saveFileDialog.ShowDialog() == DialogResult.OK)
                    ProtectAndSaveDocument(saveFileDialog.FileName);
            }
        }
        void ProtectAndSaveDocument(string filePath) {
            try {
                using(RichEditDocumentServer server = new RichEditDocumentServer()) {
                    server.LoadDocument(pathToFile);
                    DocumentProtectionType protectionType = cbReadOnly.Checked ? DocumentProtectionType.ReadOnly : DocumentProtectionType.AllowComments;
                    server.Document.Protect(edDocumentPassword.Text, protectionType);
                    string fileExtension = Path.GetExtension(filePath);
                    DocumentFormat format;
                    if(fileExtensionToFormat.TryGetValue(fileExtension, out format))
                        format = DocumentFormat.OpenXml;
                    server.SaveDocument(filePath, format);
                }
                DemoUtils.PreviewDocument(filePath);
            }
            catch(Exception e) {
                XtraMessageBox.Show(e.Message, Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        void edDocumentPassword_TextChanged(object sender, EventArgs e) {
            edConfirmPassword.Enabled = !String.IsNullOrEmpty(edDocumentPassword.Text);
            UpdateProtectAndSaveButtonState();
        }
        void edConfirmPassword_TextChanged(object sender, EventArgs e) {
            UpdateProtectAndSaveButtonState();
        }
        void UpdateProtectAndSaveButtonState() {
            btnProtectAndSave.Enabled = edDocumentPassword.Text == edConfirmPassword.Text;
        }
        void InitializeFileExtensionToFormat() {
            fileExtensionToFormat.Add(".doc", DocumentFormat.Doc);
            fileExtensionToFormat.Add(".docx", DocumentFormat.OpenXml);
            fileExtensionToFormat.Add(".rtf", DocumentFormat.Rtf);
        }
        protected override void Dispose(bool disposing) {
            if(disposing) {
                if(components != null)
                    components.Dispose();
                if(documentServer != null)
                    documentServer.Dispose();
                if(link != null)
                    link.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
