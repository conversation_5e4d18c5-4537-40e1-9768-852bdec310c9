﻿using System;
using System.ComponentModel;
using DevExpress.Pdf;

namespace DevExpress.Docs.Demos {
    public class ListBoxFormFieldData : ArrayBasedFormFieldData {
        bool fMultiSelect;
        int[] fSelectedIndices;

        [Category(BehaviorCategory)]
        public bool MultiSelect {
            get { return fMultiSelect; }
            set {
                fMultiSelect = value;
                UpdateModel();
            }
        }

        [Category(AppearanceCategory)]
        public int[] SelectedIndices {
            get { return fSelectedIndices; }
            set {
                if(Items == null)
                    throw new ArgumentException("Selected value cannot be set if collection is empty.");
                if(value != null) {
                    int length = Items.Length;
                    foreach(int val in value)
                        if(val < 0 || val >= length)
                            throw new ArgumentException(String.Format("Indices should be in range from 0 to {0}.", length - 1));
                }
                fSelectedIndices = value;
                UpdateModel();
            }
        }

        public ListBoxFormFieldData(PdfDocumentPosition position, DocumentFormController controller)
            : base(position, controller) {
        }
        protected override PdfAcroFormChoiceField CreateChoiceField() {
            return new PdfAcroFormListBoxField(Name, PageNumber, Rectangle.InnerRectangle) { MultiSelect = MultiSelect };
        }
        protected override void ClearSelected() {
            SelectedIndices = null;
        }
        protected override void SelectItems(PdfAcroFormChoiceField choiceField) {
            if(SelectedIndices != null)
                foreach(int selectedItem in SelectedIndices)
                    choiceField.SetSelected(selectedItem, true);
        }
    }
}
