﻿using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using DevExpress.Spreadsheet;
using DevExpress.XtraPrinting;
using DevExpress.XtraPrinting.Control;
using DevExpress.XtraPrintingLinks;
using DevExpress.XtraTab;

namespace DevExpress.Docs.Demos {
    public partial class SpreadsheetPreview : UserControl {

        public SpreadsheetPreview() {
            InitializeComponent();
            UpdatePreview();
        }

        public IWorkbook Workbook { get; set; }
        public bool ShouldSerializeWorkbook() {
            return Workbook != null;
        }
        public void ResetWorkbook() {
            Workbook = null;
        }

        [DefaultValue(false)]
        public bool CanShowBorders { get; set; } = false;

        public void UpdatePreview() {
            xtraTabControl1.Visible = false;
            xtraTabControl1.TabPages.Clear();
            if(Workbook != null) {
                int count = Workbook.Worksheets.Count;
                documentViewerSinglePreview.Visible = false;
                if(count < 2)
                    UpdateWorkbookSinglePreview();
                else
                    UpdateMultipleSheetPreview();
            }
        }

        void UpdateWorkbookSinglePreview() {
            documentViewerSinglePreview.Visible = true;
            UpdateDocumentViewerProperties(documentViewerSinglePreview);
            if(documentViewerSinglePreview.PrintingSystem == null)
                documentViewerSinglePreview.PrintingSystem = new PrintingSystemBase();
            PrintableComponentLinkBase link = new PrintableComponentLinkBase(documentViewerSinglePreview.PrintingSystem);
            link.Component = Workbook;
            link.CreateDocument();
            documentViewerSinglePreview.ExecCommand(DevExpress.XtraPrinting.PrintingSystemCommand.ZoomToPageWidth);
        }

        void UpdateMultipleSheetPreview() {
            xtraTabControl1.Visible = true;
            int count = Workbook.Worksheets.Count;
            List<PrintControl> usedPrintControls = new List<PrintControl>();
            for(int i = 0; i < count; i++) {
                Worksheet sheet = Workbook.Worksheets[i];
                PrintControl pc = EnsurePrintControl(i, sheet.Name);
                PrintableComponentLinkBase link = new PrintableComponentLinkBase(pc.PrintingSystem);
                UpdateSheetPreview(sheet, link);
                usedPrintControls.Add(pc);
            }
        }

        void UpdateSheetPreview(Worksheet sheet, PrintableComponentLinkBase link) {
            IBasePrintable printable = sheet as IBasePrintable;
            link.Component = printable;
            link.CreateDocument();
        }

        PrintControl EnsurePrintControl(int i, string caption) {
            XtraTabPageCollection tabPages = xtraTabControl1.TabPages;
            XtraTabPage page = (i < tabPages.Count) ? tabPages[i] : CreateNewTabPage();
            page.Text = caption;
            if(page.Controls.Count == 1 && page.Controls[0] is PrintControl)
                return page.Controls[0] as PrintControl;
            PrintControl pc = new PrintControl();
            pc.PrintingSystem = new PrintingSystemBase();
            pc.Dock = DockStyle.Fill;
            page.Controls.Clear();
            page.Controls.Add(pc);
            UpdatePrintControlProperties(pc);
            return pc;
        }

        void UpdateDocumentViewerProperties(XtraPrinting.Preview.DocumentViewer documentViewer) {
            if(documentViewer == null || CanShowBorders)
                return;
            documentViewer.PageBorderVisibility = DevExpress.DocumentView.PageBorderVisibility.None;
            documentViewer.ShowPageMargins = false;
            if(documentViewer.PrintingSystem != null)
                documentViewer.PrintingSystem.Graph.PageBackColor = Color.Gray;
            documentViewer.ExecCommand(DevExpress.XtraPrinting.PrintingSystemCommand.ZoomToWholePage);
        }

        void UpdatePrintControlProperties(PrintControl pc) {
            if(pc == null || CanShowBorders)
                return;
            pc.PageBorderVisibility = DevExpress.DocumentView.PageBorderVisibility.None;
            pc.ShowPageMargins = false;
            pc.PrintingSystem.Graph.PageBackColor = Color.Gray;
            pc.ExecCommand(DevExpress.XtraPrinting.PrintingSystemCommand.ZoomToWholePage);
        }

        XtraTabPage CreateNewTabPage() {
            XtraTabPage tabPage = new XtraTabPage();
            xtraTabControl1.TabPages.Add(tabPage);
            return tabPage;
        }
    }
}
