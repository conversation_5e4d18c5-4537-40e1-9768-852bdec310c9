﻿using System;
using System.IO;
using System.Windows.Forms;
using DevExpress.Pdf;
using DevExpress.XtraPdfViewer;

namespace DevExpress.Docs.Demos {
    public class PdfDocumentSignerFileHelper : PdfFileHelper, IDisposable {
        readonly PdfViewerFileHelper viewerDocument;
        readonly PdfViewer viewer;
        PdfDocumentSigner signer;

        protected override string Creator {
            get { return ""; }
            set { }
        }
        protected override string Producer {
            get { return ""; }
            set { }
        }

        public PdfDocumentSignerFileHelper(PdfViewer viewer) {
            this.viewer = viewer;
            viewerDocument = new PdfViewerFileHelper(viewer);
        }
        public override void LoadDocument(string path, bool detach) {
            DisposeSigner();
            if(detach) {
                MemoryStream stream = new MemoryStream();
                using(FileStream fileStream = File.OpenRead(path))
                    fileStream.CopyTo(stream);
                signer = new PdfDocumentSigner(stream);
            }
            else
                signer = new PdfDocumentSigner(path);
            viewerDocument.LoadDocument(path, detach);
        }
        public override void LoadDocument(Stream stream) {
            DisposeSigner();
            signer = new PdfDocumentSigner(stream);
            viewerDocument.LoadDocument(stream);
        }
        public void SignDocument(PdfSignatureBuilder builder) {
            if(signer != null) {
                string fileName = ShowFileDialog<SaveFileDialog>();
                if(!String.IsNullOrEmpty(fileName))
                    signer.SaveDocument(fileName, builder);
            }
        }
        protected override void SaveDocument(string filePath, PdfSaveOptions options) {
            if(signer != null)
                signer.SaveDocument(filePath);
        }
        public void Dispose() {
            DisposeSigner();
        }
        void DisposeSigner() {
            if(signer != null)
                signer.Dispose();
        }
    }
}
