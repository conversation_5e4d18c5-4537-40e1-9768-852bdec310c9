﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using DevExpress.DXperience.Demos;
using DevExpress.Office.Services;
using DevExpress.XtraPrintingLinks;
using DevExpress.XtraRichEdit;

namespace DevExpress.Docs.Demos {
    public partial class WordRTFMailMerge : TutorialControlBase {
        readonly PrintableComponentLinkBase link;
        readonly RichEditDocumentServer documentServer;
        readonly System.Data.OleDb.OleDbConnection connection = new System.Data.OleDb.OleDbConnection();
        public WordRTFMailMerge() {
            InitializeComponent();

            documentServer = new RichEditDocumentServer();
            string path = DemoUtils.GetRelativePath("MailMerge.rtf");
            documentServer.LoadDocument(path, DocumentFormat.Rtf);

            printPreviewControl.PrintingSystem = new DevExpress.XtraPrinting.PrintingSystem();
            link = new DevExpress.XtraPrintingLinks.PrintableComponentLinkBase(printPreviewControl.PrintingSystem);
            LoadData();
            documentServer.Options.MailMerge.ViewMergedData = true;
            gridView1.FocusedRowChanged += gridView1_FocusedRowChanged;
            Load += MailMerge_Load;
        }

        void MailMerge_Load(object sender, EventArgs e) {
            ProcessSelectedRows();
        }
        void ProcessSelectedRows() {
            UpdateMailMergeOptions();
            link.Component = documentServer;
            link.CreateDocument();
            printPreviewControl.ExecCommand(XtraPrinting.PrintingSystemCommand.ZoomToPageWidth);
        }
        protected void UpdateMailMergeOptions() {
            int[] selectedRowIndexes = gridView1.GetSelectedRows();
            int count = selectedRowIndexes.Length;
            List<object> selectedRows = new List<object>();
            for(int i = 0; i < count; i++)
                selectedRows.Add(gridView1.GetRow(selectedRowIndexes[i]));
            documentServer.Options.MailMerge.DataSource = selectedRows;
        }
        void gridView1_FocusedRowChanged(object sender, XtraGrid.Views.Base.FocusedRowChangedEventArgs e) {
            ProcessSelectedRows();
        }
        void LoadData() {
            string path = DemoUtils.GetRelativePath("nwind.mdb");
            if(path.Length <= 0)
                return;
            IUriStreamService uriService = (IUriStreamService)documentServer.GetService(typeof(IUriStreamService));
            uriService.RegisterProvider(new DBUriStreamProvider(nwindDataSet.Employees, "Photo"));
            employeesTableAdapter.Connection = new System.Data.OleDb.OleDbConnection();
            DemoUtils.SetConnectionString(employeesTableAdapter.Connection, path);
            DemoUtils.SetConnectionString(connection, path);
            employeesTableAdapter.Fill(nwindDataSet.Employees);
            FillData();
        }
        void FillData() {
            string cmd = @"SELECT Employees.*, Customers.* FROM (Employees INNER JOIN EmployeeCustomers ON Employees.EmployeeID = EmployeeCustomers.EmployeeId) INNER JOIN Customers ON EmployeeCustomers.CustomerId = Customers.CustomerID;";
            System.Data.OleDb.OleDbCommand selectCommand = new System.Data.OleDb.OleDbCommand(cmd, connection);
            System.Data.OleDb.OleDbDataAdapter adapter = new System.Data.OleDb.OleDbDataAdapter(selectCommand);
            DataSet dataSet = new DataSet();
            adapter.Fill(dataSet);
            gridControl1.DataSource = dataSet.Tables[0];
            documentServer.Options.MailMerge.DataSource = dataSet.Tables[0];
        }
    }
    public class DBUriStreamProvider : IUriStreamProvider {
        static readonly string prefix = "dbimg://";
        readonly DataTable table;
        readonly string columnName;

        public DBUriStreamProvider(DataTable table, string columnName) {
            this.table = table;
            this.columnName = columnName;
        }

        #region IUriStreamProvider Members
        Stream IUriStreamProvider.GetStream(string uri) {
            uri = uri.Trim();
            if(!uri.StartsWith(prefix))
                return null;
            string strId = uri.Substring(prefix.Length).Trim();
            int id;
            if(!int.TryParse(strId, out id))
                return null;
            DataRow row = table.Rows.Find(id);
            if(row == null)
                return null;
            byte[] bytes = row[columnName] as byte[];
            if(bytes == null)
                return null;
            return new MemoryStream(bytes);
        }
        #endregion
    }
}
