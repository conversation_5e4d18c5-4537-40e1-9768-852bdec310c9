﻿using DevExpress.Pdf;
using DevExpress.XtraEditors;

namespace DevExpress.Docs.Demos.Pdf.Forms {
    public partial class PdfFindAndMarkupForm : XtraForm {
        public string TextToFind { get { return textEdit.EditValue as string; } }
        public PdfTextMarkupAnnotationType MarkupType { get { return (PdfTextMarkupAnnotationType)cbMarkupType.SelectedIndex; } }

        public PdfFindAndMarkupForm() {
            InitializeComponent();
        }
    }
}
