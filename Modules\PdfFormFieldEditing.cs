﻿using System;
using System.Collections;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using DevExpress.DXperience.Demos;
using DevExpress.Pdf;
using DevExpress.XtraPdfViewer;
using DevExpress.XtraVerticalGrid.Events;
using DevExpress.XtraVerticalGrid.Rows;

namespace DevExpress.Docs.Demos {
    public partial class PdfFormFieldEditing : TutorialControlBase {
        static PdfFormFieldEditing() {
            TypeDescriptor.AddAttributes(typeof(PdfRectangle), new TypeConverterAttribute(typeof(PdfRectangleTypeConverter)));
            TypeDescriptor.AddAttributes(typeof(PdfRGBColor), new TypeConverterAttribute(typeof(PdfRGBColorConverter)));
            TypeDescriptor.AddAttributes(typeof(PdfButtonWidgetIconOptions), new TypeConverterAttribute(typeof(PdfButtonWidgetIconOptionsTypeConverter)));
            TypeDescriptor.AddAttributes(typeof(PdfAcroFormButtonStyle?), new TypeConverterAttribute(typeof(PdfAcroFormButtonStyleConverter)));
        }
        static readonly string demoDocumentPath = DemoUtils.GetRelativePath("FormDemo.pdf");

        PdfFormFieldFacade selectedFormField;
        public override bool NoGap { get { return true; } }
        PdfAcroFormFacade AcroForm { get { return pdfViewer.GetDocumentFacade().AcroForm; } }
        public PdfFormFieldEditing() {
            InitializeComponent();
            string demoSource = DemoUtils.GetTempFileName();
            pdfViewer.LoadDocument(demoDocumentPath);
            pdfViewer.FormFieldGotFocus += PdfViewer_FormFieldGotFocus;
            pdfViewer.FormFieldLostFocus += PdfViewer_FormFieldLostFocus;
            pdfViewer.FormFieldValueChanged += PdfViewer_FormFieldValueChanged;
            pdfViewer.HighlightFormFields = true;
            formFieldPropertyGridControl.DataSourceChanged += FormFieldPropertyGridControl_DataSourceChanged;
            formFieldPropertyGridControl.CellValueChanged += PropertyGridControlValueChanged;
            widgetPropertyGridControl.CellValueChanged += PropertyGridControlValueChanged;
            widgetPropertyGridControl.CustomPropertyDescriptors += WidgetPropertyGridControl_CustomPropertyDescriptors;
        }
        void WidgetPropertyGridControl_CustomPropertyDescriptors(object sender, CustomPropertyDescriptorsEventArgs e) {
            PdfButtonWidgetIconOptions options = e.Source as PdfButtonWidgetIconOptions;
            if(options != null) {
                PropertyDescriptorCollection properties = e.Properties;
                ArrayList list = new ArrayList(properties);
                list.Add(new UnboundRowPropertyDescriptor("Image", typeof(Image), selectedFormField as PdfButtonFormFieldFacade));
                PropertyDescriptor[] result = new PropertyDescriptor[list.Count];
                list.ToArray().CopyTo(result, 0);
                e.Properties = new PropertyDescriptorCollection(result);
            }
        }
        void PdfViewer_FormFieldValueChanged(object sender, PdfFormFieldValueChangedEventArgs args) {
            SetPropertyGridObject(args.FieldName);
        }
        void FormFieldPropertyGridControl_DataSourceChanged(object sender, EventArgs e) {
            BaseRow flags = formFieldPropertyGridControl.GetRowByFieldName("Flags");
            if(flags != null)
                flags.Properties.ReadOnly = true;
            string[] fieldNames = new string[] { "Widgets", "Field", "AcroForm", "Items" };
            foreach(string fieldName in fieldNames) {
                BaseRow row = formFieldPropertyGridControl.GetRowByFieldName(fieldName);
                if(row != null)
                    row.Visible = false;
            }
        }
        void PdfViewer_FormFieldLostFocus(object sender, PdfFormFieldFocusEventArgs e) {
            formFieldPropertyGridControl.SelectedObject = null;
            widgetPropertyGridControl.SelectedObject = null;
            selectedFormField = null;
        }
        void PdfViewer_FormFieldGotFocus(object sender, PdfFormFieldFocusEventArgs e) {
            SetPropertyGridObject(e.FieldName);
        }

        void SetPropertyGridObject(string fieldName) {
            PdfFormFieldFacade field = AcroForm.GetFormField(fieldName);
            formFieldPropertyGridControl.SelectedObject = null;
            formFieldPropertyGridControl.SelectedObject = field;
            widgetPropertyGridControl.SelectedObject = null;
            System.Collections.Generic.IEnumerator<PdfWidgetFacade> enumerator = field.GetEnumerator();
            while(enumerator.MoveNext()) {
                PdfRadioButtonWidgetFacade buttonFacade = enumerator.Current as PdfRadioButtonWidgetFacade;
                if(buttonFacade == null || buttonFacade.IsChecked) {
                    widgetPropertyGridControl.SelectedObject = enumerator.Current;
                    selectedFormField = field;
                    break;
                }
            }
        }

        void PropertyGridControlValueChanged(object sender, CellValueChangedEventArgs e) {
            if(selectedFormField != null)
                selectedFormField.RebuildAppearance();
        }
        void UnsubscribeFromEvents() {
            if(formFieldPropertyGridControl != null) {
                formFieldPropertyGridControl.CellValueChanged -= PropertyGridControlValueChanged;
                formFieldPropertyGridControl.DataSourceChanged -= FormFieldPropertyGridControl_DataSourceChanged;
            }
            if(widgetPropertyGridControl != null)
                widgetPropertyGridControl.CellValueChanged -= PropertyGridControlValueChanged;
        }
        void openBtn_Click(object sender, EventArgs e) {
            pdfViewer.LoadDocument();
            pdfViewer.ZoomMode = PdfZoomMode.PageLevel;
        }
        void saveAsBtn_Click(object sender, EventArgs e) {
            pdfViewer.SaveDocument();
        }
    }
}
