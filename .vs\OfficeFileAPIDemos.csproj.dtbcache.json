{"RootPath": "C:\\Users\\<USER>\\Documents\\DevExpress Demos 25.1\\Components\\Office File API\\CS\\OfficeFileAPIDemos", "ProjectFileName": "OfficeFileAPIDemos.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "AssemblyInfo.cs"}, {"SourceFile": "Forms\\PdfFindAndMarkupForm.cs"}, {"SourceFile": "Forms\\PdfFindAndMarkupForm.Designer.cs"}, {"SourceFile": "Forms\\PdfProgressForm.cs"}, {"SourceFile": "Forms\\PdfProgressForm.Designer.cs"}, {"SourceFile": "Forms\\PdfTextSearchResultsForm.cs"}, {"SourceFile": "Forms\\PdfTextSearchResultsForm.Designer.cs"}, {"SourceFile": "Modules\\PdfClearPageContent.cs"}, {"SourceFile": "Modules\\PdfClearPageContent.Designer.cs"}, {"SourceFile": "Modules\\PdfADocumentConversion.cs"}, {"SourceFile": "Modules\\PdfADocumentConversion.Designer.cs"}, {"SourceFile": "Modules\\Pdf\\FileHelper\\PdfDocumentConverterFileHelper.cs"}, {"SourceFile": "Modules\\Pdf\\PdfFileNameEditor.cs"}, {"SourceFile": "Modules\\Pdf\\PdfRedactAnnotationProperties.cs"}, {"SourceFile": "Modules\\Pdf\\PdfRubberStampAnnotationFacadeWrapper.cs"}, {"SourceFile": "Modules\\PdfRubberStampAnnotations.cs"}, {"SourceFile": "Modules\\PdfRubberStampAnnotations.Designer.cs"}, {"SourceFile": "Modules\\PdfPageContentCreation.cs"}, {"SourceFile": "Modules\\PdfPageContentCreation.Designer.cs"}, {"SourceFile": "Modules\\Pdf\\PageContent\\PageImageContent.cs"}, {"SourceFile": "Modules\\Pdf\\PageContent\\PagePathContent.cs"}, {"SourceFile": "Modules\\Pdf\\PageContent\\PageShapeContent.cs"}, {"SourceFile": "Modules\\Pdf\\PageContent\\PageTextContent.cs"}, {"SourceFile": "Modules\\Pdf\\PdfContentData.cs"}, {"SourceFile": "Modules\\Pdf\\PageContent\\PageContentData.cs"}, {"SourceFile": "Modules\\Pdf\\PageContent\\PageContentController.cs"}, {"SourceFile": "Modules\\Pdf\\PdfPathDragItem.cs"}, {"SourceFile": "Modules\\Pdf\\PdfViewerHelpers.cs"}, {"SourceFile": "Modules\\Pdf\\TypeConverters\\FontConverter.cs"}, {"SourceFile": "Modules\\Pdf\\SignatureAppearanceBuilderForm.cs"}, {"SourceFile": "Modules\\Pdf\\SignatureAppearanceBuilderForm.Designer.cs"}, {"SourceFile": "Modules\\Pdf\\TypeConverters\\PdfAcroFormButtonStyleConverter.cs"}, {"SourceFile": "Modules\\PdfFileAttachmentDemo.cs"}, {"SourceFile": "Modules\\PdfFileAttachmentDemo.Designer.cs"}, {"SourceFile": "Modules\\PdfFormFieldEditing.cs"}, {"SourceFile": "Modules\\PdfFormFieldEditing.Designer.cs"}, {"SourceFile": "Modules\\PdfFormCreation.cs"}, {"SourceFile": "Modules\\PdfFormCreation.Designer.cs"}, {"SourceFile": "Modules\\PdfFormFlattening.cs"}, {"SourceFile": "Modules\\PdfFormFlattening.Designer.cs"}, {"SourceFile": "Modules\\PdfTextMarkupAnnotations.cs"}, {"SourceFile": "Modules\\PdfTextMarkupAnnotations.Designer.cs"}, {"SourceFile": "Modules\\Pdf\\TypeConverters\\PdfRubberStampIconNameTypeConverter.cs"}, {"SourceFile": "Modules\\Pdf\\TypeConverters\\PdfStringFormatConverter.cs"}, {"SourceFile": "Modules\\Pdf\\UnboundRowPropertyDescriptor.cs"}, {"SourceFile": "Modules\\Pdf\\ColorPicker\\PdfRGBColorConverter.cs"}, {"SourceFile": "Modules\\Pdf\\ColorPicker\\PdfRGBColorPickEdit.cs"}, {"SourceFile": "Modules\\Pdf\\ColorPicker\\PdfRGBColorPickEditViewInfo.cs"}, {"SourceFile": "Modules\\Pdf\\ColorPicker\\RepositoryItemPdfRGBColorPickEdit.cs"}, {"SourceFile": "Modules\\Pdf\\DocumentFormController.cs"}, {"SourceFile": "Modules\\Pdf\\FileHelper\\PdfDocumentProcessorAndViewerFileHelper.cs"}, {"SourceFile": "Modules\\Pdf\\FileHelper\\PdfDocumentProcessorFileHelper.cs"}, {"SourceFile": "Modules\\Pdf\\FileHelper\\PdfDocumentSignerFileHelper.cs"}, {"SourceFile": "Modules\\Pdf\\FileHelper\\PdfViewerFileHelper.cs"}, {"SourceFile": "Modules\\Pdf\\FormFieldAppearance.cs"}, {"SourceFile": "Modules\\Pdf\\FormFieldData\\ArrayBasedFormFieldData.cs"}, {"SourceFile": "Modules\\Pdf\\FormFieldData\\CheckBoxFormFieldData.cs"}, {"SourceFile": "Modules\\Pdf\\FormFieldData\\ComboBoxFormFieldData.cs"}, {"SourceFile": "Modules\\Pdf\\FormFieldData\\FormFieldData.cs"}, {"SourceFile": "Modules\\Pdf\\FormFieldData\\ListBoxFormFieldData.cs"}, {"SourceFile": "Modules\\Pdf\\FormFieldData\\SignatureFormFieldData.cs"}, {"SourceFile": "Modules\\Pdf\\FormFieldData\\TextBoxFormFieldData.cs"}, {"SourceFile": "Modules\\Pdf\\PdfPageDragItem.cs"}, {"SourceFile": "Modules\\Pdf\\DragPoint.cs"}, {"SourceFile": "Modules\\Pdf\\FormFieldFontEditor.cs"}, {"SourceFile": "Modules\\Pdf\\PdfContentRectangle.cs"}, {"SourceFile": "Modules\\Pdf\\TypeConverters\\PdfButtonWidgetIconOptionsTypeConverter.cs"}, {"SourceFile": "Modules\\Pdf\\TypeConverters\\PdfRectangleTypeConverter.cs"}, {"SourceFile": "Modules\\Pdf\\TypeConverters\\PdfRGBColorTypeConverter.cs"}, {"SourceFile": "Modules\\SpreadsheetAccessiblePDF.cs"}, {"SourceFile": "Modules\\SpreadsheetAccessiblePDF.Designer.cs"}, {"SourceFile": "Modules\\SpreadsheetDocumentEncryption.cs"}, {"SourceFile": "Modules\\SpreadsheetDocumentEncryption.Designer.cs"}, {"SourceFile": "Modules\\PdfPasswordProtection.cs"}, {"SourceFile": "Modules\\PdfPasswordProtection.Designer.cs"}, {"SourceFile": "Modules\\PdfSignatureDemo.cs"}, {"SourceFile": "Modules\\PdfSignatureDemo.Designer.cs"}, {"SourceFile": "Modules\\PdfFormFilling.cs"}, {"SourceFile": "Modules\\PdfFormFilling.Designer.cs"}, {"SourceFile": "Modules\\PdfPageMerging.cs"}, {"SourceFile": "Modules\\PdfPageMerging.Designer.cs"}, {"SourceFile": "Modules\\SpreadsheetDocumentConversion.cs"}, {"SourceFile": "Modules\\SpreadsheetDocumentConversion.Designer.cs"}, {"SourceFile": "Modules\\SpreadsheetDocumentProtection.cs"}, {"SourceFile": "Modules\\SpreadsheetDocumentProtection.Designer.cs"}, {"SourceFile": "Modules\\SpreadsheetDocumentMerging.cs"}, {"SourceFile": "Modules\\SpreadsheetDocumentMerging.Designer.cs"}, {"SourceFile": "Modules\\Pdf\\FileHelper\\PdfFileHelper.cs"}, {"SourceFile": "Forms\\PdfTextExtractionResultsForm.cs"}, {"SourceFile": "Forms\\PdfTextExtractionResultsForm.Designer.cs"}, {"SourceFile": "Modules\\SpreadsheetDocumentSignatureDemo.cs"}, {"SourceFile": "Modules\\SpreadsheetDocumentSignatureDemo.Designer.cs"}, {"SourceFile": "Modules\\SpreadsheetPreview.cs"}, {"SourceFile": "Modules\\SpreadsheetPreview.Designer.cs"}, {"SourceFile": "DemosRegistration.cs"}, {"SourceFile": "DemoUtils.cs"}, {"SourceFile": "Forms\\PasswordForm.cs"}, {"SourceFile": "Forms\\PasswordForm.Designer.cs"}, {"SourceFile": "frmMain.cs"}, {"SourceFile": "Modules\\About.cs"}, {"SourceFile": "Modules\\About.Designer.cs"}, {"SourceFile": "Modules\\WordRTFAcroFormPDF.cs"}, {"SourceFile": "Modules\\WordRTFAcroFormPDF.Designer.cs"}, {"SourceFile": "Modules\\WordRTFAccessiblePDF.cs"}, {"SourceFile": "Modules\\WordRTFAccessiblePDF.Designer.cs"}, {"SourceFile": "Modules\\WordRtfDocumentWatermark.cs"}, {"SourceFile": "Modules\\WordRtfDocumentWatermark.Designer.cs"}, {"SourceFile": "Modules\\WordCompareDocuments.cs"}, {"SourceFile": "Modules\\WordCompareDocuments.Designer.cs"}, {"SourceFile": "Modules\\WordRTFDocumentEncryption.cs"}, {"SourceFile": "Modules\\WordRTFDocumentEncryption.Designer.cs"}, {"SourceFile": "Modules\\WordRTFDocumentProtection.cs"}, {"SourceFile": "Modules\\WordRTFDocumentProtection.Designer.cs"}, {"SourceFile": "Modules\\WordRTFFindAndReplace.cs"}, {"SourceFile": "Modules\\WordRTFFindAndReplace.Designer.cs"}, {"SourceFile": "Modules\\MailMerge\\DocumentGenerator.cs"}, {"SourceFile": "Modules\\SpreadsheetDocumentTemplate.cs"}, {"SourceFile": "Modules\\SpreadsheetDocumentTemplate.Designer.cs"}, {"SourceFile": "Modules\\SpreadsheetMailMerge.cs"}, {"SourceFile": "Modules\\SpreadsheetMailMerge.Designer.cs"}, {"SourceFile": "Modules\\WordRTFDocumentConversion.cs"}, {"SourceFile": "Modules\\WordRTFDocumentConversion.Designer.cs"}, {"SourceFile": "Modules\\WordRTFMailMerge.cs"}, {"SourceFile": "Modules\\WordRTFMailMerge.Designer.cs"}, {"SourceFile": "Modules\\Utils.cs"}, {"SourceFile": "Modules\\WordRTFSignatureDemo.cs"}, {"SourceFile": "Modules\\WordRTFSignatureDemo.Designer.cs"}, {"SourceFile": "nwindDataSet.Designer.cs"}, {"SourceFile": "Modules\\Pdf\\PdfTutorialControl.cs"}, {"SourceFile": "Modules\\Pdf\\PdfTutorialControl.Designer.cs"}, {"SourceFile": "Program.cs"}, {"SourceFile": "Properties\\Resources.Designer.cs"}, {"SourceFile": "Properties\\Settings.Designer.cs"}, {"SourceFile": "Modules\\SignatureTutorialControl.cs"}, {"SourceFile": "obj_netFW\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"}], "References": [{"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.BonusSkins.v25.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.CodeParser.v25.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.Data.Desktop.v25.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.Data.v25.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.DataVisualization.v25.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.DemoData.v25.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.DevAV.v25.1.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.Docs.v25.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.Drawing.v25.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.Office.v25.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.Pdf.v25.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.Pdf.v25.1.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.Printing.v25.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.RichEdit.v25.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.Spreadsheet.v25.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.TreeMap.v25.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.Tutorials.v25.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.Utils.v25.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.XtraBars.v25.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.XtraEditors.v25.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.XtraGrid.v25.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.XtraLayout.v25.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.XtraNavBar.v25.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.XtraPdfViewer.v25.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.XtraPrinting.v25.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.XtraRichEdit.v25.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.XtraSpreadsheet.v25.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.XtraTreeList.v25.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.XtraTreeMap.v25.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 25.1\\Components\\Bin\\Framework\\DevExpress.XtraVerticalGrid.v25.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Microsoft.Build.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Configuration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Design.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "C:\\Users\\<USER>\\Documents\\DevExpress Demos 25.1\\Components\\Office File API\\CS\\OfficeFileAPIDemos\\bin\\Debug\\OfficeFileAPIDemos.exe", "OutputItemRelativePath": "OfficeFileAPIDemos.exe"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}