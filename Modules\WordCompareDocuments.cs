﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using DevExpress.DXperience.Demos;
using DevExpress.XtraEditors;
using DevExpress.XtraPrinting;
using DevExpress.XtraPrintingLinks;
using DevExpress.XtraRichEdit;
using DevExpress.XtraRichEdit.API.Native;

namespace DevExpress.Docs.Demos {
    public partial class WordCompareDocuments: TutorialControlBase {
        const string fileDialogFilter =
        "Word Documents (*.docx)|*.docx|" +
        "Word 97-2003 Documents (*.doc)|*.doc|" +
        "Rich Text Format (*.rtf)|*.rtf";
        readonly string pathToFileOriginal = DemoUtils.GetRelativePath("CompareDoc_Original.docx");
        readonly string pathToFileRevised = DemoUtils.GetRelativePath("CompareDoc_Revised.docx");
        readonly Dictionary<string, DocumentFormat> fileExtensionToFormat = new Dictionary<string, DocumentFormat>();
        readonly PrintableComponentLinkBase linkOrigin;
        readonly RichEditDocumentServer documentServerOrigin;
        readonly PrintableComponentLinkBase linkRevised;
        readonly RichEditDocumentServer documentServerRevised;
        readonly PrintableComponentLinkBase linkResult;
        readonly RichEditDocumentServer documentServerResult;

        public WordCompareDocuments() {
            InitializeComponent();
            InitializeDemo();
            InitializeFileExtensionToFormat();
            //origin
            documentServerOrigin = new RichEditDocumentServer();
            printPreviewControlOriginal.PrintingSystem = new PrintingSystem();
            linkOrigin = new PrintableComponentLinkBase(printPreviewControlOriginal.PrintingSystem);
            documentServerOrigin.LoadDocument(pathToFileOriginal);
            linkOrigin.Component = documentServerOrigin;
            linkOrigin.CreateDocument();
            //revised
            documentServerRevised = new RichEditDocumentServer();
            printPreviewControlRevised.PrintingSystem = new PrintingSystem();
            linkRevised = new PrintableComponentLinkBase(printPreviewControlRevised.PrintingSystem);
            documentServerRevised.LoadDocument(pathToFileRevised);
            linkRevised.Component = documentServerRevised;
            linkRevised.CreateDocument();
            //result
            documentServerResult = new RichEditDocumentServer();
            printPreviewControlResult.PrintingSystem = new PrintingSystem();
            linkResult = new PrintableComponentLinkBase(printPreviewControlResult.PrintingSystem);
            CompareAndLoadResultDocument();
        }
        void CompareAndLoadResultDocument() {
            CompareDocumentOptions options = new CompareDocumentOptions();
            options.CompareHeadersAndFooters = checkHeadersFooters.Checked;
            options.CompareTextBoxes = checkTextBoxes.Checked;
            options.CompareCaseChanges = chckedtCaseChanges.Checked;
            options.CompareFormatting = chckedtFormatting.Checked;
            switch(cmbboxComparisonLevel.Text) {
                case "Word":
                    options.ComparisonLevel = ComparisonLevel.Word;
                    break;
                case "Character":
                    options.ComparisonLevel = ComparisonLevel.Character;
                    break;
                default:
                    break;
            }
            XtraRichEdit.API.Native.Document result = documentServerOrigin.Document.Compare(documentServerRevised.Document, options);
            byte[] buffer;
            using(MemoryStream memoryStream = new MemoryStream()) {
                result.SaveDocument(memoryStream, DocumentFormat.OpenXml);
                buffer = memoryStream.ToArray();
            }
            documentServerResult.LoadDocument(buffer);
            TrackChangesOptions trackChangesOptions = documentServerResult.Options.Annotations.TrackChanges;
            trackChangesOptions.DisplayForReviewMode = DisplayForReviewMode.AllMarkup;
            linkResult.Component = documentServerResult;
            linkResult.CreateDocument();
        }
        void InitializeDemo() {
            cmbboxComparisonLevel.Properties.Items.Add("Character");
            cmbboxComparisonLevel.Properties.Items.Add("Word");
            cmbboxComparisonLevel.EditValue = cmbboxComparisonLevel.Properties.Items[0];
        }
        void InitializeFileExtensionToFormat() {
            fileExtensionToFormat.Add(".doc", DocumentFormat.Doc);
            fileExtensionToFormat.Add(".docx", DocumentFormat.OpenXml);
            fileExtensionToFormat.Add(".rtf", DocumentFormat.Rtf);
        }

        private void OnButtonLoadRevisedDocumentClick(object sender, EventArgs e) {
            using(OpenFileDialog openFileDialog = new OpenFileDialog()) {
                openFileDialog.Filter = fileDialogFilter;
                if(openFileDialog.ShowDialog() == DialogResult.OK) {
                    try {
                        documentServerRevised.LoadDocument(openFileDialog.FileName);
                        linkRevised.Component = documentServerRevised;
                        linkRevised.CreateDocument();
                    }
                    catch(Exception ex) {
                        XtraMessageBox.Show(ex.Message, Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            tabControl1.SelectTab(1);
        }
        private void OnButtonLoadOriginalDocumentClick(object sender, EventArgs e) {
            using(OpenFileDialog openFileDialog = new OpenFileDialog()) {
                openFileDialog.Filter = fileDialogFilter;
                if(openFileDialog.ShowDialog() == DialogResult.OK) {
                    try {
                        documentServerOrigin.LoadDocument(openFileDialog.FileName);
                        linkOrigin.Component = documentServerOrigin;
                        linkOrigin.CreateDocument();
                    }
                    catch(Exception ex) {
                        XtraMessageBox.Show(ex.Message, Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            tabControl1.SelectTab(0);
        }

        private void OnButtonCompareDocumentClick(object sender, EventArgs e) {
            CompareAndLoadResultDocument();
            tabControl1.SelectTab(2);
        }

        private void OnButtonSaveResultDocumentClick(object sender, EventArgs e) {
            using(SaveFileDialog saveFileDialog = new SaveFileDialog()) {
                saveFileDialog.Filter = fileDialogFilter;
                saveFileDialog.FileName = "CompareDocuments.docx";
                if(saveFileDialog.ShowDialog() == DialogResult.OK)
                    SaveDocument(saveFileDialog.FileName);
            }
        }
        void SaveDocument(string filePath) {
            try {
                DocumentFormat format;
                string fileExtension = Path.GetExtension(filePath);
                if(fileExtensionToFormat.TryGetValue(fileExtension, out format))
                    format = DocumentFormat.OpenXml;
                documentServerResult.Document.SaveDocument(filePath, DocumentFormat.Docx);
                DemoUtils.PreviewDocument(filePath);
            }
            catch(Exception e) {
                XtraMessageBox.Show(e.Message, Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
