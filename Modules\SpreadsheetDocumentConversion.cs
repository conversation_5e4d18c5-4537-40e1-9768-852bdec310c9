﻿using System;
using System.Globalization;
using System.IO;
using System.Windows.Forms;
using DevExpress.DXperience.Demos;
using DevExpress.Spreadsheet;
using DevExpress.XtraEditors;
using DevExpress.XtraPrinting;

namespace DevExpress.Docs.Demos {
    public partial class SpreadsheetDocumentConversion : TutorialControlBase {
        const string openFileDialogFilter =
            "All Supported Files (*.xlsx;*.xlsm;*.xlsb;*.xls;*.xltx;*.xltm;*.xlt;*.xml;*.csv;*.txt)|*.xlsx;*.xlsm;*.xlsb;*.xls;*.xltx;*.xltm;*.xlt;*.xml;*.csv;*.txt|" +
            "Excel Workbook (*.xlsx)|*.xlsx|" +
            "Excel Macro-Enabled Workbook (*.xlsm)|*.xlsm|" +
            "Excel Binary Workbook (*.xlsb)|*.xlsb|" +
            "Excel 97-2003 Workbook (*.xls)|*.xls|" +
            "Excel Template (*.xltx)|*.xltx|" +
            "Excel Macro-Enabled Template (*.xltm)|*.xltm|" +
            "Excel 97-2003 Template (*.xlt)|*.xlt|" +
            "XML Spreadsheet 2003 (*.xml)|*.xml|" +
            "CSV (Comma Delimited) (*.csv)|*.csv|" +
            "Text (Tab Delimited) (*.txt)|*.txt|" +
            "All files (*.*)|*.*";

        IWorkbook workbook;

        public SpreadsheetDocumentConversion() {
            InitializeComponent();
            spreadsheetPreview1.CanShowBorders = true;
            edFilePath.Text = DemoUtils.GetRelativePath("BreakevenAnalysis.xlsx");
            edSaveTo.Text = DevExpress.Data.Utils.SafeEnvironment.MyDocuments;
            LoadDocument();
        }

        void LoadDocument() {
            if(workbook == null)
                workbook = new Workbook();
            try {
                workbook.LoadDocument(edFilePath.Text);
                workbook.Worksheets[0].PrintOptions.FitToPage = true;
                spreadsheetPreview1.Workbook = workbook;
                spreadsheetPreview1.UpdatePreview();
            }
            catch(Exception ex) {
                XtraMessageBox.Show(ex.Message, Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        void ConvertTo_Click(object sender, EventArgs e) {
            try {
                string format = ((Control)sender).Tag.ToString();
                string filePath = edFilePath.Text;
                string fileName = Path.GetFileNameWithoutExtension(filePath);
                string pathString = Path.Combine(edSaveTo.Text, fileName);
                string resultFilePath = String.Empty;
                if(format == "xlsx") {
                    resultFilePath = pathString + ".xlsx";
                    workbook.SaveDocument(resultFilePath, DocumentFormat.Xlsx);
                }
                else if(format == "xlsm") {
                    resultFilePath = pathString + ".xlsm";
                    workbook.SaveDocument(resultFilePath, DocumentFormat.Xlsm);
                }
                else if(format == "xlsb") {
                    resultFilePath = pathString + ".xlsb";
                    workbook.SaveDocument(resultFilePath, DocumentFormat.Xlsb);
                }
                else if(format == "xls") {
                    resultFilePath = pathString + ".xls";
                    workbook.SaveDocument(resultFilePath, DocumentFormat.Xls);
                }
                else if(format == "xltx") {
                    resultFilePath = pathString + ".xltx";
                    workbook.SaveDocument(resultFilePath, DocumentFormat.Xltx);
                }
                else if(format == "xltm") {
                    resultFilePath = pathString + ".xltm";
                    workbook.SaveDocument(resultFilePath, DocumentFormat.Xltm);
                }
                else if(format == "xlt") {
                    resultFilePath = pathString + ".xlt";
                    workbook.SaveDocument(resultFilePath, DocumentFormat.Xlt);
                }
                else if(format == "xml") {
                    resultFilePath = pathString + ".xml";
                    workbook.SaveDocument(resultFilePath, DocumentFormat.XmlSpreadsheet2003);
                }
                else if(format == "csv") {
                    resultFilePath = pathString + ".csv";
                    workbook.Options.Export.Csv.Culture = CultureInfo.CurrentCulture;
                    workbook.SaveDocument(resultFilePath, DocumentFormat.Csv);
                }
                else if(format == "txt") {
                    resultFilePath = pathString + ".txt";
                    workbook.Options.Export.Txt.Culture = CultureInfo.CurrentCulture;
                    workbook.SaveDocument(resultFilePath, DocumentFormat.Text);
                }
                else if(format == "pdf") {
                    resultFilePath = pathString + ".pdf";
                    PdfExportOptions options = new PdfExportOptions();
                    options.ConvertImagesToJpeg = false;
                    workbook.ExportToPdf(resultFilePath, options);
                }
                else if(format == "html") {
                    resultFilePath = pathString + ".html";
                    workbook.ExportToHtml(resultFilePath, 0);
                }
                if(!string.IsNullOrEmpty(resultFilePath))
                    DemoUtils.PreviewDocument(resultFilePath);
            }
            catch(Exception ex) {
                XtraMessageBox.Show(ex.Message, Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        void edFilePath_ButtonClick(object sender, XtraEditors.Controls.ButtonPressedEventArgs e) {
            ChooseFileToOpen(String.Empty);
        }

        void edFilePath_KeyUp(object sender, KeyEventArgs e) {
            if(e.KeyCode == Keys.Enter) {
                FileInfo fileInfo = new FileInfo(edFilePath.Text);
                if(fileInfo.Exists)
                    LoadDocument();
                else
                    ChooseFileToOpen(edFilePath.Text);
            }
        }

        void edSaveTo_ButtonClick(object sender, XtraEditors.Controls.ButtonPressedEventArgs e) {
            using(FolderBrowserDialog folderBrowserDialog = new FolderBrowserDialog()) {
                if(folderBrowserDialog.ShowDialog() == DialogResult.OK)
                    edSaveTo.Text = folderBrowserDialog.SelectedPath;
            }
        }

        void ChooseFileToOpen(string initialPath) {
            using(OpenFileDialog openFileDialog = new OpenFileDialog()) {
                openFileDialog.Filter = openFileDialogFilter;
                if(!string.IsNullOrEmpty(initialPath))
                    openFileDialog.InitialDirectory = initialPath;
                if(openFileDialog.ShowDialog() == DialogResult.OK) {
                    edFilePath.Text = openFileDialog.FileName;
                    LoadDocument();
                }
            }
        }

    }
}
