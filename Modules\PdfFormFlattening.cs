﻿using System;
using System.IO;
using System.Windows.Forms;
using DevExpress.DXperience.Demos;
using DevExpress.Pdf;
using DevExpress.XtraEditors;
using DevExpress.XtraPdfViewer;

namespace DevExpress.Docs.Demos {
    public partial class PdfFormFlattening : TutorialControlBase {
        static readonly string flattenDemoDocumentName = "FormDemo.pdf";

        readonly PdfDocumentProcessor documentProcessor;
        readonly PdfDocumentProcessorAndViewerFileHelper fileHelper;

        public override bool NoGap { get { return true; } }

        public PdfFormFlattening() {
            InitializeComponent();
            documentProcessor = new PdfDocumentProcessor();
            fileHelper = new PdfDocumentProcessorAndViewerFileHelper(documentProcessor, pdfViewer);
            Enabled = fileHelper.LoadDemoDocument(flattenDemoDocumentName, true);
        }
        void FlattenFormFieldsClick(object sender, EventArgs e) {
            using(Stream sourceStream = new MemoryStream()) {
                try {
                    pdfViewer.SaveDocument(sourceStream);
                    documentProcessor.LoadDocument(sourceStream);
                    documentProcessor.FlattenForm();
                    using(Stream stream = new MemoryStream()) {
                        documentProcessor.SaveDocument(stream, true);
                        stream.Position = 0;
                        pdfViewer.LoadDocument(stream);
                    }
                }
                catch {
                    XtraMessageBox.Show("An error occurred while saving the PDF document", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
        }
        void DocumentClosing(object sender, PdfDocumentClosingEventArgs e) {
        }
        void OpenClick(object sender, EventArgs e) {
            fileHelper.LoadDocumentWithDialog();
        }
        void SaveClick(object sender, EventArgs e) {
            fileHelper.SaveDocument();
        }
        void LoadDemoDocumentClick(object sender, EventArgs e) {
            Enabled = fileHelper.LoadDemoDocument(flattenDemoDocumentName, true);
        }
    }
}
