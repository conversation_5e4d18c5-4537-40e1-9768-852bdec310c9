﻿using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;

namespace DevExpress.Docs.Demos {
    public partial class PasswordForm : XtraForm {
        const string descriptionFmt = "File '{0}' is password protected. Please enter the password in the box below.";
        //
        public PasswordForm(string fileName) {
            InitializeComponent();
            labelDescription.Text = string.Format(descriptionFmt, fileName.Length < 20 ? fileName : fileName.Substring(0, 17) + "...");
            buttonOK.Enabled = false;
        }
        public string Password {
            get { return textEditPassword.Text; }
        }
        void tbPassword_EditValueChanging(object sender, ChangingEventArgs e) {
            buttonOK.Enabled = !string.IsNullOrEmpty(e.NewValue as string);
        }
    }
}
