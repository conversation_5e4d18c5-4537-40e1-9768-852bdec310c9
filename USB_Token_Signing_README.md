# USB Token Digital Signature Support for PdfSignatureDemo

This document describes the new PKCS#11 USB token support added to the PdfSignatureDemo class in the DevExpress Office File API Demo application.

## Overview

The enhanced PdfSignatureDemo now supports digital signing of PDF documents using private keys stored on PKCS#11 compatible USB tokens, in addition to the existing PFX file support.

## Features Added

### 1. USB Token Detection
- Automatic detection of available USB tokens with certificates
- Display of USB tokens in the certificate selection list
- Separation between PFX files and USB tokens in the UI

### 2. PKCS#11 Signer Implementation
- Custom `Pkcs11Signer` class that inherits from `Pkcs7SignerBase`
- Support for hardware-based private key operations
- Integration with DevExpress PDF signature framework

### 3. PIN Entry
- Secure PIN entry dialog (`PinEntryForm`)
- Password masking for security
- Validation and error handling

### 4. Certificate Management
- Retrieval of certificates from USB tokens
- Support for multiple certificates on a single token
- Automatic certificate chain handling

## Technical Implementation

### Key Classes

#### `Pkcs11Signer`
A custom signer class that extends `Pkcs7SignerBase` to support PKCS#11 USB tokens:

```csharp
public class Pkcs11Signer : Pkcs7SignerBase {
    // Handles USB token communication and signing operations
    // Integrates with PKCS#11 libraries for hardware security modules
}
```

#### `UsbTokenItem`
Represents a USB token in the certificate selection list:

```csharp
public class UsbTokenItem {
    public string SlotDescription { get; set; }
    public string TokenLabel { get; set; }
    public uint SlotId { get; set; }
    public string DisplayName => $"{TokenLabel} (Slot: {SlotId})";
}
```

#### `PinEntryForm`
A secure dialog for PIN entry:

```csharp
public partial class PinEntryForm : XtraForm {
    public string Pin { get; private set; }
    // Secure PIN input with validation
}
```

### Integration Points

1. **Constructor Enhancement**: Added USB token detection during initialization
2. **Signing Logic**: Modified `OnButtonSignClick` to handle both PFX and USB token signing
3. **Appearance Builder**: Updated to work with USB token certificates
4. **Certificate Detection**: Added methods to detect and enumerate USB tokens

## Usage Instructions

### Prerequisites

1. **PKCS#11 Library**: Install the PKCS#11 library provided by your USB token manufacturer
2. **USB Token**: Insert your PKCS#11 compatible USB token
3. **Certificate**: Ensure your signing certificate is installed on the USB token

### Steps to Sign with USB Token

1. **Launch the Application**: Start the DevExpress Office File API Demo
2. **Navigate to PDF Signature Demo**: Select the PDF signature demonstration module
3. **Token Detection**: Available USB tokens will appear in the certificate list under "--- USB Tokens ---"
4. **Select Token**: Choose your USB token from the list
5. **Configure Signature**: Set location, contact info, reason, and other signature parameters
6. **Sign Document**: Click "Sign" button
7. **Enter PIN**: When prompted, enter your USB token PIN
8. **Save**: Choose location to save the signed PDF

### Configuration

#### PKCS#11 Library Path
Update the `GetPkcs11LibraryPath()` method to return the correct path to your PKCS#11 library:

```csharp
string GetPkcs11LibraryPath() {
    // Examples of common PKCS#11 library paths:
    // return "C:\\Windows\\System32\\eTPKCS11.dll";  // eToken
    // return "C:\\Program Files\\Gemalto\\Classic Client\\BIN\\gclib.dll";  // Gemalto
    // return "C:\\Program Files\\TokenVendor\\pkcs11.dll";  // Generic
    return "your-pkcs11-library-path.dll";
}
```

## Security Considerations

1. **PIN Protection**: PINs are handled securely and not stored in memory longer than necessary
2. **Hardware Security**: Private keys remain on the USB token and are never exposed
3. **Certificate Validation**: Certificates are validated before use
4. **Error Handling**: Comprehensive error handling for token communication failures

## Limitations and Notes

1. **PKCS#11 Library Dependency**: Requires the appropriate PKCS#11 library for your USB token
2. **Token Compatibility**: Tested with standard PKCS#11 compatible tokens
3. **Certificate Store Integration**: Currently uses Windows Certificate Store for certificate enumeration
4. **Demo Implementation**: The current implementation includes placeholder code that should be replaced with actual PKCS#11 library calls for production use

## Future Enhancements

1. **Full PKCS#11 Integration**: Replace placeholder code with complete PKCS#11 library integration
2. **Multiple Token Support**: Enhanced support for multiple simultaneous tokens
3. **Certificate Filtering**: Advanced filtering options for certificates
4. **Token Management**: Additional token management features

## Troubleshooting

### Common Issues

1. **Token Not Detected**: Ensure PKCS#11 library is installed and token is properly inserted
2. **PIN Errors**: Verify PIN is correct and token is not locked
3. **Certificate Issues**: Check that signing certificate is properly installed on token
4. **Library Path**: Verify PKCS#11 library path is correct in `GetPkcs11LibraryPath()`

### Error Messages

- "Unable to sign with USB token. PKCS#11 library integration required.": Indicates need for full PKCS#11 implementation
- "Could not retrieve certificate from USB token.": Certificate enumeration failed
- "PIN entry was cancelled.": User cancelled PIN entry dialog

## Support

For additional support with USB token integration:

1. Consult your USB token manufacturer's documentation
2. Verify PKCS#11 library compatibility
3. Check DevExpress documentation for digital signature APIs
4. Review the implementation code for customization points

## Example Code

See the modified `PdfSignatureDemo.cs` file for complete implementation details, including:
- USB token detection logic
- PKCS#11 signer implementation
- PIN entry handling
- Certificate management
- Error handling and validation
