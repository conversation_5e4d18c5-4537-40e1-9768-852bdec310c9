﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using DevExpress.Pdf;
using DevExpress.XtraPdfViewer;

namespace DevExpress.Docs.Demos {
    public interface IDocumentController {
        int PageCount { get; }
        void UpdateDocument();
    }
    public class DocumentFormController : IDisposable, IDocumentController {
        const string prefix = "dxFormField";

        readonly string editingTempFilePath;
        readonly string sourceTempFilePath;
        readonly IList<FormFieldData> fields = new List<FormFieldData>();
        readonly HashSet<string> existingNames = new HashSet<string>();
        readonly PdfViewer pdfViewer;
        readonly Pen inactive = new Pen(SystemColors.ActiveBorder) { DashStyle = DashStyle.Dash };
        readonly Pen highlight = new Pen(SystemColors.Highlight, 2) { DashStyle = DashStyle.Dash };

        int counter = 1;
        public int PageCount {
            get { return pdfViewer.PageCount; }
        }
        public DocumentFormController(PdfViewer pdfViewer, string sourceTempFilePath, string editingTempFilePath) {
            this.pdfViewer = pdfViewer;
            this.sourceTempFilePath = sourceTempFilePath;
            this.editingTempFilePath = editingTempFilePath;
            using(PdfDocumentProcessor processor = new PdfDocumentProcessor()) {
                processor.LoadDocument(sourceTempFilePath);
                PdfInteractiveForm form = processor.Document.AcroForm;
                if(form != null)
                    foreach(PdfInteractiveFormField field in form.Fields)
                        existingNames.Add(field.Name);
            }
        }
        public void UpdateDocument() {
            float scrollPosition = pdfViewer.VerticalScrollPosition;
            pdfViewer.CloseDocument();
            try {
                using(PdfDocumentProcessor processor = new PdfDocumentProcessor()) {
                    processor.LoadDocument(sourceTempFilePath);
                    List<PdfAcroFormField> acroFields = new List<PdfAcroFormField>(fields.Count);
                    foreach(FormFieldData item in fields)
                        acroFields.Add(item.CreateAcroFormField());
                    processor.AddFormFields(acroFields.ToArray());
                    processor.SaveDocument(editingTempFilePath);
                }
            }
            finally {
                new PdfViewerFileHelper(pdfViewer).LoadDocument(editingTempFilePath);
                pdfViewer.VerticalScrollPosition = scrollPosition;
            }
        }
        public string GetNextName() {
            string name;
            do {
                name = prefix + counter;
                counter++;
            }
            while(existingNames.Contains(name));
            return name;
        }
        public void AddField(FormFieldData field) {
            existingNames.Add(field.Name);
            fields.Add(field);
            UpdateDocument();
        }
        public void RemoveField(FormFieldData field) {
            fields.Remove(field);
            existingNames.Remove(field.Name);
            UpdateDocument();
        }
        public FormFieldData GetFormFieldFromPoint(Point point) {
            foreach(FormFieldData item in fields)
                if(item.ContainsPosition(pdfViewer.GetDocumentPosition(point)))
                    return item;
            return null;
        }
        public void RemoveExistingForm() {
            using(PdfDocumentProcessor processor = new PdfDocumentProcessor()) {
                processor.LoadDocument(sourceTempFilePath);
                processor.RemoveForm();
                processor.SaveDocument(sourceTempFilePath);
            }
            UpdateDocument();
        }
        public void Draw(Graphics graphics) {
            foreach(FormFieldData item in fields)
                DrawItem(graphics, item.GetClientRectangle(pdfViewer));
        }
        public void DrawSelectedItem(Graphics graphics, Rectangle itemRect) {
            graphics.DrawRectangle(highlight, itemRect);
        }
        public void DrawItem(Graphics graphics, Rectangle itemRect) {
            graphics.DrawRectangle(inactive, itemRect);
        }
        public void Dispose() {
            foreach(FormFieldData field in fields)
                field.Dispose();
            fields.Clear();
            inactive.Dispose();
            highlight.Dispose();
        }
    }
}
