﻿using System.IO;
namespace DevExpress.Docs.Demos {
    partial class PdfRubberStampAnnotations {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing) {
            if (disposing) {
                UnsubscribeFromEvents();
                DemoUtils.DeleteTempFile(editingTempFilePath);
                if (components != null)
                    components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent() {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(PdfRubberStampAnnotations));
            DevExpress.Utils.SuperToolTip superToolTip3 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipTitleItem toolTipTitleItem3 = new DevExpress.Utils.ToolTipTitleItem();
            DevExpress.Utils.SuperToolTip superToolTip4 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipTitleItem toolTipTitleItem4 = new DevExpress.Utils.ToolTipTitleItem();
            this.accordionControl = new DevExpress.XtraBars.Navigation.AccordionControl();
            this.accordionControlFileElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlNewElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlOpenElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlSaveElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlFormFieldsElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlDConfidentialElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlDApprovedElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlDRevisedElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlDReviewedElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlDReceivedElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlSignHereElements = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlSignHereElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlInitialHereElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlWitnessHereElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlSHAcceptedElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlSHRejectedElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlStandardElements = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlDraftElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlApprovedElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlNotApprovedElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlAsIsElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlTopSecretElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlExperimentalElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlExpiredElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlFinalElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlSoldElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlDepartmentalElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlForCommentElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlNotForPublicReleaseElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlForPublicReleaseElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.sidePanelLayout = new DevExpress.XtraLayout.LayoutControl();
            this.propertiesGroup = new DevExpress.XtraEditors.GroupControl();
            this.propertyGridControl = new DevExpress.XtraVerticalGrid.PropertyGridControl();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.accordionLayoutItem = new DevExpress.XtraLayout.LayoutControlItem();
            this.splitter = new DevExpress.XtraLayout.SplitterItem();
            this.propertyGridLayoutItem = new DevExpress.XtraLayout.LayoutControlItem();
            this.pdfViewer = new DevExpress.XtraPdfViewer.PdfViewer();
            this.sidePanel = new DevExpress.XtraEditors.SidePanel();
            this.viewerPanel = new DevExpress.XtraEditors.PanelControl();
            this.accordionContentContainer1 = new DevExpress.XtraBars.Navigation.AccordionContentContainer();
            this.accordionControlElement2 = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            ((System.ComponentModel.ISupportInitialize)(this.accordionControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.sidePanelLayout)).BeginInit();
            this.sidePanelLayout.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.propertiesGroup)).BeginInit();
            this.propertiesGroup.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.propertyGridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.accordionLayoutItem)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitter)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.propertyGridLayoutItem)).BeginInit();
            this.sidePanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.viewerPanel)).BeginInit();
            this.viewerPanel.SuspendLayout();
            this.SuspendLayout();
            // 
            // accordionControl
            // 
            this.accordionControl.AllowDrop = true;
            this.accordionControl.AllowItemSelection = true;
            this.accordionControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.accordionControl.Elements.AddRange(new DevExpress.XtraBars.Navigation.AccordionControlElement[] {
            this.accordionControlFileElement,
            this.accordionControlFormFieldsElement,
            this.accordionControlSignHereElements,
            this.accordionControlStandardElements});
            this.accordionControl.Location = new System.Drawing.Point(12, 12);
            this.accordionControl.Name = "accordionControl";
            this.accordionControl.ScrollBarMode = DevExpress.XtraBars.Navigation.ScrollBarMode.Hidden;
            this.accordionControl.Size = new System.Drawing.Size(258, 388);
            this.accordionControl.StyleController = this.sidePanelLayout;
            this.accordionControl.TabIndex = 1;
            // 
            // accordionControlFileElement
            // 
            this.accordionControlFileElement.Elements.AddRange(new DevExpress.XtraBars.Navigation.AccordionControlElement[] {
            this.accordionControlNewElement,
            this.accordionControlOpenElement,
            this.accordionControlSaveElement});
            this.accordionControlFileElement.Expanded = true;
            this.accordionControlFileElement.Name = "accordionControlFileElement";
            this.accordionControlFileElement.Text = "File";
            // 
            // accordionControlNewElement
            // 
            this.accordionControlNewElement.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("accordionControlNewElement.ImageOptions.SvgImage")));
            this.accordionControlNewElement.Name = "accordionControlNewElement";
            this.accordionControlNewElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlNewElement.Text = "New";
            this.accordionControlNewElement.Click += new System.EventHandler(this.accordionControlNewElement_Click);
            // 
            // accordionControlOpenElement
            // 
            this.accordionControlOpenElement.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("accordionControlOpenElement.ImageOptions.SvgImage")));
            this.accordionControlOpenElement.Name = "accordionControlOpenElement";
            this.accordionControlOpenElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            toolTipTitleItem3.Text = "Open... (Ctrl+O)";
            superToolTip3.Items.Add(toolTipTitleItem3);
            this.accordionControlOpenElement.SuperTip = superToolTip3;
            this.accordionControlOpenElement.Text = "Open...";
            this.accordionControlOpenElement.Click += new System.EventHandler(this.AccordionControlOpenElementClick);
            // 
            // accordionControlSaveElement
            // 
            this.accordionControlSaveElement.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("accordionControlSaveElement.ImageOptions.SvgImage")));
            this.accordionControlSaveElement.Name = "accordionControlSaveElement";
            this.accordionControlSaveElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            toolTipTitleItem4.Text = "Save as... (Ctrl+S)";
            superToolTip4.Items.Add(toolTipTitleItem4);
            this.accordionControlSaveElement.SuperTip = superToolTip4;
            this.accordionControlSaveElement.Text = "Save As...";
            this.accordionControlSaveElement.Click += new System.EventHandler(this.AccordionControlSaveElementClick);
            // 
            // accordionControlFormFieldsElement
            // 
            this.accordionControlFormFieldsElement.Elements.AddRange(new DevExpress.XtraBars.Navigation.AccordionControlElement[] {
            this.accordionControlDConfidentialElement,
            this.accordionControlDApprovedElement,
            this.accordionControlDRevisedElement,
            this.accordionControlDReviewedElement,
            this.accordionControlDReceivedElement});
            this.accordionControlFormFieldsElement.Expanded = true;
            this.accordionControlFormFieldsElement.Name = "accordionControlFormFieldsElement";
            this.accordionControlFormFieldsElement.Text = "Dynamic";
            // 
            // accordionControlDConfidentialElement
            // 
            this.accordionControlDConfidentialElement.Name = "accordionControlDConfidentialElement";
            this.accordionControlDConfidentialElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlDConfidentialElement.Text = "Confidential";
            // 
            // accordionControlDApprovedElement
            // 
            this.accordionControlDApprovedElement.Name = "accordionControlDApprovedElement";
            this.accordionControlDApprovedElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlDApprovedElement.Text = "Approved";
            // 
            // accordionControlDRevisedElement
            // 
            this.accordionControlDRevisedElement.Name = "accordionControlDRevisedElement";
            this.accordionControlDRevisedElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlDRevisedElement.Text = "Revised";
            // 
            // accordionControlDReviewedElement
            // 
            this.accordionControlDReviewedElement.Name = "accordionControlDReviewedElement";
            this.accordionControlDReviewedElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlDReviewedElement.Text = "Reviewed";
            // 
            // accordionControlDReceivedElement
            // 
            this.accordionControlDReceivedElement.Name = "accordionControlDReceivedElement";
            this.accordionControlDReceivedElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlDReceivedElement.Text = "Received";
            // 
            // accordionControlSignHereElements
            // 
            this.accordionControlSignHereElements.Elements.AddRange(new DevExpress.XtraBars.Navigation.AccordionControlElement[] {
            this.accordionControlSignHereElement,
            this.accordionControlInitialHereElement,
            this.accordionControlWitnessHereElement,
            this.accordionControlSHAcceptedElement,
            this.accordionControlSHRejectedElement});
            this.accordionControlSignHereElements.Name = "accordionControlSignHereElements";
            this.accordionControlSignHereElements.Text = "Sign Here";
            // 
            // accordionControlSignHereElement
            // 
            this.accordionControlSignHereElement.Name = "accordionControlSignHereElement";
            this.accordionControlSignHereElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlSignHereElement.Text = "Sign Here";
            // 
            // accordionControlInitialHereElement
            // 
            this.accordionControlInitialHereElement.Name = "accordionControlInitialHereElement";
            this.accordionControlInitialHereElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlInitialHereElement.Text = "InitialHere";
            // 
            // accordionControlWitnessHereElement
            // 
            this.accordionControlWitnessHereElement.Name = "accordionControlWitnessHereElement";
            this.accordionControlWitnessHereElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlWitnessHereElement.Text = "Witness Here";
            // 
            // accordionControlSHAcceptedElement
            // 
            this.accordionControlSHAcceptedElement.Name = "accordionControlSHAcceptedElement";
            this.accordionControlSHAcceptedElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlSHAcceptedElement.Text = "Accepted";
            // 
            // accordionControlSHRejectedElement
            // 
            this.accordionControlSHRejectedElement.Name = "accordionControlSHRejectedElement";
            this.accordionControlSHRejectedElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlSHRejectedElement.Text = "Rejected";
            // 
            // accordionControlStandardElements
            // 
            this.accordionControlStandardElements.Elements.AddRange(new DevExpress.XtraBars.Navigation.AccordionControlElement[] {
            this.accordionControlDraftElement,
            this.accordionControlApprovedElement,
            this.accordionControlNotApprovedElement,
            this.accordionControlAsIsElement,
            this.accordionControlTopSecretElement,
            this.accordionControlExperimentalElement,
            this.accordionControlExpiredElement,
            this.accordionControlFinalElement,
            this.accordionControlSoldElement,
            this.accordionControlDepartmentalElement,
            this.accordionControlForCommentElement,
            this.accordionControlNotForPublicReleaseElement,
            this.accordionControlForPublicReleaseElement});
            this.accordionControlStandardElements.Name = "accordionControlStandardElements";
            this.accordionControlStandardElements.Text = "Standard";
            // 
            // accordionControlDraftElement
            // 
            this.accordionControlDraftElement.Name = "accordionControlDraftElement";
            this.accordionControlDraftElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlDraftElement.Text = "Draft";
            // 
            // accordionControlApprovedElement
            // 
            this.accordionControlApprovedElement.Name = "accordionControlApprovedElement";
            this.accordionControlApprovedElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlApprovedElement.Text = "Approved";
            // 
            // accordionControlNotApprovedElement
            // 
            this.accordionControlNotApprovedElement.Name = "accordionControlNotApprovedElement";
            this.accordionControlNotApprovedElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlNotApprovedElement.Text = "Not Approved";
            // 
            // accordionControlAsIsElement
            // 
            this.accordionControlAsIsElement.Name = "accordionControlAsIsElement";
            this.accordionControlAsIsElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlAsIsElement.Text = "As Is";
            // 
            // accordionControlTopSecretElement
            // 
            this.accordionControlTopSecretElement.Name = "accordionControlTopSecretElement";
            this.accordionControlTopSecretElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlTopSecretElement.Text = "Top Secret";
            // 
            // accordionControlExperimentalElement
            // 
            this.accordionControlExperimentalElement.Name = "accordionControlExperimentalElement";
            this.accordionControlExperimentalElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlExperimentalElement.Text = "Experimental";
            // 
            // accordionControlExpiredElement
            // 
            this.accordionControlExpiredElement.Name = "accordionControlExpiredElement";
            this.accordionControlExpiredElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlExpiredElement.Text = "Expired";
            // 
            // accordionControlFinalElement
            // 
            this.accordionControlFinalElement.Name = "accordionControlFinalElement";
            this.accordionControlFinalElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlFinalElement.Text = "Final";
            // 
            // accordionControlSoldElement
            // 
            this.accordionControlSoldElement.Name = "accordionControlSoldElement";
            this.accordionControlSoldElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlSoldElement.Text = "Sold";
            // 
            // accordionControlDepartmentalElement
            // 
            this.accordionControlDepartmentalElement.Name = "accordionControlDepartmentalElement";
            this.accordionControlDepartmentalElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlDepartmentalElement.Text = "Departmental";
            // 
            // accordionControlForCommentElement
            // 
            this.accordionControlForCommentElement.Name = "accordionControlForCommentElement";
            this.accordionControlForCommentElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlForCommentElement.Text = "For Comment";
            // 
            // accordionControlNotForPublicReleaseElement
            // 
            this.accordionControlNotForPublicReleaseElement.Name = "accordionControlNotForPublicReleaseElement";
            this.accordionControlNotForPublicReleaseElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlNotForPublicReleaseElement.Text = "Not For Public Release";
            // 
            // accordionControlForPublicReleaseElement
            // 
            this.accordionControlForPublicReleaseElement.Name = "accordionControlForPublicReleaseElement";
            this.accordionControlForPublicReleaseElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlForPublicReleaseElement.Text = "For Public Release";
            // 
            // sidePanelLayout
            // 
            this.sidePanelLayout.Controls.Add(this.propertiesGroup);
            this.sidePanelLayout.Controls.Add(this.accordionControl);
            this.sidePanelLayout.Dock = System.Windows.Forms.DockStyle.Fill;
            this.sidePanelLayout.Location = new System.Drawing.Point(1, 0);
            this.sidePanelLayout.Name = "sidePanelLayout";
            this.sidePanelLayout.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = new System.Drawing.Rectangle(1162, 362, 650, 400);
            this.sidePanelLayout.Root = this.Root;
            this.sidePanelLayout.Size = new System.Drawing.Size(282, 654);
            this.sidePanelLayout.TabIndex = 1;
            this.sidePanelLayout.Text = "layoutControl1";
            // 
            // propertiesGroup
            // 
            this.propertiesGroup.Controls.Add(this.propertyGridControl);
            this.propertiesGroup.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.propertiesGroup.Location = new System.Drawing.Point(12, 414);
            this.propertiesGroup.Name = "propertiesGroup";
            this.propertiesGroup.Size = new System.Drawing.Size(258, 228);
            this.propertiesGroup.TabIndex = 1;
            this.propertiesGroup.Text = "Annotation Properties";
            // 
            // propertyGridControl
            // 
            this.propertyGridControl.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.propertyGridControl.Cursor = System.Windows.Forms.Cursors.Default;
            this.propertyGridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.propertyGridControl.Location = new System.Drawing.Point(2, 23);
            this.propertyGridControl.Margin = new System.Windows.Forms.Padding(0);
            this.propertyGridControl.Name = "propertyGridControl";
            this.propertyGridControl.OptionsView.AllowReadOnlyRowAppearance = DevExpress.Utils.DefaultBoolean.True;
            this.propertyGridControl.OptionsView.ShowRootCategories = false;
            this.propertyGridControl.Size = new System.Drawing.Size(254, 203);
            this.propertyGridControl.TabIndex = 2;
            // 
            // Root
            // 
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.accordionLayoutItem,
            this.splitter,
            this.propertyGridLayoutItem});
            this.Root.Name = "Root";
            this.Root.Size = new System.Drawing.Size(282, 654);
            this.Root.TextVisible = false;
            // 
            // accordionLayoutItem
            // 
            this.accordionLayoutItem.Control = this.accordionControl;
            this.accordionLayoutItem.Location = new System.Drawing.Point(0, 0);
            this.accordionLayoutItem.Name = "accordionLayoutItem";
            this.accordionLayoutItem.Size = new System.Drawing.Size(262, 392);
            this.accordionLayoutItem.TextSize = new System.Drawing.Size(0, 0);
            this.accordionLayoutItem.TextVisible = false;
            // 
            // splitter
            // 
            this.splitter.AllowHotTrack = true;
            this.splitter.Location = new System.Drawing.Point(0, 392);
            this.splitter.Name = "splitter";
            this.splitter.ShowSplitGlyph = DevExpress.Utils.DefaultBoolean.True;
            this.splitter.Size = new System.Drawing.Size(262, 10);
            // 
            // propertyGridLayoutItem
            // 
            this.propertyGridLayoutItem.Control = this.propertiesGroup;
            this.propertyGridLayoutItem.Location = new System.Drawing.Point(0, 402);
            this.propertyGridLayoutItem.Name = "propertyGridLayoutItem";
            this.propertyGridLayoutItem.Size = new System.Drawing.Size(262, 232);
            this.propertyGridLayoutItem.TextSize = new System.Drawing.Size(0, 0);
            this.propertyGridLayoutItem.TextVisible = false;
            // 
            // pdfViewer
            // 
            this.pdfViewer.AllowDrop = true;
            this.pdfViewer.DetachStreamAfterLoadComplete = true;
            this.pdfViewer.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pdfViewer.Location = new System.Drawing.Point(0, 0);
            this.pdfViewer.Name = "pdfViewer";
            this.pdfViewer.NavigationPanePageVisibility = DevExpress.XtraPdfViewer.PdfNavigationPanePageVisibility.None;
            this.pdfViewer.Size = new System.Drawing.Size(560, 654);
            this.pdfViewer.TabIndex = 0;
            this.pdfViewer.ZoomMode = DevExpress.XtraPdfViewer.PdfZoomMode.PageLevel;
            // 
            // sidePanel
            // 
            this.sidePanel.Controls.Add(this.sidePanelLayout);
            this.sidePanel.Dock = System.Windows.Forms.DockStyle.Right;
            this.sidePanel.Location = new System.Drawing.Point(560, 0);
            this.sidePanel.MinimumSize = new System.Drawing.Size(200, 0);
            this.sidePanel.Name = "sidePanel";
            this.sidePanel.Size = new System.Drawing.Size(283, 654);
            this.sidePanel.TabIndex = 4;
            this.sidePanel.Text = "sidePanel1";
            // 
            // viewerPanel
            // 
            this.viewerPanel.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.viewerPanel.Controls.Add(this.pdfViewer);
            this.viewerPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.viewerPanel.Location = new System.Drawing.Point(0, 0);
            this.viewerPanel.Name = "viewerPanel";
            this.viewerPanel.Size = new System.Drawing.Size(560, 654);
            this.viewerPanel.TabIndex = 6;
            // 
            // accordionContentContainer1
            // 
            this.accordionContentContainer1.Appearance.BackColor = System.Drawing.SystemColors.Control;
            this.accordionContentContainer1.Appearance.Options.UseBackColor = true;
            this.accordionContentContainer1.Name = "accordionContentContainer1";
            this.accordionContentContainer1.Size = new System.Drawing.Size(216, 76);
            this.accordionContentContainer1.TabIndex = 1;
            // 
            // accordionControlElement2
            // 
            this.accordionControlElement2.ContentContainer = this.accordionContentContainer1;
            this.accordionControlElement2.Name = "accordionControlElement2";
            this.accordionControlElement2.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlElement2.Text = "Element2";
            // 
            // PdfRubberStampAnnotations
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.viewerPanel);
            this.Controls.Add(this.sidePanel);
            this.Margin = new System.Windows.Forms.Padding(0);
            this.Name = "PdfRubberStampAnnotations";
            this.Size = new System.Drawing.Size(843, 654);
            ((System.ComponentModel.ISupportInitialize)(this.accordionControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.sidePanelLayout)).EndInit();
            this.sidePanelLayout.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.propertiesGroup)).EndInit();
            this.propertiesGroup.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.propertyGridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.accordionLayoutItem)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitter)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.propertyGridLayoutItem)).EndInit();
            this.sidePanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.viewerPanel)).EndInit();
            this.viewerPanel.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion
        private XtraEditors.SidePanel sidePanel;
        private XtraEditors.PanelControl viewerPanel;
        private XtraBars.Navigation.AccordionControl accordionControl;
        private XtraBars.Navigation.AccordionControlElement accordionControlFileElement;
        private XtraPdfViewer.PdfViewer pdfViewer;
        private XtraBars.Navigation.AccordionControlElement accordionControlOpenElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlSaveElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlFormFieldsElement;
        private XtraBars.Navigation.AccordionContentContainer accordionContentContainer1;
        private XtraBars.Navigation.AccordionControlElement accordionControlElement2;
        private XtraBars.Navigation.AccordionControlElement accordionControlNewElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlDraftElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlAsIsElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlApprovedElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlDConfidentialElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlNotApprovedElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlInitialHereElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlSignHereElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlTopSecretElement;
        private XtraVerticalGrid.PropertyGridControl propertyGridControl;
        private XtraBars.Navigation.AccordionControlElement accordionControlDApprovedElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlDRevisedElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlDReviewedElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlDReceivedElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlSignHereElements;
        private XtraBars.Navigation.AccordionControlElement accordionControlWitnessHereElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlSHAcceptedElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlSHRejectedElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlStandardElements;
        private XtraBars.Navigation.AccordionControlElement accordionControlExperimentalElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlExpiredElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlFinalElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlSoldElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlDepartmentalElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlForCommentElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlNotForPublicReleaseElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlForPublicReleaseElement;
        private XtraLayout.LayoutControl sidePanelLayout;
        private XtraLayout.LayoutControlGroup Root;
        private XtraLayout.LayoutControlItem accordionLayoutItem;
        private XtraLayout.SplitterItem splitter;
        private XtraEditors.GroupControl propertiesGroup;
        private XtraLayout.LayoutControlItem propertyGridLayoutItem;
    }
}
