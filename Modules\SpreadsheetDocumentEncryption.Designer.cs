﻿using DevExpress.XtraEditors;

namespace DevExpress.Docs.Demos {
    partial class SpreadsheetDocumentEncryption {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing) {
            if (disposing && (components != null)) {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent() {
            this.sidePanel1 = new DevExpress.XtraEditors.SidePanel();
            this.btnSaveAs = new DevExpress.XtraEditors.SimpleButton();
            this.lblPasswordToOpen = new DevExpress.XtraEditors.LabelControl();
            this.lblEncryptionType = new DevExpress.XtraEditors.LabelControl();
            this.edPasswordToOpen = new DevExpress.XtraEditors.TextEdit();
            this.edEncryptionType = new DevExpress.XtraEditors.ComboBoxEdit();
            this.spreadsheetPreview1 = new DevExpress.Docs.Demos.SpreadsheetPreview();
            this.sidePanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edPasswordToOpen.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edEncryptionType.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // sidePanel1
            // 
            this.sidePanel1.AllowResize = false;
            this.sidePanel1.Controls.Add(this.btnSaveAs);
            this.sidePanel1.Controls.Add(this.lblPasswordToOpen);
            this.sidePanel1.Controls.Add(this.lblEncryptionType);
            this.sidePanel1.Controls.Add(this.edPasswordToOpen);
            this.sidePanel1.Controls.Add(this.edEncryptionType);
            this.sidePanel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.sidePanel1.Location = new System.Drawing.Point(0, 0);
            this.sidePanel1.Name = "sidePanel1";
            this.sidePanel1.Size = new System.Drawing.Size(784, 53);
            this.sidePanel1.TabIndex = 35;
            this.sidePanel1.Text = "sidePanel1";
            // 
            // btnSaveAs
            // 
            this.btnSaveAs.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSaveAs.Location = new System.Drawing.Point(671, 15);
            this.btnSaveAs.Name = "btnSaveAs";
            this.btnSaveAs.Size = new System.Drawing.Size(98, 23);
            this.btnSaveAs.TabIndex = 40;
            this.btnSaveAs.Text = "Save As...";
            this.btnSaveAs.Click += new System.EventHandler(this.SaveAs_Click);
            // 
            // lblPasswordToOpen
            // 
            this.lblPasswordToOpen.Location = new System.Drawing.Point(16, 19);
            this.lblPasswordToOpen.Name = "lblPasswordToOpen";
            this.lblPasswordToOpen.Size = new System.Drawing.Size(92, 13);
            this.lblPasswordToOpen.TabIndex = 35;
            this.lblPasswordToOpen.Text = "Password to Open:";
            // 
            // lblEncryptionType
            // 
            this.lblEncryptionType.Location = new System.Drawing.Point(248, 19);
            this.lblEncryptionType.Name = "lblEncryptionType";
            this.lblEncryptionType.Size = new System.Drawing.Size(82, 13);
            this.lblEncryptionType.TabIndex = 36;
            this.lblEncryptionType.Text = "Encryption Type:";
            // 
            // edPasswordToOpen
            // 
            this.edPasswordToOpen.Location = new System.Drawing.Point(131, 16);
            this.edPasswordToOpen.Name = "edPasswordToOpen";
            this.edPasswordToOpen.Properties.MaxLength = 255;
            this.edPasswordToOpen.Size = new System.Drawing.Size(93, 20);
            this.edPasswordToOpen.TabIndex = 37;
            // 
            // edEncryptionType
            // 
            this.edEncryptionType.Location = new System.Drawing.Point(338, 16);
            this.edEncryptionType.Name = "edEncryptionType";
            this.edEncryptionType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.edEncryptionType.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.edEncryptionType.Size = new System.Drawing.Size(120, 20);
            this.edEncryptionType.TabIndex = 38;
            // 
            // spreadsheetPreview1
            // 
            this.spreadsheetPreview1.CanShowBorders = false;
            this.spreadsheetPreview1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.spreadsheetPreview1.Location = new System.Drawing.Point(0, 53);
            this.spreadsheetPreview1.Name = "spreadsheetPreview1";
            this.spreadsheetPreview1.Size = new System.Drawing.Size(784, 379);
            this.spreadsheetPreview1.TabIndex = 36;
            this.spreadsheetPreview1.Workbook = null;
            // 
            // SpreadsheetDocumentEncryption
            // 
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.spreadsheetPreview1);
            this.Controls.Add(this.sidePanel1);
            this.Name = "SpreadsheetDocumentEncryption";
            this.sidePanel1.ResumeLayout(false);
            this.sidePanel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edPasswordToOpen.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edEncryptionType.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion
        private SidePanel sidePanel1;
        private LabelControl lblPasswordToOpen;
        private LabelControl lblEncryptionType;
        private TextEdit edPasswordToOpen;
        private ComboBoxEdit edEncryptionType;
        protected SimpleButton btnSaveAs;
        private SpreadsheetPreview spreadsheetPreview1;
    }
}
