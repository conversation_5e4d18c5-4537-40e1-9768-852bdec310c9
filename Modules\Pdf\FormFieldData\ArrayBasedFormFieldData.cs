﻿using System.ComponentModel;
using DevExpress.Pdf;

namespace DevExpress.Docs.Demos {
    public abstract class ArrayBasedFormFieldData : FormFieldData {
        string[] fItems;

        [Category("Data")]
        public string[] Items {
            get { return fItems; }
            set {
                fItems = value;
                ClearSelected();
                UpdateModel();
            }
        }

        protected ArrayBasedFormFieldData(PdfDocumentPosition position, DocumentFormController controller)
            : base(position, controller) {
        }
        protected override PdfAcroFormCommonVisualField CreateVisualFormField() {
            PdfAcroFormChoiceField choiceField = CreateChoiceField();
            if(Items != null) {
                choiceField.ClearValues();
                foreach(string item in Items)
                    choiceField.AddValue(item);
            }
            SelectItems(choiceField);
            return choiceField;
        }
        protected abstract void ClearSelected();
        protected abstract void SelectItems(PdfAcroFormChoiceField choiceField);
        protected abstract PdfAcroFormChoiceField CreateChoiceField();
    }
}
