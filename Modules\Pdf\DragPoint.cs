﻿using System.Collections.Generic;
using DevExpress.Pdf;

namespace DevExpress.Docs.Demos {
    public enum DragDirection {
        None,
        Move,
        ResizeTop,
        ResizeBottom,
        ResizeLeft,
        ResizeRight,
        ResizeTopLeft,
        ResizeTopRight,
        ResizeBottomLeft,
        ResizeBottomRight,
        MovePoint
    }

    public class DragPoint {
        const double dragPointHalfSize = 5;

        public static DragPoint[] CreateDragPoints(IList<PdfPoint> source) {
            DragPoint[] points = new DragPoint[source.Count];
            for(int i = 0; i < source.Count; i++) {
                points[i] = new DragPoint(source[i].X, source[i].Y, DragDirection.MovePoint, i);
            }
            return points;
        }
        public static DragPoint[] CreateDragPoints(PdfRectangle formFieldRectangle) {
            DragPoint[] points = new DragPoint[9];
            double left = formFieldRectangle.Left;
            double bottom = formFieldRectangle.Bottom;
            double right = formFieldRectangle.Right;
            double top = formFieldRectangle.Top;
            double centerX = left + formFieldRectangle.Width / 2;
            double centerY = bottom + formFieldRectangle.Height / 2;
            points[0] = new DragPoint(left, top, DragDirection.ResizeTopLeft);
            points[1] = new DragPoint(centerX, bottom, DragDirection.ResizeBottom);
            points[2] = new DragPoint(left, bottom, DragDirection.ResizeBottomLeft);
            points[3] = new DragPoint(right, bottom, DragDirection.ResizeBottomRight);
            points[4] = new DragPoint(left, centerY, DragDirection.ResizeLeft);
            points[5] = new DragPoint(right, centerY, DragDirection.ResizeRight);
            points[6] = new DragPoint(centerX, top, DragDirection.ResizeTop);
            points[7] = new DragPoint(right, top, DragDirection.ResizeTopRight);
            points[8] = new DragPoint(formFieldRectangle, DragDirection.Move);
            return points;
        }

        readonly DragDirection fDirection;
        readonly PdfRectangle dragRectangle;

        public DragDirection Direction { get { return fDirection; } }
        public PdfRectangle Rectangle { get { return dragRectangle; } }
        public int Index { get; }

        DragPoint(double x, double y, DragDirection direction, int index = 0) {
            dragRectangle = new PdfRectangle(x - dragPointHalfSize, y - dragPointHalfSize, x + dragPointHalfSize, y + dragPointHalfSize);
            fDirection = direction;
            Index = index;
        }
        DragPoint(PdfRectangle dragRectangle, DragDirection direction) {
            this.dragRectangle = dragRectangle;
            fDirection = direction;
        }
        public bool Contains(PdfPoint point) {
            return dragRectangle.Contains(point);
        }
    }
}
