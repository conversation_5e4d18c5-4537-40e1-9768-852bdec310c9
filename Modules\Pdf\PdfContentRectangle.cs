﻿using System;
using System.ComponentModel;
using DevExpress.Pdf;

namespace DevExpress.Docs.Demos {
    public class PdfContentRectangle {
        PdfRectangle fInnerRectangle = new PdfRectangle(0, 0, 0, 0);
        readonly IDocumentController controller;

        public int Top {
            get { return (int)InnerRectangle.Top; }
            set {
                InnerRectangle = new PdfRectangle(InnerRectangle.Left, InnerRectangle.Bottom, InnerRectangle.Right, value);
                controller.UpdateDocument();
            }
        }
        public int Bottom {
            get { return (int)fInnerRectangle.Bottom; }
            set {
                InnerRectangle = new PdfRectangle(InnerRectangle.Left, value, InnerRectangle.Right, InnerRectangle.Top);
                controller.UpdateDocument();
            }
        }
        public int Left {
            get { return (int)InnerRectangle.Left; }
            set {
                InnerRectangle = new PdfRectangle(value, InnerRectangle.Bottom, InnerRectangle.Right, InnerRectangle.Top);
                controller.UpdateDocument();
            }
        }
        public int Right {
            get { return (int)InnerRectangle.Right; }
            set {
                InnerRectangle = new PdfRectangle(InnerRectangle.Left, InnerRectangle.Bottom, value, InnerRectangle.Top);
                controller.UpdateDocument();
            }
        }

        [Browsable(false)]
        public PdfRectangle InnerRectangle {
            get { return fInnerRectangle; }
            set { fInnerRectangle = value; }
        }

        public PdfContentRectangle(PdfPoint startPoint, PdfPoint endPoint, IDocumentController controller) {
            this.controller = controller;
            fInnerRectangle = new PdfRectangle(Math.Min(startPoint.X, endPoint.X), Math.Min(startPoint.Y, endPoint.Y), Math.Max(startPoint.X, endPoint.X), Math.Max(startPoint.Y, endPoint.Y));
        }
        public override string ToString() {
            return string.Format("Left={0}, Bottom={1}, Right={2}, Top={3}", Left, Bottom, Right, Top);
        }
        public void Move(double x, double y) {
            InnerRectangle = new PdfRectangle(InnerRectangle.Left + x, InnerRectangle.Bottom + y, InnerRectangle.Right + x, InnerRectangle.Top + y);
            controller.UpdateDocument();
        }
    }
}
