{"version": 2, "dgSpecHash": "WsHQyJrD/Co=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Documents\\DevExpress Demos 25.1\\Components\\Office File API\\CS\\OfficeFileAPIDemos\\OfficeFileAPIDemos.NetCore.Desktop.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\devexpress.charts\\25.1.3\\devexpress.charts.25.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devexpress.charts.core\\25.1.3\\devexpress.charts.core.25.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devexpress.codeparser\\25.1.3\\devexpress.codeparser.25.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devexpress.data\\25.1.3\\devexpress.data.25.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devexpress.data.desktop\\25.1.3\\devexpress.data.desktop.25.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devexpress.dataaccess\\25.1.3\\devexpress.dataaccess.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.dataaccess.ui\\25.1.3\\devexpress.dataaccess.ui.25.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devexpress.datavisualization.core\\25.1.3\\devexpress.datavisualization.core.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.diagram.core\\25.1.3\\devexpress.diagram.core.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.document.processor\\25.1.3\\devexpress.document.processor.25.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devexpress.drawing\\25.1.3\\devexpress.drawing.25.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devexpress.gauges.core\\25.1.3\\devexpress.gauges.core.25.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devexpress.images\\25.1.3\\devexpress.images.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.map.core\\25.1.3\\devexpress.map.core.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.mvvm\\25.1.3\\devexpress.mvvm.25.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devexpress.office.core\\25.1.3\\devexpress.office.core.25.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devexpress.pdf.core\\25.1.3\\devexpress.pdf.core.25.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devexpress.pdf.drawing\\25.1.3\\devexpress.pdf.drawing.25.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devexpress.pivotgrid.core\\25.1.3\\devexpress.pivotgrid.core.25.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devexpress.printing.core\\25.1.3\\devexpress.printing.core.25.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devexpress.reporting.core\\25.1.3\\devexpress.reporting.core.25.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devexpress.richedit.core\\25.1.3\\devexpress.richedit.core.25.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devexpress.richedit.export\\25.1.3\\devexpress.richedit.export.25.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devexpress.scheduler.core\\25.1.3\\devexpress.scheduler.core.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.scheduler.coredesktop\\25.1.3\\devexpress.scheduler.coredesktop.25.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devexpress.sparkline.core\\25.1.3\\devexpress.sparkline.core.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.spellchecker.core\\25.1.3\\devexpress.spellchecker.core.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.spreadsheet.core\\25.1.3\\devexpress.spreadsheet.core.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.treemap\\25.1.3\\devexpress.treemap.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.treemap.core\\25.1.3\\devexpress.treemap.core.25.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devexpress.utils\\25.1.3\\devexpress.utils.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.utils.ui\\25.1.3\\devexpress.utils.ui.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.win\\25.1.3\\devexpress.win.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.win.bonusskins\\25.1.3\\devexpress.win.bonusskins.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.win.charts\\25.1.3\\devexpress.win.charts.25.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devexpress.win.design\\25.1.3\\devexpress.win.design.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.win.diagram\\25.1.3\\devexpress.win.diagram.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.win.dialogs\\25.1.3\\devexpress.win.dialogs.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.win.dialogs.core\\25.1.3\\devexpress.win.dialogs.core.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.win.gantt\\25.1.3\\devexpress.win.gantt.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.win.gauges\\25.1.3\\devexpress.win.gauges.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.win.grid\\25.1.3\\devexpress.win.grid.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.win.map\\25.1.3\\devexpress.win.map.25.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devexpress.win.navigation\\25.1.3\\devexpress.win.navigation.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.win.pdfviewer\\25.1.3\\devexpress.win.pdfviewer.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.win.pivotgrid\\25.1.3\\devexpress.win.pivotgrid.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.win.printing\\25.1.3\\devexpress.win.printing.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.win.reporting\\25.1.3\\devexpress.win.reporting.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.win.richedit\\25.1.3\\devexpress.win.richedit.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.win.scheduler\\25.1.3\\devexpress.win.scheduler.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.win.schedulerextensions\\25.1.3\\devexpress.win.schedulerextensions.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.win.schedulerreporting\\25.1.3\\devexpress.win.schedulerreporting.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.win.spellchecker\\25.1.3\\devexpress.win.spellchecker.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.win.spreadsheet\\25.1.3\\devexpress.win.spreadsheet.25.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devexpress.win.treelist\\25.1.3\\devexpress.win.treelist.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.win.treemap\\25.1.3\\devexpress.win.treemap.25.1.3.nupkg.sha512", "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages\\devexpress.win.verticalgrid\\25.1.3\\devexpress.win.verticalgrid.25.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devexpress.xpo\\25.1.3\\devexpress.xpo.25.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\8.0.1\\microsoft.extensions.dependencyinjection.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\8.0.2\\microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.objectpool\\8.0.10\\microsoft.extensions.objectpool.8.0.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\3.1.0\\microsoft.netcore.platforms.3.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\4.7.0\\microsoft.win32.registry.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\8.0.0\\microsoft.win32.systemevents.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.data.sqlclient.sni\\4.7.0\\runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-arm64.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-x64.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-x86.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.codedom\\4.4.0\\system.codedom.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\8.0.1\\system.configuration.configurationmanager.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.oledb\\8.0.1\\system.data.oledb.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.sqlclient\\4.8.6\\system.data.sqlclient.4.8.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.eventlog\\8.0.1\\system.diagnostics.eventlog.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.performancecounter\\8.0.1\\system.diagnostics.performancecounter.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\8.0.15\\system.drawing.common.8.0.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\4.7.0\\system.security.accesscontrol.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.pkcs\\8.0.1\\system.security.cryptography.pkcs.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\8.0.0\\system.security.cryptography.protecteddata.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.xml\\8.0.2\\system.security.cryptography.xml.8.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\4.7.0\\system.security.principal.windows.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.http\\8.1.2\\system.servicemodel.http.8.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.netframingbase\\8.1.2\\system.servicemodel.netframingbase.8.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.nettcp\\8.1.2\\system.servicemodel.nettcp.8.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.primitives\\8.1.2\\system.servicemodel.primitives.8.1.2.nupkg.sha512"], "logs": []}