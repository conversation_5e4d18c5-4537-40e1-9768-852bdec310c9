﻿namespace DevExpress.Docs.Demos
{
    partial class PdfSignatureDemo
	{
		/// <summary> 
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		#region Component Designer generated code

		/// <summary> 
		/// Required method for Designer support - do not modify 
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
            this.pdfViewer = new DevExpress.XtraPdfViewer.PdfViewer();
            this.appearanceBuilderButton = new DevExpress.XtraEditors.SimpleButton();
            this.certificationLevelLabel = new DevExpress.XtraEditors.LabelControl();
            this.certificationLevelComboBox = new DevExpress.XtraEditors.ComboBoxEdit();
            this.tsaServerLabel = new DevExpress.XtraEditors.LabelControl();
            this.hashAlgorithmLabel = new DevExpress.XtraEditors.LabelControl();
            this.hashAlgorithmComboBox = new DevExpress.XtraEditors.ComboBoxEdit();
            this.tsaURITextEdit = new DevExpress.XtraEditors.TextEdit();
            this.labelImage = new DevExpress.XtraEditors.LabelControl();
            this.imagePictureEdit = new DevExpress.XtraEditors.PictureEdit();
            this.buttonSign = new DevExpress.XtraEditors.SimpleButton();
            this.buttonNewCertificate = new DevExpress.XtraEditors.SimpleButton();
            this.teReason = new DevExpress.XtraEditors.TextEdit();
            this.lbCerts = new DevExpress.XtraEditors.ListBoxControl();
            this.labelContactInfo = new DevExpress.XtraEditors.LabelControl();
            this.labelLocation = new DevExpress.XtraEditors.LabelControl();
            this.labelReason = new DevExpress.XtraEditors.LabelControl();
            this.labelCertificate = new DevExpress.XtraEditors.LabelControl();
            this.teContactInfo = new DevExpress.XtraEditors.TextEdit();
            this.teLocation = new DevExpress.XtraEditors.TextEdit();
            this.sidePanel = new DevExpress.XtraEditors.SidePanel();
            this.tablePanel = new DevExpress.Utils.Layout.TablePanel();
            ((System.ComponentModel.ISupportInitialize)(this.certificationLevelComboBox.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.hashAlgorithmComboBox.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tsaURITextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.imagePictureEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.teReason.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lbCerts)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.teContactInfo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.teLocation.Properties)).BeginInit();
            this.sidePanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tablePanel)).BeginInit();
            this.tablePanel.SuspendLayout();
            this.SuspendLayout();
            // 
            // pdfViewer
            // 
            this.pdfViewer.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pdfViewer.Location = new System.Drawing.Point(0, 0);
            this.pdfViewer.Name = "pdfViewer";
            this.pdfViewer.NavigationPaneInitialVisibility = DevExpress.XtraPdfViewer.PdfNavigationPaneVisibility.Hidden;
            this.pdfViewer.ReadOnly = true;
            this.pdfViewer.Size = new System.Drawing.Size(365, 564);
            this.pdfViewer.TabIndex = 6;
            this.pdfViewer.TabStop = false;
            this.pdfViewer.ZoomMode = DevExpress.XtraPdfViewer.PdfZoomMode.FitToWidth;
            // 
            // appearanceBuilderButton
            // 
            this.tablePanel.SetColumn(this.appearanceBuilderButton, 1);
            this.appearanceBuilderButton.Location = new System.Drawing.Point(108, 310);
            this.appearanceBuilderButton.Name = "appearanceBuilderButton";
            this.tablePanel.SetRow(this.appearanceBuilderButton, 6);
            this.appearanceBuilderButton.Size = new System.Drawing.Size(248, 20);
            this.appearanceBuilderButton.TabIndex = 2;
            this.appearanceBuilderButton.Text = "Appearance Builder...";
            this.appearanceBuilderButton.Click += new System.EventHandler(this.appearanceBuilderButton_Click);
            // 
            // certificationLevelLabel
            // 
            this.tablePanel.SetColumn(this.certificationLevelLabel, 0);
            this.certificationLevelLabel.Location = new System.Drawing.Point(15, 339);
            this.certificationLevelLabel.Name = "certificationLevelLabel";
            this.tablePanel.SetRow(this.certificationLevelLabel, 7);
            this.certificationLevelLabel.Size = new System.Drawing.Size(87, 13);
            this.certificationLevelLabel.TabIndex = 45;
            this.certificationLevelLabel.Text = "Certification level:";
            // 
            // certificationLevelComboBox
            // 
            this.tablePanel.SetColumn(this.certificationLevelComboBox, 1);
            this.certificationLevelComboBox.Location = new System.Drawing.Point(108, 336);
            this.certificationLevelComboBox.Name = "certificationLevelComboBox";
            this.certificationLevelComboBox.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.certificationLevelComboBox.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.tablePanel.SetRow(this.certificationLevelComboBox, 7);
            this.certificationLevelComboBox.Size = new System.Drawing.Size(248, 20);
            this.certificationLevelComboBox.TabIndex = 6;
            // 
            // tsaServerLabel
            // 
            this.tablePanel.SetColumn(this.tsaServerLabel, 0);
            this.tsaServerLabel.Location = new System.Drawing.Point(15, 391);
            this.tsaServerLabel.Name = "tsaServerLabel";
            this.tablePanel.SetRow(this.tsaServerLabel, 9);
            this.tsaServerLabel.Size = new System.Drawing.Size(57, 13);
            this.tsaServerLabel.TabIndex = 43;
            this.tsaServerLabel.Text = "TSA server:";
            // 
            // hashAlgorithmLabel
            // 
            this.tablePanel.SetColumn(this.hashAlgorithmLabel, 0);
            this.hashAlgorithmLabel.Location = new System.Drawing.Point(15, 365);
            this.hashAlgorithmLabel.Name = "hashAlgorithmLabel";
            this.tablePanel.SetRow(this.hashAlgorithmLabel, 8);
            this.hashAlgorithmLabel.Size = new System.Drawing.Size(75, 13);
            this.hashAlgorithmLabel.TabIndex = 42;
            this.hashAlgorithmLabel.Text = "Hash algorithm:";
            // 
            // hashAlgorithmComboBox
            // 
            this.tablePanel.SetColumn(this.hashAlgorithmComboBox, 1);
            this.hashAlgorithmComboBox.Location = new System.Drawing.Point(108, 362);
            this.hashAlgorithmComboBox.Name = "hashAlgorithmComboBox";
            this.hashAlgorithmComboBox.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.hashAlgorithmComboBox.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.tablePanel.SetRow(this.hashAlgorithmComboBox, 8);
            this.hashAlgorithmComboBox.Size = new System.Drawing.Size(248, 20);
            this.hashAlgorithmComboBox.TabIndex = 7;
            // 
            // tsaURITextEdit
            // 
            this.tablePanel.SetColumn(this.tsaURITextEdit, 1);
            this.tsaURITextEdit.EditValue = "https://freetsa.org/tsr";
            this.tsaURITextEdit.Location = new System.Drawing.Point(108, 388);
            this.tsaURITextEdit.Name = "tsaURITextEdit";
            this.tablePanel.SetRow(this.tsaURITextEdit, 9);
            this.tsaURITextEdit.Size = new System.Drawing.Size(248, 20);
            this.tsaURITextEdit.TabIndex = 8;
            // 
            // labelImage
            // 
            this.tablePanel.SetColumn(this.labelImage, 0);
            this.labelImage.Location = new System.Drawing.Point(15, 249);
            this.labelImage.Name = "labelImage";
            this.tablePanel.SetRow(this.labelImage, 5);
            this.labelImage.Size = new System.Drawing.Size(81, 13);
            this.labelImage.TabIndex = 37;
            this.labelImage.Text = "Signature image:";
            // 
            // imagePictureEdit
            // 
            this.tablePanel.SetColumn(this.imagePictureEdit, 1);
            this.imagePictureEdit.Location = new System.Drawing.Point(108, 208);
            this.imagePictureEdit.Name = "imagePictureEdit";
            this.imagePictureEdit.Properties.PictureInterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
            this.imagePictureEdit.Properties.ShowCameraMenuItem = DevExpress.XtraEditors.Controls.CameraMenuItemVisibility.Auto;
            this.imagePictureEdit.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Zoom;
            this.imagePictureEdit.Properties.EditValueChanged += new System.EventHandler(this.OnSignatureImageChanged);
            this.tablePanel.SetRow(this.imagePictureEdit, 5);
            this.imagePictureEdit.Size = new System.Drawing.Size(248, 96);
            this.imagePictureEdit.TabIndex = 5;
            this.imagePictureEdit.TabStop = true;
            // 
            // buttonSign
            // 
            this.tablePanel.SetColumn(this.buttonSign, 1);
            this.buttonSign.Location = new System.Drawing.Point(108, 414);
            this.buttonSign.Name = "buttonSign";
            this.tablePanel.SetRow(this.buttonSign, 10);
            this.buttonSign.Size = new System.Drawing.Size(248, 20);
            this.buttonSign.TabIndex = 9;
            this.buttonSign.Text = "Sign and Save...";
            this.buttonSign.Click += new System.EventHandler(this.OnButtonSignClick);
            // 
            // buttonNewCertificate
            // 
            this.tablePanel.SetColumn(this.buttonNewCertificate, 1);
            this.buttonNewCertificate.Location = new System.Drawing.Point(108, 104);
            this.buttonNewCertificate.Name = "buttonNewCertificate";
            this.tablePanel.SetRow(this.buttonNewCertificate, 1);
            this.buttonNewCertificate.Size = new System.Drawing.Size(248, 20);
            this.buttonNewCertificate.TabIndex = 1;
            this.buttonNewCertificate.Text = "Load Certificate...";
            this.buttonNewCertificate.Click += new System.EventHandler(this.OnButtonNewCertificateClick);
            // 
            // teReason
            // 
            this.tablePanel.SetColumn(this.teReason, 1);
            this.teReason.Location = new System.Drawing.Point(108, 130);
            this.teReason.Name = "teReason";
            this.tablePanel.SetRow(this.teReason, 2);
            this.teReason.Size = new System.Drawing.Size(248, 20);
            this.teReason.TabIndex = 2;
            // 
            // lbCerts
            // 
            this.tablePanel.SetColumn(this.lbCerts, 1);
            this.lbCerts.DisplayMember = "Name";
            this.lbCerts.ItemAutoHeight = true;
            this.lbCerts.Location = new System.Drawing.Point(108, 15);
            this.lbCerts.Name = "lbCerts";
            this.tablePanel.SetRow(this.lbCerts, 0);
            this.lbCerts.Size = new System.Drawing.Size(248, 83);
            this.lbCerts.TabIndex = 0;
            // 
            // labelContactInfo
            // 
            this.tablePanel.SetColumn(this.labelContactInfo, 0);
            this.labelContactInfo.Location = new System.Drawing.Point(15, 185);
            this.labelContactInfo.Name = "labelContactInfo";
            this.tablePanel.SetRow(this.labelContactInfo, 4);
            this.labelContactInfo.Size = new System.Drawing.Size(63, 13);
            this.labelContactInfo.TabIndex = 35;
            this.labelContactInfo.Text = "Contact info:";
            // 
            // labelLocation
            // 
            this.tablePanel.SetColumn(this.labelLocation, 0);
            this.labelLocation.Location = new System.Drawing.Point(15, 159);
            this.labelLocation.Name = "labelLocation";
            this.tablePanel.SetRow(this.labelLocation, 3);
            this.labelLocation.Size = new System.Drawing.Size(44, 13);
            this.labelLocation.TabIndex = 33;
            this.labelLocation.Text = "Location:";
            // 
            // labelReason
            // 
            this.tablePanel.SetColumn(this.labelReason, 0);
            this.labelReason.Location = new System.Drawing.Point(15, 133);
            this.labelReason.Name = "labelReason";
            this.tablePanel.SetRow(this.labelReason, 2);
            this.labelReason.Size = new System.Drawing.Size(40, 13);
            this.labelReason.TabIndex = 31;
            this.labelReason.Text = "Reason:";
            // 
            // labelCertificate
            // 
            this.tablePanel.SetColumn(this.labelCertificate, 0);
            this.labelCertificate.Location = new System.Drawing.Point(15, 50);
            this.labelCertificate.Name = "labelCertificate";
            this.tablePanel.SetRow(this.labelCertificate, 0);
            this.labelCertificate.Size = new System.Drawing.Size(54, 13);
            this.labelCertificate.TabIndex = 25;
            this.labelCertificate.Text = "Certificate:";
            // 
            // teContactInfo
            // 
            this.tablePanel.SetColumn(this.teContactInfo, 1);
            this.teContactInfo.Location = new System.Drawing.Point(108, 182);
            this.teContactInfo.Name = "teContactInfo";
            this.tablePanel.SetRow(this.teContactInfo, 4);
            this.teContactInfo.Size = new System.Drawing.Size(248, 20);
            this.teContactInfo.TabIndex = 4;
            // 
            // teLocation
            // 
            this.tablePanel.SetColumn(this.teLocation, 1);
            this.teLocation.Location = new System.Drawing.Point(108, 156);
            this.teLocation.Name = "teLocation";
            this.tablePanel.SetRow(this.teLocation, 3);
            this.teLocation.Size = new System.Drawing.Size(248, 20);
            this.teLocation.TabIndex = 3;
            // 
            // sidePanel
            // 
            this.sidePanel.Controls.Add(this.tablePanel);
            this.sidePanel.Dock = System.Windows.Forms.DockStyle.Right;
            this.sidePanel.Location = new System.Drawing.Point(365, 0);
            this.sidePanel.MinimumSize = new System.Drawing.Size(200, 0);
            this.sidePanel.Name = "sidePanel";
            this.sidePanel.Size = new System.Drawing.Size(372, 564);
            this.sidePanel.TabIndex = 7;
            this.sidePanel.Text = "sidePanel1";
            // 
            // tablePanel
            // 
            this.tablePanel.Columns.AddRange(new DevExpress.Utils.Layout.TablePanelColumn[] {
            new DevExpress.Utils.Layout.TablePanelColumn(DevExpress.Utils.Layout.TablePanelEntityStyle.AutoSize, 5F),
            new DevExpress.Utils.Layout.TablePanelColumn(DevExpress.Utils.Layout.TablePanelEntityStyle.Relative, 55F)});
            this.tablePanel.Controls.Add(this.buttonSign);
            this.tablePanel.Controls.Add(this.tsaServerLabel);
            this.tablePanel.Controls.Add(this.certificationLevelLabel);
            this.tablePanel.Controls.Add(this.tsaURITextEdit);
            this.tablePanel.Controls.Add(this.hashAlgorithmLabel);
            this.tablePanel.Controls.Add(this.appearanceBuilderButton);
            this.tablePanel.Controls.Add(this.hashAlgorithmComboBox);
            this.tablePanel.Controls.Add(this.certificationLevelComboBox);
            this.tablePanel.Controls.Add(this.buttonNewCertificate);
            this.tablePanel.Controls.Add(this.labelCertificate);
            this.tablePanel.Controls.Add(this.lbCerts);
            this.tablePanel.Controls.Add(this.teReason);
            this.tablePanel.Controls.Add(this.teLocation);
            this.tablePanel.Controls.Add(this.labelReason);
            this.tablePanel.Controls.Add(this.labelLocation);
            this.tablePanel.Controls.Add(this.labelImage);
            this.tablePanel.Controls.Add(this.teContactInfo);
            this.tablePanel.Controls.Add(this.imagePictureEdit);
            this.tablePanel.Controls.Add(this.labelContactInfo);
            this.tablePanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tablePanel.LabelVertAlignment = DevExpress.Utils.Layout.LabelVertAlignment.Baseline;
            this.tablePanel.Location = new System.Drawing.Point(1, 0);
            this.tablePanel.Name = "tablePanel";
            this.tablePanel.Padding = new System.Windows.Forms.Padding(12);
            this.tablePanel.Rows.AddRange(new DevExpress.Utils.Layout.TablePanelRow[] {
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.AutoSize, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.AutoSize, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F)});
            this.tablePanel.Size = new System.Drawing.Size(371, 564);
            this.tablePanel.TabIndex = 0;
            // 
            // PdfSignatureDemo
            // 
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.pdfViewer);
            this.Controls.Add(this.sidePanel);
            this.Name = "PdfSignatureDemo";
            this.Size = new System.Drawing.Size(737, 564);
            ((System.ComponentModel.ISupportInitialize)(this.certificationLevelComboBox.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.hashAlgorithmComboBox.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tsaURITextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.imagePictureEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.teReason.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lbCerts)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.teContactInfo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.teLocation.Properties)).EndInit();
            this.sidePanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.tablePanel)).EndInit();
            this.tablePanel.ResumeLayout(false);
            this.tablePanel.PerformLayout();
            this.ResumeLayout(false);

		}

		#endregion
        private XtraPdfViewer.PdfViewer pdfViewer;
        private XtraEditors.SimpleButton buttonSign;
        private XtraEditors.TextEdit teContactInfo;
        private XtraEditors.TextEdit teLocation;
        private XtraEditors.LabelControl labelContactInfo;
        private XtraEditors.LabelControl labelLocation;
        private XtraEditors.LabelControl labelReason;
        private XtraEditors.LabelControl labelCertificate;
        private XtraEditors.ListBoxControl lbCerts;
        private XtraEditors.TextEdit teReason;
        private XtraEditors.SimpleButton buttonNewCertificate;
        private XtraEditors.PictureEdit imagePictureEdit;
        private XtraEditors.LabelControl labelImage;
        private XtraEditors.ComboBoxEdit hashAlgorithmComboBox;
        private XtraEditors.TextEdit tsaURITextEdit;
        private XtraEditors.LabelControl tsaServerLabel;
        private XtraEditors.LabelControl hashAlgorithmLabel;
        private XtraEditors.LabelControl certificationLevelLabel;
        private XtraEditors.ComboBoxEdit certificationLevelComboBox;
        private XtraEditors.SimpleButton appearanceBuilderButton;
        private Utils.Layout.TablePanel tablePanel;
        private XtraEditors.SidePanel sidePanel;
    }
}
