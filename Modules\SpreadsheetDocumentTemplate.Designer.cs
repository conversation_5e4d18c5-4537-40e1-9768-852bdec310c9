﻿using System;
using DevExpress.XtraEditors;

namespace DevExpress.Docs.Demos {
    public partial class SpreadsheetDocumentTemplate {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing) {
            if (disposing && (components != null)) {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code
        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        /// 
        private void InitializeComponent() {
            this.sidePanel1 = new DevExpress.XtraEditors.SidePanel();
            this.lblLoanAmount = new DevExpress.XtraEditors.LabelControl();
            this.lblAnnualInterestRate = new DevExpress.XtraEditors.LabelControl();
            this.lblLoanPeriodinYears = new DevExpress.XtraEditors.LabelControl();
            this.lblStartDateOfLoan = new DevExpress.XtraEditors.LabelControl();
            this.edLoanAmount = new DevExpress.XtraEditors.SpinEdit();
            this.edStartDateOfLoan = new DevExpress.XtraEditors.DateEdit();
            this.edLoanPeriodinYears = new DevExpress.XtraEditors.SpinEdit();
            this.edAnnualInterestRate = new DevExpress.XtraEditors.SpinEdit();
            this.btnSave = new DevExpress.XtraEditors.SimpleButton();
            this.cbFitToPage = new DevExpress.XtraEditors.CheckEdit();
            this.spreadsheetPreview1 = new DevExpress.Docs.Demos.SpreadsheetPreview();
            this.sidePanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edLoanAmount.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edStartDateOfLoan.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edStartDateOfLoan.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edLoanPeriodinYears.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edAnnualInterestRate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbFitToPage.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // sidePanel1
            // 
            this.sidePanel1.AllowResize = false;
            this.sidePanel1.Controls.Add(this.btnSave);
            this.sidePanel1.Controls.Add(this.cbFitToPage);
            this.sidePanel1.Controls.Add(this.lblLoanAmount);
            this.sidePanel1.Controls.Add(this.lblAnnualInterestRate);
            this.sidePanel1.Controls.Add(this.lblLoanPeriodinYears);
            this.sidePanel1.Controls.Add(this.lblStartDateOfLoan);
            this.sidePanel1.Controls.Add(this.edLoanAmount);
            this.sidePanel1.Controls.Add(this.edStartDateOfLoan);
            this.sidePanel1.Controls.Add(this.edLoanPeriodinYears);
            this.sidePanel1.Controls.Add(this.edAnnualInterestRate);
            this.sidePanel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.sidePanel1.Location = new System.Drawing.Point(0, 0);
            this.sidePanel1.Name = "sidePanel1";
            this.sidePanel1.Size = new System.Drawing.Size(784, 120);
            this.sidePanel1.TabIndex = 35;
            this.sidePanel1.Text = "sidePanel1";
            // 
            // lblLoanAmount
            // 
            this.lblLoanAmount.Location = new System.Drawing.Point(16, 19);
            this.lblLoanAmount.Name = "lblLoanAmount";
            this.lblLoanAmount.Size = new System.Drawing.Size(67, 13);
            this.lblLoanAmount.TabIndex = 35;
            this.lblLoanAmount.Text = "Loan Amount:";
            // 
            // lblAnnualInterestRate
            // 
            this.lblAnnualInterestRate.Location = new System.Drawing.Point(16, 51);
            this.lblAnnualInterestRate.Name = "lblAnnualInterestRate";
            this.lblAnnualInterestRate.Size = new System.Drawing.Size(105, 13);
            this.lblAnnualInterestRate.TabIndex = 36;
            this.lblAnnualInterestRate.Text = "Annual Interest Rate:";
            // 
            // lblLoanPeriodinYears
            // 
            this.lblLoanPeriodinYears.Location = new System.Drawing.Point(262, 19);
            this.lblLoanPeriodinYears.Name = "lblLoanPeriodinYears";
            this.lblLoanPeriodinYears.Size = new System.Drawing.Size(101, 13);
            this.lblLoanPeriodinYears.TabIndex = 37;
            this.lblLoanPeriodinYears.Text = "Loan Period in Years:";
            // 
            // lblStartDateOfLoan
            // 
            this.lblStartDateOfLoan.Location = new System.Drawing.Point(262, 51);
            this.lblStartDateOfLoan.Name = "lblStartDateOfLoan";
            this.lblStartDateOfLoan.Size = new System.Drawing.Size(93, 13);
            this.lblStartDateOfLoan.TabIndex = 38;
            this.lblStartDateOfLoan.Text = "Start Date of Loan:";
            // 
            // edLoanAmount
            // 
            this.edLoanAmount.EditValue = new decimal(new int[] {
            19900,
            0,
            0,
            0});
            this.edLoanAmount.Location = new System.Drawing.Point(132, 16);
            this.edLoanAmount.Name = "edLoanAmount";
            this.edLoanAmount.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.edLoanAmount.Properties.Increment = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.edLoanAmount.Properties.IsFloatValue = false;
            this.edLoanAmount.Properties.MaskSettings.Set("mask", "N00");
            this.edLoanAmount.Properties.MaxValue = new decimal(new int[] {
            200000,
            0,
            0,
            0});
            this.edLoanAmount.Properties.MinValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.edLoanAmount.Size = new System.Drawing.Size(100, 20);
            this.edLoanAmount.TabIndex = 40;
            // 
            // edStartDateOfLoan
            // 
            this.edStartDateOfLoan.EditValue = new System.DateTime(2013, 5, 15, 0, 0, 0, 0);
            this.edStartDateOfLoan.Location = new System.Drawing.Point(372, 48);
            this.edStartDateOfLoan.Name = "edStartDateOfLoan";
            this.edStartDateOfLoan.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.edStartDateOfLoan.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.edStartDateOfLoan.Size = new System.Drawing.Size(100, 20);
            this.edStartDateOfLoan.TabIndex = 39;
            // 
            // edLoanPeriodinYears
            // 
            this.edLoanPeriodinYears.EditValue = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.edLoanPeriodinYears.Location = new System.Drawing.Point(372, 16);
            this.edLoanPeriodinYears.Name = "edLoanPeriodinYears";
            this.edLoanPeriodinYears.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.edLoanPeriodinYears.Properties.IsFloatValue = false;
            this.edLoanPeriodinYears.Properties.MaskSettings.Set("mask", "N00");
            this.edLoanPeriodinYears.Properties.MaxValue = new decimal(new int[] {
            7,
            0,
            0,
            0});
            this.edLoanPeriodinYears.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.edLoanPeriodinYears.Size = new System.Drawing.Size(100, 20);
            this.edLoanPeriodinYears.TabIndex = 41;
            // 
            // edAnnualInterestRate
            // 
            this.edAnnualInterestRate.EditValue = new decimal(new int[] {
            55,
            0,
            0,
            196608});
            this.edAnnualInterestRate.Location = new System.Drawing.Point(132, 48);
            this.edAnnualInterestRate.Name = "edAnnualInterestRate";
            this.edAnnualInterestRate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.edAnnualInterestRate.Properties.DisplayFormat.FormatString = "p";
            this.edAnnualInterestRate.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Custom;
            this.edAnnualInterestRate.Properties.EditFormat.FormatString = "p";
            this.edAnnualInterestRate.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.Custom;
            this.edAnnualInterestRate.Properties.Increment = new decimal(new int[] {
            1,
            0,
            0,
            196608});
            this.edAnnualInterestRate.Properties.MaskSettings.Set("mask", "p");
            this.edAnnualInterestRate.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.edAnnualInterestRate.Size = new System.Drawing.Size(100, 20);
            this.edAnnualInterestRate.TabIndex = 42;
            // 
            // btnSave
            // 
            this.btnSave.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSave.Location = new System.Drawing.Point(669, 80);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(98, 23);
            this.btnSave.TabIndex = 44;
            this.btnSave.Text = "Save As...";
            this.btnSave.Click += new System.EventHandler(this.Save_Click);
            // 
            // cbFitToPage
            // 
            this.cbFitToPage.Location = new System.Drawing.Point(16, 83);
            this.cbFitToPage.Name = "cbFitToPage";
            this.cbFitToPage.Properties.Caption = "Fit To Page";
            this.cbFitToPage.Size = new System.Drawing.Size(100, 20);
            this.cbFitToPage.TabIndex = 43;
            // 
            // spreadsheetPreview1
            // 
            this.spreadsheetPreview1.CanShowBorders = false;
            this.spreadsheetPreview1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.spreadsheetPreview1.Location = new System.Drawing.Point(0, 120);
            this.spreadsheetPreview1.Name = "spreadsheetPreview1";
            this.spreadsheetPreview1.Size = new System.Drawing.Size(784, 312);
            this.spreadsheetPreview1.TabIndex = 36;
            this.spreadsheetPreview1.Workbook = null;
            // 
            // SpreadsheetDocumentTemplate
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.spreadsheetPreview1);
            this.Controls.Add(this.sidePanel1);
            this.Name = "SpreadsheetDocumentTemplate";
            this.sidePanel1.ResumeLayout(false);
            this.sidePanel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edLoanAmount.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edStartDateOfLoan.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edStartDateOfLoan.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edLoanPeriodinYears.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edAnnualInterestRate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbFitToPage.Properties)).EndInit();
            this.ResumeLayout(false);

        }
        #endregion

        private SidePanel sidePanel1;
        private LabelControl lblLoanAmount;
        private LabelControl lblAnnualInterestRate;
        private LabelControl lblLoanPeriodinYears;
        private LabelControl lblStartDateOfLoan;
        private SpinEdit edLoanAmount;
        private DateEdit edStartDateOfLoan;
        private SpinEdit edLoanPeriodinYears;
        private SpinEdit edAnnualInterestRate;
        protected SimpleButton btnSave;
        protected CheckEdit cbFitToPage;
        private SpreadsheetPreview spreadsheetPreview1;
    }
}
