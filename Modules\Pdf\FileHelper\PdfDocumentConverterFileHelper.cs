﻿using System;
using System.IO;
using System.Windows.Forms;
using DevExpress.Pdf;
using DevExpress.XtraPdfViewer;

namespace DevExpress.Docs.Demos {
    public class PdfDocumentConverterFileHelper : PdfFileHelper, IDisposable {
        readonly PdfViewerFileHelper viewerDocument;
        readonly PdfViewer viewer;
        PdfDocumentConverter converter;

        protected override string Creator {
            get { return ""; }
            set { }
        }
        protected override string Producer {
            get { return ""; }
            set { }
        }

        public PdfDocumentConverterFileHelper(PdfViewer viewer) {
            this.viewer = viewer;
            viewerDocument = new PdfViewerFileHelper(viewer);
        }
        public override void LoadDocument(string path, bool detach) {
            DisposeConverter();
            if(detach) {
                MemoryStream stream = new MemoryStream();
                using(FileStream fileStream = File.OpenRead(path))
                    fileStream.CopyTo(stream);
                converter = new PdfDocumentConverter(stream);
            }
            else
                converter = new PdfDocumentConverter(path);
            viewerDocument.LoadDocument(path, detach);
        }
        public override void LoadDocument(Stream stream) {
            DisposeConverter();
            converter = new PdfDocumentConverter(stream);
            viewerDocument.LoadDocument(stream);
        }
        public void ConvertDocument(PdfCompatibility compatibility) {
            if(converter != null) {
                string fileName = ShowFileDialog<SaveFileDialog>();
                if(!String.IsNullOrEmpty(fileName)) {
                    converter.Convert(compatibility);
                    converter.SaveDocument(fileName);
                }
            }
        }
        public PdfConversionReport ConversionReport => converter?.ConversionReport;
        public PdfACompatibility PdfACompatibility => converter?.PdfACompatibility ?? PdfACompatibility.None;

        protected override void SaveDocument(string filePath, PdfSaveOptions options) {
            converter?.SaveDocument(filePath);
        }
        public void Dispose() {
            DisposeConverter();
        }
        void DisposeConverter() {
            converter?.Dispose();
        }
    }
}
