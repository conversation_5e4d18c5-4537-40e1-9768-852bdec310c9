﻿using System;
using System.Windows.Forms;
using DevExpress.DXperience.Demos;
using DevExpress.Spreadsheet;
using DevExpress.XtraEditors;

namespace DevExpress.Docs.Demos {
    public partial class SpreadsheetDocumentProtection : TutorialControlBase {
        const string saveFileDialogFilter =
            "Excel Workbook (*.xlsx)|*.xlsx|" +
            "Excel Macro-Enabled Workbook (*.xlsm)|*.xlsm|" +
            "Excel Binary Workbook (*.xlsb)|*.xlsb|" +
            "Excel 97-2003 Workbook (*.xls)|*.xls";

        public SpreadsheetDocumentProtection() {
            InitializeComponent();
            spreadsheetPreview1.CanShowBorders = true;
            LoadDocument();
        }

        void LoadDocument() {
            try {
                Workbook workbook = new Workbook();
                workbook.LoadDocument(DemoUtils.GetRelativePath("SimpleMonthlyBudget.xltx"));
                workbook.Worksheets[0].PrintOptions.FitToPage = true;
                spreadsheetPreview1.Workbook = workbook;
                spreadsheetPreview1.UpdatePreview();
            }
            catch(Exception ex) {
                XtraMessageBox.Show(ex.Message, Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void cbProtect_CheckedChanged(object sender, EventArgs e) {
            btnProtectAndSave.Enabled = cbProtectWorkbook.Checked || cbProtectWorksheet.Checked;
        }

        private void btnProtectAndSave_Click(object sender, EventArgs e) {
            using(SaveFileDialog openFileDialog = new SaveFileDialog()) {
                openFileDialog.Filter = saveFileDialogFilter;
                openFileDialog.FileName = "ProtectedWorkbook.xlsx";
                if(openFileDialog.ShowDialog() == DialogResult.OK)
                    ProtectAndSaveDocument(openFileDialog.FileName);
            }
        }

        void ProtectAndSaveDocument(string filePath) {
            try {
                using(Workbook workbook = new Workbook()) {
                    workbook.LoadDocument(DemoUtils.GetRelativePath("SimpleMonthlyBudget.xltx"));
                    if(cbProtectWorkbook.Checked)
                        workbook.Protect(edWorkbookPassword.Text, cbProtectWorkbookStructure.Checked, cbProtectWorkbookWindows.Checked);
                    if(cbProtectWorksheet.Checked) {
                        WorksheetProtectionPermissions permissions = GetPermissions();
                        workbook.Worksheets[0].Protect(edWorksheetPassword.Text, permissions);
                    }
                    workbook.SaveDocument(filePath);
                }
                DemoUtils.PreviewDocument(filePath);
            }
            catch(Exception ex) {
                XtraMessageBox.Show(ex.Message, Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        WorksheetProtectionPermissions GetPermissions() {
            WorksheetProtectionPermissions permissions = 0;
            foreach(int index in edPermissions.CheckedIndices)
                permissions |= (WorksheetProtectionPermissions)(0x01 << index);
            return permissions;
        }
    }
}
