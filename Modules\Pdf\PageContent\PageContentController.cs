﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using DevExpress.Pdf;
using DevExpress.XtraPdfViewer;

namespace DevExpress.Docs.Demos {
    public class PageContentController : IDisposable, IDocumentController {
        readonly string editingTempFilePath;
        readonly string sourceTempFilePath;
        readonly IList<PageContentData> contents = new List<PageContentData>();
        readonly PdfViewer pdfViewer;
        readonly Pen inactive = new Pen(SystemColors.ActiveBorder) { DashStyle = DashStyle.Dash };
        public static readonly Pen highlight = new Pen(SystemColors.Highlight, 2) { DashStyle = DashStyle.Dash };

        public int PageCount {
            get { return pdfViewer.PageCount; }
        }
        public PageContentController(PdfViewer pdfViewer, string sourceTempFilePath, string editingTempFilePath) {
            this.pdfViewer = pdfViewer;
            this.sourceTempFilePath = sourceTempFilePath;
            this.editingTempFilePath = editingTempFilePath;
            using(PdfDocumentProcessor processor = new PdfDocumentProcessor()) {
                processor.LoadDocument(sourceTempFilePath);
            }
        }
        public void AddNewPage(PdfRectangle paperSize) {
            using(PdfDocumentProcessor processor = new PdfDocumentProcessor()) {
                processor.LoadDocument(sourceTempFilePath);
                processor.AddNewPage(paperSize);
                processor.SaveDocument(sourceTempFilePath);
            }
            UpdateDocument();
        }
        public void UpdateDocument() {
            float scrollPosition = pdfViewer.VerticalScrollPosition;
            pdfViewer.CloseDocument();
            try {
                using(PdfDocumentProcessor processor = new PdfDocumentProcessor()) {
                    processor.LoadDocument(sourceTempFilePath);
                    Dictionary<int, PdfGraphics> graphics = new Dictionary<int, PdfGraphics>();
                    try {
                        foreach(PageContentData pageContentData in contents) {
                            int pageIndex = pageContentData.PageNumber - 1;
                            PdfGraphics g = null;
                            if(!graphics.TryGetValue(pageIndex, out g)) {
                                g = processor.CreateGraphics();
                                graphics[pageIndex] = g;
                            }
                            pageContentData.FillContent(g, processor.Document.Pages[pageIndex].CropBox);
                        }
                    }
                    finally {
                        foreach(KeyValuePair<int, PdfGraphics> kvp in graphics) {
                            kvp.Value.AddToPageForeground(processor.Document.Pages[kvp.Key], 72, 72);
                            kvp.Value.Dispose();
                        }
                    }
                    processor.SaveDocument(editingTempFilePath);
                }
            }
            finally {
                new PdfViewerFileHelper(pdfViewer).LoadDocument(editingTempFilePath);
                pdfViewer.VerticalScrollPosition = scrollPosition;
            }
        }
        public void AddContent(PageContentData content) {
            contents.Add(content);
            UpdateDocument();
        }
        public void RemoveContent(PageContentData content) {
            contents.Remove(content);
            UpdateDocument();
        }
        public void BringToFront(PageContentData content) {
            contents.Remove(content);
            contents.Add(content);
            UpdateDocument();
        }
        public void BringForward(PageContentData content) {
            int index = contents.IndexOf(content);
            contents.Remove(content);
            contents.Insert(Math.Min(contents.Count, index + 1), content);
            UpdateDocument();
        }
        public void SendBackward(PageContentData content) {
            int index = contents.IndexOf(content);
            contents.Remove(content);
            contents.Insert(Math.Max(0, index - 1), content);
            UpdateDocument();
        }
        public void SendToBack(PageContentData content) {
            contents.Remove(content);
            contents.Insert(0, content);
            UpdateDocument();
        }
        public PageContentData GetFormFieldFromPoint(Point point) {
            foreach(PageContentData item in contents)
                if(item.ContainsPosition(pdfViewer.GetDocumentPosition(point)))
                    return item;
            return null;
        }
        public bool HasFormFieldAtPoint(Point point, PageContentData current) {
            return current != null && current.ContainsPosition(pdfViewer.GetDocumentPosition(point));
        }
        public PageContentData GetNextFormFieldFromPoint(Point point, PageContentData current) {
            bool check = false;
            foreach(PageContentData item in contents) {
                if(item.ContainsPosition(pdfViewer.GetDocumentPosition(point))) {
                    if(check)
                        return item;
                    else if(current == item)
                        check = true;
                }
            }
            foreach(PageContentData item in contents) {
                if(item.ContainsPosition(pdfViewer.GetDocumentPosition(point)))
                    return item;
            }
            return null;
        }
        public void RemoveExistingForm() {
            using(PdfDocumentProcessor processor = new PdfDocumentProcessor()) {
                processor.LoadDocument(sourceTempFilePath);
                processor.RemoveForm();
                processor.SaveDocument(sourceTempFilePath);
            }
            UpdateDocument();
        }
        public void Draw(Graphics graphics) {
            foreach(PageContentData item in contents)
                DrawItem(graphics, item.GetClientRectangle(pdfViewer));
        }
        public void DrawSelectedItem(Graphics graphics, Rectangle itemRect) {
            graphics.DrawRectangle(highlight, itemRect);
        }
        public void DrawItem(Graphics graphics, Rectangle itemRect) {
            graphics.DrawRectangle(inactive, itemRect);
        }
        public void Dispose() {
            foreach(PageContentData field in contents)
                field.Dispose();
            contents.Clear();
            inactive.Dispose();
            highlight.Dispose();
        }
    }
}
