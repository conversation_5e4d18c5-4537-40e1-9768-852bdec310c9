﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Globalization;
using DevExpress.Pdf;

namespace DevExpress.Docs.Demos {
    public class PdfRGBColorConverter : ColorConverter {
        public override bool CanConvertTo(ITypeDescriptorContext context, Type destinationType) {
            if(destinationType == typeof(string))
                return true;
            return base.CanConvertTo(context, destinationType);
        }
        public override object ConvertTo(ITypeDescriptorContext context, CultureInfo culture, object value, Type destinationType) {
            if(destinationType == typeof(string))
                return converterCore.ConvertTo(context, culture, ToColor(value), typeof(string));
            return base.ConvertTo(context, culture, value, destinationType);
        }

        public override bool CanConvertFrom(ITypeDescriptorContext context, Type sourceType) {
            if(sourceType == typeof(string))
                return true;
            if(sourceType == typeof(Color))
                return true;
            return base.CanConvertFrom(context, sourceType);
        }
        public override object ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, object value) {
            if(value is Color)
                return ToPdfRGBColor(value);
            return ConvertFrom(context, culture, value);
        }
        public static ColorConverter converterCore = new ColorConverter();
        public static string ToString(object value) {
            return converterCore.ConvertToString(value);
        }
        public static Color ToColor(object value) {
            if(value is PdfRGBColor) {
                PdfRGBColor colorValue = ((PdfRGBColor)value);
                return Color.FromArgb((int)(colorValue.R * 255), (int)(colorValue.G * 255), (int)(colorValue.B * 255));
            }
            if(value is Color) return (Color)value;
            if(value is int) return Color.FromArgb((int)value);
            if(value is string) {
                try { return (Color)converterCore.ConvertFromString(value.ToString()); }
                catch { }
            }
            return Color.Empty;
        }
        public static PdfRGBColor ToPdfRGBColor(object value) {
            if(value is Color) {
                Color color = (Color)value;
                return new PdfRGBColor(color.R / 255.0, color.G / 255.0, color.B / 255.0);
            }
            if(value is string) {
                try {
                    Color color = ToColor(value);
                    return new PdfRGBColor(color.R / 255.0, color.G / 255.0, color.B / 255.0);
                }
                catch { }
            }
            return new PdfRGBColor();
        }
    }
}
