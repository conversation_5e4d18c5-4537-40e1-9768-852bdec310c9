﻿using System;
using System.ComponentModel;
using System.Drawing;
using DevExpress.Pdf;
using DevExpress.XtraPdfViewer;

namespace DevExpress.Docs.Demos {
    public abstract class PdfContentData : IDisposable {
        protected const string AppearanceCategory = "Appearance";
        protected const string LayoutCategory = "Layout";
        protected const string DesignCategory = "Design";
        protected const string BehaviorCategory = "Behavior";

        readonly IDocumentController controller;
        readonly PdfContentRectangle fRectangle;
        int fPageNumber;

        protected IDocumentController Controller { get { return controller; } }

        [Category(LayoutCategory)]
        public int PageNumber {
            get { return fPageNumber; }
            set {
                if(value < 1 || value > controller.PageCount)
                    throw new ArgumentOutOfRangeException("PageNumber", String.Format("The page number should be in the range from 1 to {0}.", controller.PageCount));
                fPageNumber = value;
                UpdateModel();
            }
        }

        [Category(LayoutCategory)]
        [TypeConverter(typeof(ExpandableObjectConverter))]
        public PdfContentRectangle Rectangle { get { return fRectangle; } }

        public PdfContentData(PdfDocumentPosition position, IDocumentController controller) {
            this.controller = controller;
            fPageNumber = position.PageNumber;
            fRectangle = new PdfContentRectangle(position.Point, ExpandToRectangle(position.Point), controller);
        }
        protected virtual PdfPoint ExpandToRectangle(PdfPoint point) {
            return new PdfPoint(point.X + 100, point.Y - 50);
        }
        protected void UpdateModel() {
            controller.UpdateDocument();
        }

        public Rectangle GetClientRectangle(PdfViewer viewer) {
            PdfRectangle innerRectangle = Rectangle.InnerRectangle;
            PointF startPoint = viewer.GetClientPoint(new PdfDocumentPosition(PageNumber, innerRectangle.TopLeft));
            PointF endPoint = viewer.GetClientPoint(new PdfDocumentPosition(PageNumber, innerRectangle.BottomRight));
            return System.Drawing.Rectangle.Round(RectangleF.FromLTRB(Math.Min(startPoint.X, endPoint.X), Math.Min(startPoint.Y, endPoint.Y),
                    Math.Max(startPoint.X, endPoint.X), Math.Max(startPoint.Y, endPoint.Y)));
        }
        public bool ContainsPosition(PdfDocumentPosition position) {
            if(PageNumber != position.PageNumber)
                return false;
            PdfRectangle innerRect = Rectangle.InnerRectangle;
            PdfPoint point = position.Point;
            return innerRect.Bottom <= point.Y && innerRect.Top >= point.Y && innerRect.Left <= point.X && innerRect.Right >= point.X;
        }
        public virtual void Dispose() {
        }
    }
}
