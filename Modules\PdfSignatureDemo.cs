﻿using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Security.Cryptography;
using System.Windows.Forms;
using DevExpress.DXperience.Demos;
using DevExpress.Office.DigitalSignatures;
using DevExpress.Office.Tsp;
using DevExpress.Pdf;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;

namespace DevExpress.Docs.Demos {
    public partial class PdfSignatureDemo : TutorialControlBase {
        readonly PdfDocumentSignerFileHelper fileHelper;
        byte[] imageData;
        PdfSignatureAppearance SignatureAppearance;
        SignatureAppearanceBuilderForm appearanceBuilderForm;
        public override bool NoGap {
            get { return true; }
        }
        public PdfSignatureDemo() {
            InitializeComponent();
            ComboBoxItemCollection comboBoxItems = hashAlgorithmComboBox.Properties.Items;
            foreach(object item in Enum.GetValues(typeof(HashAlgorithmType)))
                comboBoxItems.Add(item);
            hashAlgorithmComboBox.SelectedItem = HashAlgorithmType.SHA256;
            ComboBoxItemCollection certificationLevelItems = certificationLevelComboBox.Properties.Items;
            certificationLevelItems.Add("No Certification");
            certificationLevelItems.Add("No Changes Allowed");
            certificationLevelItems.Add("Fill Forms");
            certificationLevelItems.Add("Fill Forms And Annotate");
            certificationLevelComboBox.SelectedIndex = 0;
            fileHelper = new PdfDocumentSignerFileHelper(pdfViewer);
            LoadDocument();
            try {
                imageData = File.ReadAllBytes(DemoUtils.GetRelativePath("Faximile.emf"));
                imagePictureEdit.EditValue = new Bitmap(new MemoryStream(imageData));
            }
            catch {
                XtraMessageBox.Show(PdfFileHelper.DemoOpeningErrorMessage, "Error");
                Enabled = false;
            }
            if(Enabled) {
                try {
                    lbCerts.Items.Add(CertificateItem.Create(DemoUtils.GetRelativePath("SignDemo.pfx"), "dxdemo"));
                    lbCerts.SelectedIndex = 0;
                    lbCerts.DisplayMember = "Subject";
                }
                catch(CryptographicException) {
                    Enabled = false;
                }
            }
        }
        void LoadDocument() {
            Enabled = fileHelper.LoadDemoDocument("SignDemo.pdf");
        }
        void OnButtonSignClick(object sender, EventArgs e) {
            try {
                PdfSignatureBuilder signature;
                CertificateItem cert = ((CertificateItem)lbCerts.SelectedItem);
                string tsaUri = tsaURITextEdit.Text;
                TsaClient tsa;
                if(!string.IsNullOrEmpty(tsaUri))
                    tsa = new TsaClient(new Uri(tsaUri, UriKind.Absolute), HashAlgorithmType.SHA256);
                else
                    tsa = null;
                Pkcs7Signer signer = new Pkcs7Signer(cert.FilePath, cert.Password, (HashAlgorithmType)hashAlgorithmComboBox.SelectedItem, tsa);
                if(SignatureAppearance != null) {
                    PdfSignatureFieldInfo fieldInfo = new PdfSignatureFieldInfo(1);
                    fieldInfo.SignatureBounds = new PdfRectangle(39.4, 254, 482, 286);
                    signature = new PdfSignatureBuilder(signer, fieldInfo);
                    signature.SetSignatureAppearance(SignatureAppearance);
                }
                else if(imageData != null) {
                    PdfSignatureFieldInfo fieldInfo = new PdfSignatureFieldInfo(1);
                    fieldInfo.SignatureBounds = new PdfRectangle(39.4, 254, 482, 286);
                    signature = new PdfSignatureBuilder(signer, fieldInfo);
                    signature.SetImageData(imageData);
                }
                else {
                    signature = new PdfSignatureBuilder(signer, new PdfSignatureFieldInfo(1));
                }
                signature.Location = teLocation.Text;
                signature.ContactInfo = teContactInfo.Text;
                signature.Reason = teReason.Text;
                signature.CertificationLevel = (PdfCertificationLevel)certificationLevelComboBox.SelectedIndex;
                fileHelper.SignDocument(signature);
            }
            catch(Exception exception) {
                XtraMessageBox.Show(exception.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            LoadDocument();
        }
        void OnButtonNewCertificateClick(object sender, EventArgs e) {
            using(OpenFileDialog openDialog = new OpenFileDialog()) {
                openDialog.Filter = "X.509 Certificate (*.cer; *.crt, *.pfx)|*.cer;*.crt;*.pfx";
                openDialog.RestoreDirectory = true;
                if(openDialog.ShowDialog() == DialogResult.OK) {
                    CertificateItem cert = CertificateItem.Create(openDialog.FileName, null);
                    if(cert != null) {
                        lbCerts.Items.Add(cert);
                        lbCerts.SelectedIndex = lbCerts.Items.Count - 1;
                    }
                }
            }
        }
        void OnSignatureImageChanged(object sender, EventArgs e) {
            Image image = imagePictureEdit.EditValue as Image;
            if(image != null) {
                using(MemoryStream stream = new MemoryStream()) {
                    image.Save(stream, ImageFormat.Png);
                    if(appearanceBuilderForm == null)
                        SignatureAppearance = null;
                    imageData = stream.ToArray();
                }
            }
            else imageData = null;
        }
        void appearanceBuilderButton_Click(object sender, EventArgs e) {
            CertificateItem cert = ((CertificateItem)lbCerts.SelectedItem);
            Pkcs7Signer signer = new Pkcs7Signer(cert.FilePath, cert.Password, (HashAlgorithmType)hashAlgorithmComboBox.SelectedItem, null);
            PdfSignatureBuilder builder = new PdfSignatureBuilder(signer) {
                Location = teLocation.Text,
                ContactInfo = teContactInfo.Text,
                Reason = teReason.Text
            };
            using(SignatureAppearanceBuilderForm form = new SignatureAppearanceBuilderForm(builder)) {
                appearanceBuilderForm = form;
                if(appearanceBuilderForm.ShowDialog() == DialogResult.OK) {
                    SignatureAppearance = appearanceBuilderForm.CreateSignatureAppearance();
                    imagePictureEdit.Image = appearanceBuilderForm.GetImage();
                }
                appearanceBuilderForm = null;
            }
        }
        protected override void Dispose(bool disposing) {
            if(disposing) {
                if(components != null)
                    components.Dispose();
                fileHelper.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
