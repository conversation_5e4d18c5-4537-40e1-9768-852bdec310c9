﻿using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Windows.Forms;
using DevExpress.DXperience.Demos;
using DevExpress.Office.DigitalSignatures;
using DevExpress.Office.Tsp;
using DevExpress.Pdf;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using System.Collections.Generic;
using System.Linq;

namespace DevExpress.Docs.Demos {

    // USB Token item for PKCS#11 support
    public class UsbTokenItem {
        public string SlotDescription { get; set; }
        public string TokenLabel { get; set; }
        public uint SlotId { get; set; }
        public string DisplayName => $"{TokenLabel} (Slot: {SlotId})";

        public override string ToString() => DisplayName;
    }

    // PKCS#11 Signer implementation
    public class Pkcs11Signer : Pkcs7SignerBase {
        private readonly X509Certificate2 certificate;
        private readonly uint slotId;
        private readonly string pin;
        private readonly string pkcs11LibraryPath;

        public Pkcs11Signer(X509Certificate2 cert, uint slotId, string pin, string libraryPath,
                           HashAlgorithmType hashAlgorithm, ITsaClient tsaClient = null)
            : base(tsaClient, null, null, PdfSignatureProfile.Pdf) {
            this.certificate = cert;
            this.slotId = slotId;
            this.pin = pin;
            this.pkcs11LibraryPath = libraryPath;
            this.HashAlgorithm = hashAlgorithm;
        }

        protected override IEnumerable<byte[]> GetCertificates() {
            yield return certificate.RawData;
        }

        protected override byte[] SignDigest(byte[] digest) {
            // This is a simplified implementation. In a real scenario, you would use
            // a PKCS#11 library like pkcs11interop to interact with the USB token
            // For demonstration purposes, we'll use the certificate's private key if available

            try {
                if (certificate.HasPrivateKey) {
                    using (var rsa = certificate.GetRSAPrivateKey()) {
                        if (rsa != null) {
                            return rsa.SignHash(digest, GetHashAlgorithmName(), RSASignaturePadding.Pkcs1);
                        }
                    }
                }

                // In a real implementation, you would:
                // 1. Initialize PKCS#11 library
                // 2. Open session with the token
                // 3. Login with PIN
                // 4. Find the private key object
                // 5. Perform the signing operation
                // 6. Close session and finalize

                throw new InvalidOperationException("Unable to sign with USB token. PKCS#11 library integration required.");
            }
            catch (Exception ex) {
                throw new CryptographicException($"Error signing with USB token: {ex.Message}", ex);
            }
        }

        private HashAlgorithmName GetHashAlgorithmName() {
            switch (HashAlgorithm) {
                case HashAlgorithmType.SHA1:
                    return HashAlgorithmName.SHA1;
                case HashAlgorithmType.SHA256:
                    return HashAlgorithmName.SHA256;
                case HashAlgorithmType.SHA384:
                    return HashAlgorithmName.SHA384;
                case HashAlgorithmType.SHA512:
                    return HashAlgorithmName.SHA512;
                default:
                    return HashAlgorithmName.SHA256;
            }
        }
    }
    public partial class PdfSignatureDemo : TutorialControlBase {
        readonly PdfDocumentSignerFileHelper fileHelper;
        byte[] imageData;
        PdfSignatureAppearance SignatureAppearance;
        SignatureAppearanceBuilderForm appearanceBuilderForm;
        private List<UsbTokenItem> availableTokens;
        private bool useUsbToken = false;
        public override bool NoGap {
            get { return true; }
        }
        public PdfSignatureDemo() {
            InitializeComponent();
            ComboBoxItemCollection comboBoxItems = hashAlgorithmComboBox.Properties.Items;
            foreach(object item in Enum.GetValues(typeof(HashAlgorithmType)))
                comboBoxItems.Add(item);
            hashAlgorithmComboBox.SelectedItem = HashAlgorithmType.SHA256;
            ComboBoxItemCollection certificationLevelItems = certificationLevelComboBox.Properties.Items;
            certificationLevelItems.Add("No Certification");
            certificationLevelItems.Add("No Changes Allowed");
            certificationLevelItems.Add("Fill Forms");
            certificationLevelItems.Add("Fill Forms And Annotate");
            certificationLevelComboBox.SelectedIndex = 0;
            fileHelper = new PdfDocumentSignerFileHelper(pdfViewer);

            // Initialize USB token support
            availableTokens = new List<UsbTokenItem>();
            InitializeUsbTokenSupport();

            LoadDocument();
            try {
                imageData = File.ReadAllBytes(DemoUtils.GetRelativePath("Faximile.emf"));
                imagePictureEdit.EditValue = new Bitmap(new MemoryStream(imageData));
            }
            catch {
                XtraMessageBox.Show(PdfFileHelper.DemoOpeningErrorMessage, "Error");
                Enabled = false;
            }
            if(Enabled) {
                try {
                    lbCerts.Items.Add(CertificateItem.Create(DemoUtils.GetRelativePath("SignDemo.pfx"), "dxdemo"));
                    lbCerts.SelectedIndex = 0;
                    lbCerts.DisplayMember = "Subject";
                }
                catch(CryptographicException) {
                    Enabled = false;
                }
            }
        }
        void LoadDocument() {
            Enabled = fileHelper.LoadDemoDocument("SignDemo.pdf");
        }

        void InitializeUsbTokenSupport() {
            try {
                // Detect available USB tokens
                DetectUsbTokens();

                // Add USB token option to certificate list if tokens are available
                if (availableTokens.Count > 0) {
                    // Add a separator item to distinguish between PFX files and USB tokens
                    lbCerts.Items.Add("--- USB Tokens ---");

                    foreach (var token in availableTokens) {
                        lbCerts.Items.Add(token);
                    }
                }
            }
            catch (Exception ex) {
                // USB token detection failed, continue with PFX-only support
                System.Diagnostics.Debug.WriteLine($"USB token detection failed: {ex.Message}");
            }
        }

        void DetectUsbTokens() {
            // This is a simplified detection method. In a real implementation, you would:
            // 1. Load PKCS#11 library (e.g., from Windows Certificate Store or specific vendor libraries)
            // 2. Initialize PKCS#11
            // 3. Get slot list
            // 4. Get token info for each slot
            // 5. Enumerate certificates on each token

            try {
                // For demonstration, we'll check the Windows Certificate Store for certificates
                // that might be stored on smart cards or USB tokens
                var store = new X509Store(StoreName.My, StoreLocation.CurrentUser);
                store.Open(OpenFlags.ReadOnly);

                uint slotId = 0;
                foreach (var cert in store.Certificates) {
                    // Check if certificate might be on a hardware token
                    // This is a simplified check - in reality you'd use PKCS#11 APIs
                    if (IsLikelyHardwareToken(cert)) {
                        var tokenItem = new UsbTokenItem {
                            SlotId = slotId++,
                            TokenLabel = GetTokenLabel(cert),
                            SlotDescription = $"Hardware Token - {cert.Subject}"
                        };
                        availableTokens.Add(tokenItem);
                    }
                }

                store.Close();
            }
            catch (Exception ex) {
                throw new InvalidOperationException($"Failed to detect USB tokens: {ex.Message}", ex);
            }
        }

        bool IsLikelyHardwareToken(X509Certificate2 cert) {
            // Simple heuristic to determine if certificate might be on hardware token
            // In a real implementation, you would check the certificate's key storage flags
            // or use PKCS#11 APIs to determine the actual location

            try {
                // Check if the certificate has a private key but it's not exportable
                // This often indicates hardware storage
                if (cert.HasPrivateKey) {
                    // Try to access the private key - hardware tokens often have different behavior
                    using (var key = cert.GetRSAPrivateKey()) {
                        // If we can get the key but it's marked as non-exportable,
                        // it might be on a hardware token
                        return key != null;
                    }
                }
            }
            catch {
                // If we can't access the private key easily, it might be on hardware
                return cert.HasPrivateKey;
            }

            return false;
        }

        string GetTokenLabel(X509Certificate2 cert) {
            // Extract a friendly name for the token from the certificate
            var subject = cert.Subject;
            var commonName = ExtractCommonName(subject);
            return !string.IsNullOrEmpty(commonName) ? commonName : "USB Token";
        }

        string ExtractCommonName(string subject) {
            // Simple CN extraction from certificate subject
            var parts = subject.Split(',');
            foreach (var part in parts) {
                var trimmed = part.Trim();
                if (trimmed.StartsWith("CN=")) {
                    return trimmed.Substring(3);
                }
            }
            return null;
        }
        void OnButtonSignClick(object sender, EventArgs e) {
            try {
                PdfSignatureBuilder signature;
                var selectedItem = lbCerts.SelectedItem;

                string tsaUri = tsaURITextEdit.Text;
                TsaClient tsa;
                if(!string.IsNullOrEmpty(tsaUri))
                    tsa = new TsaClient(new Uri(tsaUri, UriKind.Absolute), HashAlgorithmType.SHA256);
                else
                    tsa = null;

                Pkcs7SignerBase signer;

                // Check if selected item is a USB token
                if (selectedItem is UsbTokenItem usbToken) {
                    signer = CreateUsbTokenSigner(usbToken, (HashAlgorithmType)hashAlgorithmComboBox.SelectedItem, tsa);
                }
                else if (selectedItem is CertificateItem cert) {
                    signer = new Pkcs7Signer(cert.FilePath, cert.Password, (HashAlgorithmType)hashAlgorithmComboBox.SelectedItem, tsa);
                }
                else {
                    XtraMessageBox.Show("Please select a certificate or USB token to sign with.", "No Certificate Selected", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                if(SignatureAppearance != null) {
                    PdfSignatureFieldInfo fieldInfo = new PdfSignatureFieldInfo(1);
                    fieldInfo.SignatureBounds = new PdfRectangle(39.4, 254, 482, 286);
                    signature = new PdfSignatureBuilder(signer, fieldInfo);
                    signature.SetSignatureAppearance(SignatureAppearance);
                }
                else if(imageData != null) {
                    PdfSignatureFieldInfo fieldInfo = new PdfSignatureFieldInfo(1);
                    fieldInfo.SignatureBounds = new PdfRectangle(39.4, 254, 482, 286);
                    signature = new PdfSignatureBuilder(signer, fieldInfo);
                    signature.SetImageData(imageData);
                }
                else {
                    signature = new PdfSignatureBuilder(signer, new PdfSignatureFieldInfo(1));
                }
                signature.Location = teLocation.Text;
                signature.ContactInfo = teContactInfo.Text;
                signature.Reason = teReason.Text;
                signature.CertificationLevel = (PdfCertificationLevel)certificationLevelComboBox.SelectedIndex;
                fileHelper.SignDocument(signature);
            }
            catch(Exception exception) {
                XtraMessageBox.Show(exception.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            LoadDocument();
        }

        Pkcs7SignerBase CreateUsbTokenSigner(UsbTokenItem usbToken, HashAlgorithmType hashAlgorithm, TsaClient tsa) {
            // Prompt for PIN
            string pin = PromptForPin();
            if (string.IsNullOrEmpty(pin)) {
                throw new OperationCanceledException("PIN entry was cancelled.");
            }

            // Get the certificate from the token
            X509Certificate2 certificate = GetCertificateFromToken(usbToken);
            if (certificate == null) {
                throw new InvalidOperationException("Could not retrieve certificate from USB token.");
            }

            // Create PKCS#11 signer
            // Note: You would need to specify the actual PKCS#11 library path for your USB token
            string pkcs11LibraryPath = GetPkcs11LibraryPath();

            return new Pkcs11Signer(certificate, usbToken.SlotId, pin, pkcs11LibraryPath, hashAlgorithm, tsa);
        }

        string PromptForPin() {
            using (var pinForm = new PinEntryForm()) {
                if (pinForm.ShowDialog() == DialogResult.OK) {
                    return pinForm.Pin;
                }
                return null;
            }
        }

        X509Certificate2 GetCertificateFromToken(UsbTokenItem usbToken) {
            // In a real implementation, you would use PKCS#11 APIs to get the certificate
            // For this demo, we'll try to find it in the certificate store
            try {
                var store = new X509Store(StoreName.My, StoreLocation.CurrentUser);
                store.Open(OpenFlags.ReadOnly);

                foreach (var cert in store.Certificates) {
                    if (IsLikelyHardwareToken(cert) && GetTokenLabel(cert) == usbToken.TokenLabel) {
                        store.Close();
                        return cert;
                    }
                }

                store.Close();
                return null;
            }
            catch (Exception ex) {
                throw new InvalidOperationException($"Failed to retrieve certificate from token: {ex.Message}", ex);
            }
        }

        string GetPkcs11LibraryPath() {
            // Return the path to the PKCS#11 library for the USB token
            // This would typically be provided by the token manufacturer
            // Common paths include:
            // - "C:\\Windows\\System32\\eTPKCS11.dll" (for some tokens)
            // - "C:\\Program Files\\TokenVendor\\pkcs11.dll"
            // For this demo, we'll return a placeholder
            return "pkcs11.dll"; // This should be the actual path to your PKCS#11 library
        }
        void OnButtonNewCertificateClick(object sender, EventArgs e) {
            using(OpenFileDialog openDialog = new OpenFileDialog()) {
                openDialog.Filter = "X.509 Certificate (*.cer; *.crt, *.pfx)|*.cer;*.crt;*.pfx";
                openDialog.RestoreDirectory = true;
                if(openDialog.ShowDialog() == DialogResult.OK) {
                    CertificateItem cert = CertificateItem.Create(openDialog.FileName, null);
                    if(cert != null) {
                        lbCerts.Items.Add(cert);
                        lbCerts.SelectedIndex = lbCerts.Items.Count - 1;
                    }
                }
            }
        }
        void OnSignatureImageChanged(object sender, EventArgs e) {
            Image image = imagePictureEdit.EditValue as Image;
            if(image != null) {
                using(MemoryStream stream = new MemoryStream()) {
                    image.Save(stream, ImageFormat.Png);
                    if(appearanceBuilderForm == null)
                        SignatureAppearance = null;
                    imageData = stream.ToArray();
                }
            }
            else imageData = null;
        }
        void appearanceBuilderButton_Click(object sender, EventArgs e) {
            var selectedItem = lbCerts.SelectedItem;
            Pkcs7SignerBase signer;

            try {
                // Check if selected item is a USB token or certificate
                if (selectedItem is UsbTokenItem usbToken) {
                    signer = CreateUsbTokenSigner(usbToken, (HashAlgorithmType)hashAlgorithmComboBox.SelectedItem, null);
                }
                else if (selectedItem is CertificateItem cert) {
                    signer = new Pkcs7Signer(cert.FilePath, cert.Password, (HashAlgorithmType)hashAlgorithmComboBox.SelectedItem, null);
                }
                else {
                    XtraMessageBox.Show("Please select a certificate or USB token.", "No Certificate Selected", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                PdfSignatureBuilder builder = new PdfSignatureBuilder(signer) {
                    Location = teLocation.Text,
                    ContactInfo = teContactInfo.Text,
                    Reason = teReason.Text
                };

                using(SignatureAppearanceBuilderForm form = new SignatureAppearanceBuilderForm(builder)) {
                    appearanceBuilderForm = form;
                    if(appearanceBuilderForm.ShowDialog() == DialogResult.OK) {
                        SignatureAppearance = appearanceBuilderForm.CreateSignatureAppearance();
                        imagePictureEdit.Image = appearanceBuilderForm.GetImage();
                    }
                    appearanceBuilderForm = null;
                }
            }
            catch (Exception ex) {
                XtraMessageBox.Show($"Error creating signature appearance: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        protected override void Dispose(bool disposing) {
            if(disposing) {
                if(components != null)
                    components.Dispose();
                fileHelper.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
