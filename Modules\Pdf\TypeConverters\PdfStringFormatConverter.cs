﻿using System;
using System.Collections;
using System.ComponentModel;
using System.Globalization;
using DevExpress.Pdf;

namespace DevExpress.Docs.Demos {
    public class PdfStringFormatConverter : ExpandableObjectConverter {
        public override object CreateInstance(ITypeDescriptorContext context, IDictionary propertyValues) {
            return new PdfStringFormat() {
                Alignment = (PdfStringAlignment)propertyValues["Alignment"],
                LineAlignment = (PdfStringAlignment)propertyValues["LineAlignment"],
                FormatFlags = (PdfStringFormatFlags)propertyValues["FormatFlags"],
                Trimming = (PdfStringTrimming)propertyValues["Trimming"],
                HotkeyPrefix = (PdfHotkeyPrefix)propertyValues["HotkeyPrefix"],
                LeadingMarginFactor = (double)propertyValues["LeadingMarginFactor"],
                TrailingMarginFactor = (double)propertyValues["TrailingMarginFactor"]
            };
        }
        public override bool GetCreateInstanceSupported(ITypeDescriptorContext context) {
            return true;
        }
        public override object ConvertTo(ITypeDescriptorContext context, CultureInfo culture, object value, Type destinationType) {
            PdfStringFormat f = value as PdfStringFormat;
            if(destinationType == typeof(String) && f != null)
                return string.Format(culture, "Alignment={0}, LineAlignment={1}", f.Alignment, f.LineAlignment);
            return base.ConvertTo(context, culture, value, destinationType);
        }
    }
}
