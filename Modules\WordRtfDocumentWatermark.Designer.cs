﻿
using DevExpress.XtraEditors;

namespace DevExpress.Docs.Demos {
    partial class WordRtfDocumentWatermark {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing) {
            if (disposing && (components != null)) {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent() {
            this.printPreviewControl = new DevExpress.XtraPrinting.Control.PrintControl();
            this.sidePanel1 = new DevExpress.XtraEditors.SidePanel();
            this.cmbboxWatermarkText = new DevExpress.XtraEditors.ComboBoxEdit();
            this.lblImageWatermark = new DevExpress.XtraEditors.LabelControl();
            this.cmbboxFontSize = new DevExpress.XtraEditors.ComboBoxEdit();
            this.buttonSetImageWatermark = new DevExpress.XtraEditors.SimpleButton();
            this.lblFontSize = new DevExpress.XtraEditors.LabelControl();
            this.chckedtWashout = new DevExpress.XtraEditors.CheckEdit();
            this.buttonExportToPdf = new DevExpress.XtraEditors.SimpleButton();
            this.buttonDeleteWatermark = new DevExpress.XtraEditors.SimpleButton();
            this.lblTextWatermark = new DevExpress.XtraEditors.LabelControl();
            this.lblWatermarkText = new DevExpress.XtraEditors.LabelControl();
            this.chckedtSemitransparent = new DevExpress.XtraEditors.CheckEdit();
            this.lblFontName = new DevExpress.XtraEditors.LabelControl();
            this.clredtTextColor = new DevExpress.XtraEditors.ColorEdit();
            this.lblColor = new DevExpress.XtraEditors.LabelControl();
            this.chckedtDiagonal = new DevExpress.XtraEditors.CheckEdit();
            this.fntedtFontName = new DevExpress.XtraEditors.FontEdit();
            this.buttonSetTextWatermark = new DevExpress.XtraEditors.SimpleButton();
            this.sidePanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbboxWatermarkText.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbboxFontSize.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chckedtWashout.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chckedtSemitransparent.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.clredtTextColor.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chckedtDiagonal.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.fntedtFontName.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // printPreviewControl
            // 
            this.printPreviewControl.AutoSize = true;
            this.printPreviewControl.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.printPreviewControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.printPreviewControl.IsMetric = false;
            this.printPreviewControl.Location = new System.Drawing.Point(0, 0);
            this.printPreviewControl.Name = "printPreviewControl";
            this.printPreviewControl.Size = new System.Drawing.Size(583, 432);
            this.printPreviewControl.TabIndex = 54;
            // 
            // sidePanel1
            // 
            this.sidePanel1.AllowResize = false;
            this.sidePanel1.Controls.Add(this.cmbboxWatermarkText);
            this.sidePanel1.Controls.Add(this.lblImageWatermark);
            this.sidePanel1.Controls.Add(this.cmbboxFontSize);
            this.sidePanel1.Controls.Add(this.buttonSetImageWatermark);
            this.sidePanel1.Controls.Add(this.lblFontSize);
            this.sidePanel1.Controls.Add(this.chckedtWashout);
            this.sidePanel1.Controls.Add(this.buttonExportToPdf);
            this.sidePanel1.Controls.Add(this.buttonDeleteWatermark);
            this.sidePanel1.Controls.Add(this.lblTextWatermark);
            this.sidePanel1.Controls.Add(this.lblWatermarkText);
            this.sidePanel1.Controls.Add(this.chckedtSemitransparent);
            this.sidePanel1.Controls.Add(this.lblFontName);
            this.sidePanel1.Controls.Add(this.clredtTextColor);
            this.sidePanel1.Controls.Add(this.lblColor);
            this.sidePanel1.Controls.Add(this.chckedtDiagonal);
            this.sidePanel1.Controls.Add(this.fntedtFontName);
            this.sidePanel1.Controls.Add(this.buttonSetTextWatermark);
            this.sidePanel1.Dock = System.Windows.Forms.DockStyle.Right;
            this.sidePanel1.Location = new System.Drawing.Point(583, 0);
            this.sidePanel1.Name = "sidePanel1";
            this.sidePanel1.Size = new System.Drawing.Size(201, 432);
            this.sidePanel1.TabIndex = 55;
            this.sidePanel1.Text = "sidePanel1";
            // 
            // cmbboxWatermarkText
            // 
            this.cmbboxWatermarkText.Location = new System.Drawing.Point(30, 170);
            this.cmbboxWatermarkText.Name = "cmbboxWatermarkText";
            this.cmbboxWatermarkText.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cmbboxWatermarkText.Size = new System.Drawing.Size(140, 20);
            this.cmbboxWatermarkText.TabIndex = 70;
            // 
            // lblImageWatermark
            // 
            this.lblImageWatermark.LineVisible = true;
            this.lblImageWatermark.Location = new System.Drawing.Point(20, 12);
            this.lblImageWatermark.Name = "lblImageWatermark";
            this.lblImageWatermark.Size = new System.Drawing.Size(88, 13);
            this.lblImageWatermark.TabIndex = 57;
            this.lblImageWatermark.Text = "Image watermark:";
            // 
            // cmbboxFontSize
            // 
            this.cmbboxFontSize.Location = new System.Drawing.Point(30, 305);
            this.cmbboxFontSize.Name = "cmbboxFontSize";
            this.cmbboxFontSize.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cmbboxFontSize.Size = new System.Drawing.Size(140, 20);
            this.cmbboxFontSize.TabIndex = 69;
            // 
            // buttonSetImageWatermark
            // 
            this.buttonSetImageWatermark.Location = new System.Drawing.Point(30, 58);
            this.buttonSetImageWatermark.Name = "buttonSetImageWatermark";
            this.buttonSetImageWatermark.Size = new System.Drawing.Size(140, 20);
            this.buttonSetImageWatermark.TabIndex = 54;
            this.buttonSetImageWatermark.Text = "Set image watermark";
            this.buttonSetImageWatermark.Click += new System.EventHandler(this.OnButtonSetImageWatermarkClick);
            // 
            // lblFontSize
            // 
            this.lblFontSize.LineVisible = true;
            this.lblFontSize.Location = new System.Drawing.Point(30, 286);
            this.lblFontSize.Name = "lblFontSize";
            this.lblFontSize.Size = new System.Drawing.Size(47, 13);
            this.lblFontSize.TabIndex = 68;
            this.lblFontSize.Text = "Font size:";
            // 
            // chckedtWashout
            // 
            this.chckedtWashout.Location = new System.Drawing.Point(30, 34);
            this.chckedtWashout.Name = "chckedtWashout";
            this.chckedtWashout.Properties.Caption = "Washout";
            this.chckedtWashout.Size = new System.Drawing.Size(150, 20);
            this.chckedtWashout.TabIndex = 55;
            // 
            // buttonExportToPdf
            // 
            this.buttonExportToPdf.Location = new System.Drawing.Point(20, 401);
            this.buttonExportToPdf.Name = "buttonExportToPdf";
            this.buttonExportToPdf.Size = new System.Drawing.Size(160, 20);
            this.buttonExportToPdf.TabIndex = 67;
            this.buttonExportToPdf.Text = "Export to PDF";
            this.buttonExportToPdf.Click += new System.EventHandler(this.OnButtonExportToPdfClick);
            // 
            // buttonDeleteWatermark
            // 
            this.buttonDeleteWatermark.Location = new System.Drawing.Point(20, 366);
            this.buttonDeleteWatermark.Name = "buttonDeleteWatermark";
            this.buttonDeleteWatermark.Size = new System.Drawing.Size(160, 20);
            this.buttonDeleteWatermark.TabIndex = 56;
            this.buttonDeleteWatermark.Text = "Delete watermark";
            this.buttonDeleteWatermark.Click += new System.EventHandler(this.OnButtonDeleteWatermarkClick);
            // 
            // lblTextWatermark
            // 
            this.lblTextWatermark.LineVisible = true;
            this.lblTextWatermark.Location = new System.Drawing.Point(20, 84);
            this.lblTextWatermark.Name = "lblTextWatermark";
            this.lblTextWatermark.Size = new System.Drawing.Size(80, 13);
            this.lblTextWatermark.TabIndex = 58;
            this.lblTextWatermark.Text = "Text watermark:";
            // 
            // lblWatermarkText
            // 
            this.lblWatermarkText.LineVisible = true;
            this.lblWatermarkText.Location = new System.Drawing.Point(30, 151);
            this.lblWatermarkText.Name = "lblWatermarkText";
            this.lblWatermarkText.Size = new System.Drawing.Size(80, 13);
            this.lblWatermarkText.TabIndex = 66;
            this.lblWatermarkText.Text = "Watermark text:";
            // 
            // chckedtSemitransparent
            // 
            this.chckedtSemitransparent.Location = new System.Drawing.Point(30, 103);
            this.chckedtSemitransparent.Name = "chckedtSemitransparent";
            this.chckedtSemitransparent.Properties.Caption = "Semitransparent";
            this.chckedtSemitransparent.Size = new System.Drawing.Size(150, 20);
            this.chckedtSemitransparent.TabIndex = 59;
            // 
            // lblFontName
            // 
            this.lblFontName.LineVisible = true;
            this.lblFontName.Location = new System.Drawing.Point(30, 241);
            this.lblFontName.Name = "lblFontName";
            this.lblFontName.Size = new System.Drawing.Size(55, 13);
            this.lblFontName.TabIndex = 65;
            this.lblFontName.Text = "Font name:";
            // 
            // clredtTextColor
            // 
            this.clredtTextColor.EditValue = System.Drawing.Color.Empty;
            this.clredtTextColor.Location = new System.Drawing.Point(30, 215);
            this.clredtTextColor.Name = "clredtTextColor";
            this.clredtTextColor.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.clredtTextColor.Size = new System.Drawing.Size(140, 20);
            this.clredtTextColor.TabIndex = 60;
            // 
            // lblColor
            // 
            this.lblColor.LineVisible = true;
            this.lblColor.Location = new System.Drawing.Point(30, 196);
            this.lblColor.Name = "lblColor";
            this.lblColor.Size = new System.Drawing.Size(29, 13);
            this.lblColor.TabIndex = 64;
            this.lblColor.Text = "Color:";
            // 
            // chckedtDiagonal
            // 
            this.chckedtDiagonal.Location = new System.Drawing.Point(30, 127);
            this.chckedtDiagonal.Name = "chckedtDiagonal";
            this.chckedtDiagonal.Properties.Caption = "Diagonal";
            this.chckedtDiagonal.Size = new System.Drawing.Size(150, 20);
            this.chckedtDiagonal.TabIndex = 61;
            // 
            // fntedtFontName
            // 
            this.fntedtFontName.Location = new System.Drawing.Point(30, 260);
            this.fntedtFontName.Name = "fntedtFontName";
            this.fntedtFontName.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.fntedtFontName.Size = new System.Drawing.Size(140, 20);
            this.fntedtFontName.TabIndex = 63;
            // 
            // buttonSetTextWatermark
            // 
            this.buttonSetTextWatermark.Location = new System.Drawing.Point(30, 331);
            this.buttonSetTextWatermark.Name = "buttonSetTextWatermark";
            this.buttonSetTextWatermark.Size = new System.Drawing.Size(140, 20);
            this.buttonSetTextWatermark.TabIndex = 62;
            this.buttonSetTextWatermark.Text = "Set text watermark";
            this.buttonSetTextWatermark.Click += new System.EventHandler(this.OnButtonSetTextWatermarkClick);
            // 
            // WordRtfDocumentWatermark
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.printPreviewControl);
            this.Controls.Add(this.sidePanel1);
            this.Name = "WordRtfDocumentWatermark";
            this.sidePanel1.ResumeLayout(false);
            this.sidePanel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbboxWatermarkText.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbboxFontSize.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chckedtWashout.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chckedtSemitransparent.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.clredtTextColor.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chckedtDiagonal.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.fntedtFontName.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private XtraPrinting.Control.PrintControl printPreviewControl;
        private SidePanel sidePanel1;
        private ComboBoxEdit cmbboxWatermarkText;
        private LabelControl lblImageWatermark;
        private ComboBoxEdit cmbboxFontSize;
        private SimpleButton buttonSetImageWatermark;
        private LabelControl lblFontSize;
        private CheckEdit chckedtWashout;
        private SimpleButton buttonExportToPdf;
        private SimpleButton buttonDeleteWatermark;
        private LabelControl lblTextWatermark;
        private LabelControl lblWatermarkText;
        private CheckEdit chckedtSemitransparent;
        private LabelControl lblFontName;
        private ColorEdit clredtTextColor;
        private LabelControl lblColor;
        private CheckEdit chckedtDiagonal;
        private FontEdit fntedtFontName;
        private SimpleButton buttonSetTextWatermark;
    }
}
