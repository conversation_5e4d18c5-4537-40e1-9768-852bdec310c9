﻿using System;
using System.IO;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Windows.Forms;
using DevExpress.Data.Utils.Security;
using DevExpress.DXperience.Demos;
using DevExpress.Office.DigitalSignatures;
using DevExpress.Office.Tsp;
using DevExpress.Pdf.Native;
using DevExpress.XtraEditors;
using DevExpress.XtraSplashScreen;

namespace DevExpress.Docs.Demos {
    public abstract class SignatureTutorialControl : TutorialControlBase {
        protected string ShowSignAndSaveFileDialog() {
            using(SaveFileDialog dialog = new SaveFileDialog()) {
                dialog.Filter = CreateFilterString();
                dialog.RestoreDirectory = true;
                dialog.CheckFileExists = false;
                dialog.CheckPathExists = true;
                dialog.OverwritePrompt = true;
                dialog.DereferenceLinks = true;
                dialog.ValidateNames = true;
                dialog.AddExtension = false;
                dialog.FilterIndex = 1;

                string fileName = GetFileNameForSaving();
                string directoryName = GetDirectoryName(fileName);
                fileName = Path.GetFileNameWithoutExtension(fileName);
                dialog.InitialDirectory = directoryName;
                dialog.FileName = fileName;
                if(dialog.ShowDialog() == DialogResult.OK)
                    return dialog.FileName;
                return string.Empty;
            }
        }
        protected abstract string GetFileNameForSaving();
        protected abstract string CreateFilterString();
        protected internal virtual string GetDirectoryName(string fileName) {
            if(string.IsNullOrEmpty(fileName))
                return string.Empty;
            string directoryName = Path.GetDirectoryName(fileName);
            if(string.IsNullOrEmpty(directoryName))
                return string.Empty;
            return Path.GetFullPath(Path.GetDirectoryName(fileName));
        }
        protected void SignAndOpen(string saveFileName, string fileName, SignatureOptions options, SignatureInfo signatureInfo) {
            IOverlaySplashScreenHandle overlay = SplashScreenManager.ShowOverlayForm(this);
            try {
                DocumentSigner documentSigner = new DocumentSigner();
                //documentSigner.Sign(DemoUtils.GetRelativePath(fileName), saveFileName, options, signatureInfo);
                documentSigner.Sign(fileName, saveFileName, options, signatureInfo);
            }
            finally {
                SplashScreenManager.CloseOverlayForm(overlay);
            }
            if(XtraMessageBox.Show("Do you want to open this file?\r\n" + saveFileName, "Signature", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                Data.Utils.SafeProcess.Open(saveFileName);
        }
        protected static SignatureOptions CreateSignatureOptions(CertificateItem cItem, string tsaUri, HashAlgorithmType digestMethod) {
            X509Certificate2 certificate = CertificateHelper.LoadCertificate(cItem.FilePath, cItem.Password);
            SignatureOptions options = new SignatureOptions();
            options.Certificate = certificate;
            if(!string.IsNullOrEmpty(tsaUri))
                options.TsaClient = new TsaClient(new Uri(tsaUri, UriKind.Absolute), HashAlgorithmType.SHA256);
            X509ChainPolicy policy = new X509ChainPolicy();
            policy.RevocationMode = X509RevocationMode.NoCheck;
            policy.RevocationFlag = X509RevocationFlag.ExcludeRoot;
            policy.VerificationFlags |=
                X509VerificationFlags.AllowUnknownCertificateAuthority |
                X509VerificationFlags.IgnoreCertificateAuthorityRevocationUnknown;
            options.CertificatePolicy = policy;
            options.TimestampCertificatePolicy = policy;
            options.CertificateKeyUsageFlags = X509KeyUsageFlags.None;
            options.DigestMethod = digestMethod;
            return options;
        }
        protected void RegisterCertificate(ListBoxControl certificatesListBox) {
            using(OpenFileDialog openDialog = new OpenFileDialog()) {
                openDialog.Filter = "X.509 Certificate (*.cer; *.crt, *.pfx)|*.cer;*.crt;*.pfx";
                openDialog.RestoreDirectory = true;
                if(openDialog.ShowDialog() == DialogResult.OK) {
                    CertificateItem certificate = CertificateItem.Create(openDialog.FileName, null);
                    if(certificate != null) {
                        certificatesListBox.Items.Add(certificate);
                        certificatesListBox.SelectedIndex = certificatesListBox.Items.Count - 1;
                    }
                }
            }
        }
        protected void PopulateHashAlgorithms(ComboBoxEdit hashAlgorithmComboBox) {
            var comboBoxItems = hashAlgorithmComboBox.Properties.Items;
            foreach(object item in Enum.GetValues(typeof(HashAlgorithmType)))
                comboBoxItems.Add(item);
            hashAlgorithmComboBox.SelectedItem = HashAlgorithmType.SHA256;
        }
        protected void PopulateCertificates(SidePanel panelOptions, ListBoxControl certificatesListBox) {
            if(!panelOptions.Enabled)
                return;
            try {
                certificatesListBox.Items.Add(CertificateItem.Create(DemoUtils.GetRelativePath("SignDemo.pfx"), "dxdemo"));
                certificatesListBox.SelectedIndex = 0;
                certificatesListBox.DisplayMember = "Subject";
            }
            catch(CryptographicException) {
                panelOptions.Enabled = false;
            }
        }
        protected void PopulateCommitmentTypes(ImageComboBoxEdit icbCommitmentType) {
            AppendCommitmentType(icbCommitmentType, CommitmentType.ProofOfApproval);
            AppendCommitmentType(icbCommitmentType, CommitmentType.ProofOfCreation);
            AppendCommitmentType(icbCommitmentType, CommitmentType.ProofOfOrigin);
            icbCommitmentType.SelectedIndex = 0;
        }
        void AppendCommitmentType(ImageComboBoxEdit icbCommitmentType, CommitmentType commitmentType) {
            icbCommitmentType.Properties.Items.Add(new XtraEditors.Controls.ImageComboBoxItem() {
                Description = commitmentType.Description,
                Value = commitmentType
            });
        }
    }
    //
    public sealed class CertificateItem {
        public static CertificateItem Create(string filePath, string password) {
            try {
                X509Certificate2 newCert = CertificateHelper.LoadCertificate(filePath, password, X509KeyStorageFlags.Exportable);
                if(newCert.HasPrivateKey)
                    return new CertificateItem(filePath, password, newCert.Subject);
                XtraMessageBox.Show("The certificate must contain a private key.", "Invalid certificate", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch {
                try {
                    using(PasswordForm passwordForm = new PasswordForm(filePath)) {
                        if(passwordForm.ShowDialog() == DialogResult.OK) {
                            password = passwordForm.Password;
                            X509Certificate2 newCert = CertificateHelper.LoadCertificate(filePath, password, X509KeyStorageFlags.Exportable);
                            return new CertificateItem(filePath, password, newCert.Subject);
                        }
                    }
                }
                catch {
                    XtraMessageBox.Show("The password for the certificate is incorrect.", "Incorrect password", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            return null;
        }
        readonly SensitiveData passwordData = SensitiveData.CreateForCurrentUser();
        CertificateItem(string filePath, string password, string name) {
            FilePath = filePath;
            passwordData.Text = password;
            Name = name;
        }
        public string FilePath {
            get; private set;
        }
        public string Password {
            get { return passwordData.Text; }
        }
        public string Name {
            get; private set;
        }
        public override string ToString() {
            return Name;
        }
    }
}
