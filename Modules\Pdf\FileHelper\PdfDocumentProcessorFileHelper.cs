﻿using System.IO;
using DevExpress.Pdf;

namespace DevExpress.Docs.Demos {
    public class PdfDocumentProcessorFileHelper : PdfFileHelper {
        readonly PdfDocumentProcessor documentProcessor;

        protected override string Creator {
            get { return documentProcessor.Document.Creator; }
            set { documentProcessor.Document.Creator = value; }
        }
        protected override string Producer {
            get { return documentProcessor.Document.Producer; }
            set { documentProcessor.Document.Producer = value; }
        }

        public PdfDocumentProcessorFileHelper(PdfDocumentProcessor processor) {
            documentProcessor = processor;
        }
        public override void LoadDocument(string path, bool detach) {
            documentProcessor.LoadDocument(path, detach);
        }
        public override void LoadDocument(Stream stream) {
            documentProcessor.LoadDocument(stream, true);
        }
        protected override void SaveDocument(string filePath, PdfSaveOptions options) {
            documentProcessor.SaveDocument(filePath, options);
        }
    }
}
