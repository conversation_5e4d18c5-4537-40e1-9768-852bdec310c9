﻿using System;
using System.ComponentModel;
using DevExpress.Pdf;

namespace DevExpress.Docs.Demos {
    public class ComboBoxFormFieldData : ArrayBasedFormFieldData {
        class ComboBoxTypeConverter : TypeConverter {
            public override TypeConverter.StandardValuesCollection GetStandardValues(ITypeDescriptorContext context) {
                ComboBoxFormFieldData formFieldData = context.Instance as ComboBoxFormFieldData;
                if(formFieldData != null)
                    return new StandardValuesCollection(formFieldData.Items);
                return base.GetStandardValues(context);
            }
            public override bool GetStandardValuesSupported(ITypeDescriptorContext context) {
                return true;
            }
        }

        static bool Contains(string[] array, string item) {
            for(int i = 0; i < array.Length; i++)
                if(array[i] == item)
                    return true;
            return false;
        }

        string fSelectedItem = string.Empty;
        bool fEditable;

        [Category(AppearanceCategory), TypeConverter(typeof(ComboBoxTypeConverter))]
        public string SelectedItem {
            get { return fSelectedItem; }
            set {
                CheckItem(Editable, value);
                fSelectedItem = value;
                UpdateModel();
            }
        }

        [Category(BehaviorCategory)]
        public bool Editable {
            get { return fEditable; }
            set {
                CheckItem(value, fSelectedItem);
                fEditable = value;
                UpdateModel();
            }
        }

        public ComboBoxFormFieldData(PdfDocumentPosition position, DocumentFormController controller)
            : base(position, controller) {
        }
        void CheckItem(bool editable, string selectedItem) {
            if(!editable && !String.IsNullOrEmpty(selectedItem) && (Items == null || !Contains(Items, selectedItem)))
                throw new ArgumentException("Selected item should be in item list if combo box is marked as non editable.");
        }
        protected override PdfAcroFormChoiceField CreateChoiceField() {
            return new PdfAcroFormComboBoxField(Name, PageNumber, Rectangle.InnerRectangle) { Editable = Editable };
        }
        protected override void SelectItems(PdfAcroFormChoiceField choiceField) {
            choiceField.SelectValue(SelectedItem);
        }
        protected override void ClearSelected() {
            if(!Editable)
                SelectedItem = null;
        }
    }
}
