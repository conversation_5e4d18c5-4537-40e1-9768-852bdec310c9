﻿using System;
using System.Windows.Forms;

namespace DevExpress.Docs.Demos {
    static class Program {
        [STAThread]
        static void Main() {
            if(!System.Windows.Forms.SystemInformation.TerminalServerSession && Screen.AllScreens.Length > 1)
                DevExpress.XtraEditors.WindowsFormsSettings.SetPerMonitorDpiAware();
            else
                DevExpress.XtraEditors.WindowsFormsSettings.SetDPIAware();

            DevExpress.XtraEditors.WindowsFormsSettings.UseUIAutomation = Utils.DefaultBoolean.True;
            DevExpress.UserSkins.BonusSkins.Register();
            try
            {
                Application.Run(new frmMain());
            }
            catch(Exception e) { MessageBox.Show(e.Message); };
        }

    }
}
