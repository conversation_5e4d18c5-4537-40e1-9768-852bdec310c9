﻿using System.Linq;
using DevExpress.DemoData.Model;
using DevExpress.DXperience.Demos;
using DevExpress.Utils;
using DevExpress.Utils.About;

namespace DevExpress.Docs.Demos {
    public partial class frmMain : DevExpress.DXperience.Demos.RibbonMainForm {
        #region Properties
        protected override string ProductName {
            get { return "DocumentServerForWin"; }
        }
        protected override string DemoName {
            get { return DemoHelper.GetFormText("Office File API Demo"); }
        }
        protected override bool ShowPanelDescription {
            get { return ModulesInfo.Instance.CurrentModuleBase.Description != ""; }
        }
        #endregion
        protected override Product GetProduct() {
            return Repository.DocsPlatform.Products.First(p => p.Name == ProductName);
        }
        protected override void ShowAbout() {
            AboutHelper.Show(ProductKind.Docs);
        }
        protected override void SetFormParam() {
            Icon = ResourceImageHelper.CreateIconFromResourcesEx("DevExpress.Docs.Demos.MainDemo.ico", typeof(frmMain).Assembly);
        }
        protected override RibbonMenuManager CreateRibbonMenuManager() {
            return new DocsRibbonMenuManager(this);
        }
        protected override void ShowModule(string name, XtraEditors.GroupControl groupControl, LookAndFeel.DefaultLookAndFeel lookAndFeel) {
            DemosInfo.DoShowModule(name, groupControl, RibbonMenuManager as DocsRibbonMenuManager);
        }
        protected override bool SupportAdvancedTitlePainting { get { return false; } }
    }

    public class DocsRibbonMenuManager : RibbonMenuManager {
        public DocsRibbonMenuManager(RibbonMainForm form)
            : base(form) {
        }
    }
}
