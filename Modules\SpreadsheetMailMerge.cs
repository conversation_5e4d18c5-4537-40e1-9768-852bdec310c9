﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Globalization;
using System.IO;
using System.Windows.Forms;
using DevExpress.DXperience.Demos;
using DevExpress.Spreadsheet;
using DevExpress.XtraEditors;

namespace DevExpress.Docs.Demos {
    public partial class SpreadsheetMailMerge : TutorialControlBase {
        const string saveFileDialogFilter =
            "Excel Workbook (*.xlsx)|*.xlsx|" +
            "Excel Macro-Enabled Workbook (*.xlsm)|*.xlsm|" +
            "Excel Binary Workbook (*.xlsb)|*.xlsb|" +
            "Excel 97-2003 Workbook (*.xls)|*.xls|" +
            "XML Spreadsheet 2003 (*.xml)|*.xml|" +
            "CSV (Comma delimited) (*.csv)|*.csv|" +
            "PDF (*.pdf)|*.pdf";
        readonly Workbook workbook;
        public SpreadsheetMailMerge() {
            InitializeComponent();
            ConfigureConnectionString();
            workbook = new Workbook();
            workbook.LoadDocument(DemoUtils.GetRelativePath("SpreadsheetMailMerge_template.xlsx"));
            spreadsheetPreview1.CanShowBorders = true;
            RefreshPreview();
        }
        void ConfigureConnectionString() {
            string path = DemoUtils.GetRelativePath("nwind.mdb");
            Configuration config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
            ConnectionStringsSection csSection = config.ConnectionStrings;
            ConnectionStringSettings newSettings = new ConnectionStringSettings("nwindConnection", @"Provider=Microsoft.Jet.OLEDB.4.0;Data Source=" + path, "System.Data.OleDb");
            csSection.ConnectionStrings.Add(newSettings);
            config.Save(ConfigurationSaveMode.Modified);
            ConfigurationManager.RefreshSection("connectionStrings");
        }
        void RefreshPreview() {
            spreadsheetPreview1.Workbook = CreateWorkbook();
            spreadsheetPreview1.UpdatePreview();
        }
        IWorkbook CreateWorkbook() {
            workbook.MailMergeParameters["Order"].Value = Convert.ToInt32(edOrderId.SelectedItem.ToString());
            IList<IWorkbook> mailMergeWorkbooks = workbook.GenerateMailMergeDocuments();
            IWorkbook mailMergeWorkbook = mailMergeWorkbooks[0];
            mailMergeWorkbook.Options.Culture = new CultureInfo("en-US");
            Worksheet sheet = mailMergeWorkbook.Worksheets[0];
            sheet.SetPrintRange(sheet.GetDataRange());
            sheet.PrintOptions.FitToPage = true;
            sheet.PrintOptions.FitToWidth = 1;
            sheet.PrintOptions.FitToHeight = 0; // automatic
            return mailMergeWorkbook;
        }
        void OrderId_SelectedValueChanged(object sender, EventArgs e) {
            RefreshPreview();
        }
        void SaveAs_Click(object sender, EventArgs e) {
            string fileName = DemoUtils.GetSaveFileName(saveFileDialogFilter, "Mail_Merge");
            if(!string.IsNullOrEmpty(fileName)) {
                try {
                    IWorkbook result = CreateWorkbook();
                    string ext = Path.GetExtension(fileName);
                    if(ext == ".pdf")
                        result.ExportToPdf(fileName);
                    else
                        result.SaveDocument(fileName);
                    DemoUtils.ShowFile(fileName, this);
                }
                catch(Exception ex) {
                    XtraMessageBox.Show(ex.Message, Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
    }
}
