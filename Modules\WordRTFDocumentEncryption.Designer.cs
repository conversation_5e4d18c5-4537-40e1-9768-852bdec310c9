﻿namespace DevExpress.Docs.Demos {
    partial class WordRTFDocumentEncryption {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent() {
            this.printPreviewControl = new DevExpress.XtraPrinting.Control.PrintControl();
            this.sidePanel1 = new DevExpress.XtraEditors.SidePanel();
            this.btnSave = new DevExpress.XtraEditors.SimpleButton();
            this.lblPassword = new DevExpress.XtraEditors.LabelControl();
            this.lblEncryptionType = new DevExpress.XtraEditors.LabelControl();
            this.edPasswordToOpen = new DevExpress.XtraEditors.TextEdit();
            this.edEncryptionType = new DevExpress.XtraEditors.ComboBoxEdit();
            this.sidePanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edPasswordToOpen.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edEncryptionType.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // printPreviewControl
            // 
            this.printPreviewControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.printPreviewControl.IsMetric = false;
            this.printPreviewControl.Location = new System.Drawing.Point(0, 53);
            this.printPreviewControl.Margin = new System.Windows.Forms.Padding(0);
            this.printPreviewControl.Name = "printPreviewControl";
            this.printPreviewControl.Size = new System.Drawing.Size(784, 379);
            this.printPreviewControl.TabIndex = 34;
            // 
            // sidePanel1
            // 
            this.sidePanel1.AllowResize = false;
            this.sidePanel1.Controls.Add(this.btnSave);
            this.sidePanel1.Controls.Add(this.lblPassword);
            this.sidePanel1.Controls.Add(this.lblEncryptionType);
            this.sidePanel1.Controls.Add(this.edPasswordToOpen);
            this.sidePanel1.Controls.Add(this.edEncryptionType);
            this.sidePanel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.sidePanel1.Location = new System.Drawing.Point(0, 0);
            this.sidePanel1.Name = "sidePanel1";
            this.sidePanel1.Size = new System.Drawing.Size(784, 53);
            this.sidePanel1.TabIndex = 53;
            this.sidePanel1.Text = "sidePanel1";
            // 
            // btnSave
            // 
            this.btnSave.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSave.Location = new System.Drawing.Point(668, 14);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(98, 23);
            this.btnSave.TabIndex = 61;
            this.btnSave.Text = "Save As...";
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // lblPassword
            // 
            this.lblPassword.Location = new System.Drawing.Point(18, 19);
            this.lblPassword.Name = "lblPassword";
            this.lblPassword.Size = new System.Drawing.Size(50, 13);
            this.lblPassword.TabIndex = 57;
            this.lblPassword.Text = "Password:";
            // 
            // lblEncryptionType
            // 
            this.lblEncryptionType.Location = new System.Drawing.Point(201, 19);
            this.lblEncryptionType.Name = "lblEncryptionType";
            this.lblEncryptionType.Size = new System.Drawing.Size(82, 13);
            this.lblEncryptionType.TabIndex = 58;
            this.lblEncryptionType.Text = "Encryption Type:";
            // 
            // edPasswordToOpen
            // 
            this.edPasswordToOpen.Location = new System.Drawing.Point(87, 16);
            this.edPasswordToOpen.Name = "edPasswordToOpen";
            this.edPasswordToOpen.Properties.MaxLength = 255;
            this.edPasswordToOpen.Size = new System.Drawing.Size(93, 20);
            this.edPasswordToOpen.TabIndex = 59;
            // 
            // edEncryptionType
            // 
            this.edEncryptionType.Location = new System.Drawing.Point(289, 16);
            this.edEncryptionType.Name = "edEncryptionType";
            this.edEncryptionType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.edEncryptionType.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.edEncryptionType.Size = new System.Drawing.Size(120, 20);
            this.edEncryptionType.TabIndex = 60;
            // 
            // WordRTFDocumentEncryption
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.printPreviewControl);
            this.Controls.Add(this.sidePanel1);
            this.Margin = new System.Windows.Forms.Padding(0);
            this.Name = "WordRTFDocumentEncryption";
            this.sidePanel1.ResumeLayout(false);
            this.sidePanel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edPasswordToOpen.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edEncryptionType.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private XtraPrinting.Control.PrintControl printPreviewControl;
        private XtraEditors.SidePanel sidePanel1;
        private XtraEditors.SimpleButton btnSave;
        private XtraEditors.LabelControl lblPassword;
        private XtraEditors.LabelControl lblEncryptionType;
        private XtraEditors.TextEdit edPasswordToOpen;
        private XtraEditors.ComboBoxEdit edEncryptionType;
    }
}
