﻿using System;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using DevExpress.Pdf;
using DevExpress.XtraBars.Navigation;
using DevExpress.XtraEditors;
using DevExpress.XtraPdfViewer;

namespace DevExpress.Docs.Demos {
    public partial class PdfRubberStampAnnotations : PdfTutorialControl {
        static readonly string editingTempFilePath = DemoUtils.GetTempFileName();
        //
        PdfAnnotationFacade selectedItem;
        string creatingAnnotationType;
        bool CursorIsOverPage {
            get { return pdfViewer.GetDocumentPosition(pdfViewer.PointToClient(MousePosition), false) != null; }
        }
        public PdfRubberStampAnnotations() {
            InitializeComponent();
            try {
                File.Copy(DemoUtils.GetRelativePath("PageContent.pdf"), editingTempFilePath, true);
                pdfViewer.LoadDocument(editingTempFilePath);
                pdfViewer.GetDocumentFacade().Pages[0].AddRubberStampAnnotation(new PdfPoint(400, 40), PdfRubberStampAnnotationIconName.Approved);
                pdfViewer.GetDocumentFacade().Pages[0].AddRubberStampAnnotation(new PdfRectangle(340, 740, 440, 780), PdfRubberStampAnnotationIconName.DReviewed);
                pdfViewer.GetDocumentFacade().Pages[1].AddRubberStampAnnotation(new PdfRectangle(420, 740, 520, 780), PdfRubberStampAnnotationIconName.DReviewed);
                pdfViewer.GetDocumentFacade().Pages[2].AddRubberStampAnnotation(new PdfRectangle(420, 740, 520, 780), PdfRubberStampAnnotationIconName.DReviewed);
                pdfViewer.GetDocumentFacade().Pages[2].AddRubberStampAnnotation(new PdfRectangle(350, 130, 450, 190), PdfRubberStampAnnotationIconName.SHSignHere);
                accordionControl.MouseDown += AccordionControlMouseDown;
                accordionControl.KeyDown += ProcessCommonKeys;
                accordionControl.DragEnter += AccordionControlDragEnter;
                accordionControl.QueryContinueDrag += AccordionControlQueryContinueDrag;
                pdfViewer.KeyDown += ProcessCommonKeys;
                pdfViewer.KeyDown += PdfViewerKeyDown;
                pdfViewer.MouseMove += PdfViewerMouseMove;
                pdfViewer.MouseUp += PdfViewerMouseUp;
                pdfViewer.DragEnter += PdfViewerDragEnter;
                pdfViewer.DragOver += PdfViewerDragOver;
                pdfViewer.DragDrop += PdfViewerDragDrop;
                pdfViewer.AnnotationGotFocus += PdfViewer_AnnotationGotFocus;
                pdfViewer.AnnotationLostFocus += PdfViewer_AnnotationLostFocus;
                pdfViewer.AnnotationChanged += PdfViewer_AnnotationChanged;
                PdfViewerHelpers.EnableCustomToolMouseNavigation(pdfViewer);
                propertyGridControl.KeyDown += ProcessCommonKeys;
            }
            catch { }
        }
        void PdfViewerMouseMove(object sender, MouseEventArgs e) {
            if(creatingAnnotationType != null && CursorIsOverPage) {
                pdfViewer.CursorMode = PdfCursorMode.Custom;
                pdfViewer.Cursor = Cursors.Cross;
            }
            else {
                pdfViewer.CursorMode = PdfCursorMode.SelectTool;
            }
        }
        void PdfViewerDragDrop(object sender, DragEventArgs e) {
            CreateContent(e.Data.GetData(DataFormats.Text) as string, pdfViewer.PointToClient(new Point(e.X, e.Y)));
        }
        void PdfViewerDragOver(object sender, DragEventArgs e) {
            e.Effect = pdfViewer.GetDocumentPosition(pdfViewer.PointToClient(new Point(e.X, e.Y)), false) == null ? DragDropEffects.None : DragDropEffects.All;
        }
        void PdfViewerDragEnter(object sender, DragEventArgs e) {
            PdfViewerDragOver(sender, e);
        }
        void PdfViewer_AnnotationChanged(object sender, PdfAnnotationChangedEventArgs e) {
            propertyGridControl.UpdateData();
        }
        void PdfViewer_AnnotationLostFocus(object sender, PdfAnnotationLostFocusEventArgs e) {
            SelectContent(null);
        }
        void PdfViewer_AnnotationGotFocus(object sender, PdfAnnotationGotFocusEventArgs e) {
            SelectContent(pdfViewer.GetDocumentFacade().Pages[e.Annotation.PageNumber - 1].Annotations.FirstOrDefault(a => a.Name == e.Annotation.Name));
        }
        void SelectContent(PdfAnnotationFacade item) {
            if(selectedItem != item) {
                selectedItem = item;
                propertyGridControl.SelectedObject = null;
                PdfRubberStampAnnotationFacade facade = item as PdfRubberStampAnnotationFacade;
                if(facade != null)
                    propertyGridControl.SelectedObject = new PdfRubberStampAnnotationFacadeWrapper(facade);
                pdfViewer.SelectAnnotation(1, "");
                pdfViewer.Focus();
            }
        }
        void CreateContent(string iconName, Point location) {
            PdfDocumentPosition position = pdfViewer.GetDocumentPosition(location, false);
            PdfPageFacade page = pdfViewer.GetDocumentFacade().Pages[position.PageNumber - 1];
            PdfRubberStampAnnotationFacade annotation = page.AddRubberStampAnnotation(position.Point, iconName);
            pdfViewer.SelectAnnotation(position.PageNumber, annotation.Name);
            pdfViewer.Cursor = Cursors.Default;
        }
        void EndContentCreation() {
            creatingAnnotationType = null;
            pdfViewer.Cursor = Cursors.Default;
            accordionControl.SelectedElement = null;
        }
        void PdfViewerMouseUp(object sender, MouseEventArgs e) {
            if(e.Button == MouseButtons.Left) {
                if(CursorIsOverPage && !string.IsNullOrEmpty(creatingAnnotationType)) {
                    CreateContent(creatingAnnotationType, e.Location);
                    EndContentCreation();
                }
            }
        }
        void PdfViewerKeyDown(object sender, KeyEventArgs e) {
            switch(e.KeyCode) {
                case Keys.Escape:
                    EndContentCreation();
                    break;
                case Keys.Delete:
                    if(selectedItem != null)
                        SelectContent(null);
                    break;
                case Keys.PageDown:
                    pdfViewer.CurrentPageNumber = Math.Min(pdfViewer.CurrentPageNumber + 1, pdfViewer.PageCount);
                    break;
                case Keys.PageUp:
                    pdfViewer.CurrentPageNumber = Math.Max(pdfViewer.CurrentPageNumber - 1, 1);
                    break;
            }
        }
        void AccordionControlMouseDown(object sender, MouseEventArgs e) {
            AccordionControlHitInfo hitInfo = accordionControl.CalcHitInfo(e.Location);
            AccordionElementBaseViewInfo info = hitInfo.ItemInfo;
            if(hitInfo.HitTest.HasFlag(AccordionControlHitTest.Item) && info != null && info.Element != null) {
                creatingAnnotationType = null;
                if(info.Element == accordionControlDraftElement) creatingAnnotationType = PdfRubberStampAnnotationIconName.Draft;
                if(info.Element == accordionControlAsIsElement) creatingAnnotationType = PdfRubberStampAnnotationIconName.AsIs;
                if(info.Element == accordionControlApprovedElement) creatingAnnotationType = PdfRubberStampAnnotationIconName.Approved;
                if(info.Element == accordionControlDConfidentialElement) creatingAnnotationType = PdfRubberStampAnnotationIconName.DConfidential;
                if(info.Element == accordionControlNotApprovedElement) creatingAnnotationType = PdfRubberStampAnnotationIconName.NotApproved;
                if(info.Element == accordionControlInitialHereElement) creatingAnnotationType = PdfRubberStampAnnotationIconName.SHInitialHere;
                if(info.Element == accordionControlSignHereElement) creatingAnnotationType = PdfRubberStampAnnotationIconName.SHSignHere;
                if(info.Element == accordionControlTopSecretElement) creatingAnnotationType = PdfRubberStampAnnotationIconName.TopSecret;
                if(info.Element == accordionControlDApprovedElement) creatingAnnotationType = PdfRubberStampAnnotationIconName.DApproved;
                if(info.Element == accordionControlDRevisedElement) creatingAnnotationType = PdfRubberStampAnnotationIconName.DRevised;
                if(info.Element == accordionControlDReviewedElement) creatingAnnotationType = PdfRubberStampAnnotationIconName.DReviewed;
                if(info.Element == accordionControlDReceivedElement) creatingAnnotationType = PdfRubberStampAnnotationIconName.DReceived;
                if(info.Element == accordionControlSignHereElements) creatingAnnotationType = PdfRubberStampAnnotationIconName.SHSignHere;
                if(info.Element == accordionControlWitnessHereElement) creatingAnnotationType = PdfRubberStampAnnotationIconName.SHWitness;
                if(info.Element == accordionControlSHAcceptedElement) creatingAnnotationType = PdfRubberStampAnnotationIconName.SHAccepted;
                if(info.Element == accordionControlSHRejectedElement) creatingAnnotationType = PdfRubberStampAnnotationIconName.SHRejected;
                if(info.Element == accordionControlExperimentalElement) creatingAnnotationType = PdfRubberStampAnnotationIconName.Experimental;
                if(info.Element == accordionControlExpiredElement) creatingAnnotationType = PdfRubberStampAnnotationIconName.Expired;
                if(info.Element == accordionControlFinalElement) creatingAnnotationType = PdfRubberStampAnnotationIconName.Final;
                if(info.Element == accordionControlSoldElement) creatingAnnotationType = PdfRubberStampAnnotationIconName.Sold;
                if(info.Element == accordionControlDepartmentalElement) creatingAnnotationType = PdfRubberStampAnnotationIconName.Departmental;
                if(info.Element == accordionControlForCommentElement) creatingAnnotationType = PdfRubberStampAnnotationIconName.ForComment;
                if(info.Element == accordionControlNotForPublicReleaseElement) creatingAnnotationType = PdfRubberStampAnnotationIconName.NotForPublicRelease;
                if(info.Element == accordionControlForPublicReleaseElement) creatingAnnotationType = PdfRubberStampAnnotationIconName.ForPublicRelease;
                if(String.IsNullOrEmpty(creatingAnnotationType))
                    EndContentCreation();
                else
                    accordionControl.DoDragDrop(creatingAnnotationType, DragDropEffects.All);
            }
        }
        void AccordionControlDragEnter(object sender, DragEventArgs e) {
            e.Effect = DragDropEffects.None;
        }
        void AccordionControlQueryContinueDrag(object sender, QueryContinueDragEventArgs e) {
            if(e.Action == DragAction.Drop) {
                if(!CursorIsOverPage)
                    e.Action = DragAction.Cancel;
                else EndContentCreation();
            }
        }
        void AccordionControlOpenElementClick(object sender, EventArgs e) {
            using(PdfDocumentProcessor processor = new PdfDocumentProcessor()) {
                using(PdfDocumentProcessorAndViewerFileHelper fileHelper = new PdfDocumentProcessorAndViewerFileHelper(processor, pdfViewer)) {
                    if(fileHelper.LoadDocumentWithDialog()) {
                        try {
                            string tempSourceFilePath = DemoUtils.GetTempFileName();
                            processor.SaveDocument(tempSourceFilePath);
                            SelectContent(null);
                            pdfViewer.ZoomMode = PdfZoomMode.PageLevel;
                        }
                        catch {
                            XtraMessageBox.Show("An error occurred while processing the PDF document", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }
                    }
                }
            }
        }
        void AccordionControlSaveElementClick(object sender, EventArgs e) {
            new PdfViewerFileHelper(pdfViewer).SaveDocument();
        }
        void ProcessCommonKeys(object sender, KeyEventArgs e) {
            if(e.Control) {
                switch(e.KeyCode) {
                    case Keys.S:
                        AccordionControlSaveElementClick(this, EventArgs.Empty);
                        break;
                    case Keys.O:
                        AccordionControlOpenElementClick(this, EventArgs.Empty);
                        break;
                }
            }
        }
        void UnsubscribeFromEvents() {
            if(pdfViewer != null) {
                pdfViewer.KeyDown -= PdfViewerKeyDown;
                pdfViewer.KeyDown -= ProcessCommonKeys;
                pdfViewer.MouseUp -= PdfViewerMouseUp;
            }
            if(accordionControl != null) {
                accordionControl.MouseDown -= AccordionControlMouseDown;
                accordionControl.KeyDown -= ProcessCommonKeys;
                accordionControl.DragEnter -= AccordionControlDragEnter;
                accordionControl.QueryContinueDrag -= AccordionControlQueryContinueDrag;
            }
            if(propertyGridControl != null)
                propertyGridControl.KeyDown -= ProcessCommonKeys;
        }
        protected override bool ProcessCmdKey(ref Message msg, Keys keyData) {
            if(keyData == Keys.Escape)
                EndContentCreation();
            return base.ProcessCmdKey(ref msg, keyData);
        }
        void accordionControlNewElement_Click(object sender, EventArgs e) {
            pdfViewer.CloseDocument();
            using(PdfDocumentProcessor processor = new PdfDocumentProcessor()) {
                processor.CreateEmptyDocument();
                processor.AddNewPage(PdfPaperSize.A4);
                processor.SaveDocument(editingTempFilePath);
            }
            pdfViewer.LoadDocument(editingTempFilePath);
            SelectContent(null);
            pdfViewer.ZoomMode = PdfZoomMode.PageLevel;
        }
    }
}
