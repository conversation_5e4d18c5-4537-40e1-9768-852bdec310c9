﻿<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003" DefaultTargets="Build" ToolsVersion="16.0">
  <PropertyGroup>
    <ProjectGuid>{8FC73700-23FB-4E8D-B117-F8DBD4FD4139}</ProjectGuid>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ApplicationIcon>MainDemo.ico</ApplicationIcon>
    <AssemblyName>OfficeFileAPIDemos</AssemblyName>
    <RootNamespace>DevExpress.Docs.Demos</RootNamespace>
    <OutputType>WinExe</OutputType>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <BaseIntermediateOutputPath>obj_netFW</BaseIntermediateOutputPath>
    <DoNotCopyLocalIfInGac>true</DoNotCopyLocalIfInGac>
    <ConvertToNet8Windows />
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>true</Prefer32Bit>
    <DXDisableCentral>true</DXDisableCentral>
    <ManagePackageVersionsCentrally>false</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <BaseAddress>285212672</BaseAddress>
    <CheckForOverflowUnderflow>false</CheckForOverflowUnderflow>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugSymbols>true</DebugSymbols>
    <FileAlignment>4096</FileAlignment>
    <Optimize>false</Optimize>
    <RegisterForComInterop>false</RegisterForComInterop>
    <RemoveIntegerChecks>false</RemoveIntegerChecks>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningLevel>4</WarningLevel>
    <DebugType>full</DebugType>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <BaseAddress>285212672</BaseAddress>
    <CheckForOverflowUnderflow>false</CheckForOverflowUnderflow>
    <DefineConstants>TRACE</DefineConstants>
    <DebugSymbols>false</DebugSymbols>
    <FileAlignment>4096</FileAlignment>
    <Optimize>true</Optimize>
    <RegisterForComInterop>false</RegisterForComInterop>
    <RemoveIntegerChecks>false</RemoveIntegerChecks>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningLevel>4</WarningLevel>
    <DebugType>none</DebugType>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.DevAV.v25.1.Data">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.CodeParser.v25.1">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.BonusSkins.v25.1">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.Data.Desktop.v25.1">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.Data.v25.1">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.Drawing.v25.1">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.DemoData.v25.1">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.Pdf.v25.1.Core">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.Pdf.v25.1.Drawing">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.Printing.v25.1.Core">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.RichEdit.v25.1.Core">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.XtraLayout.v25.1">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.XtraSpreadsheet.v25.1">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.Spreadsheet.v25.1.Core">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.Docs.v25.1">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.XtraGrid.v25.1">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.XtraPdfViewer.v25.1">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.XtraPrinting.v25.1">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.Tutorials.v25.1">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.Utils.v25.1">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.RichEdit.v25.1.Core">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.XtraRichEdit.v25.1">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.XtraBars.v25.1">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.XtraEditors.v25.1">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.Office.v25.1.Core">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.XtraTreeList.v25.1">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.XtraNavBar.v25.1">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.XtraVerticalGrid.v25.1">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.DataVisualization.v25.1.Core">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.TreeMap.v25.1.Core">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.XtraTreeMap.v25.1">
      <HintPath>
      </HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Build">
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <Name>System</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.configuration">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <Name>System.Data</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Design">
      <Name>System.Design</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <Name>System.Drawing</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Web">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Windows.Forms">
      <Name>System.Windows.Forms</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <Name>System.XML</Name>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AssemblyInfo.cs" />
    <Compile Include="Forms\PdfFindAndMarkupForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\PdfFindAndMarkupForm.Designer.cs">
      <DependentUpon>PdfFindAndMarkupForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\PdfProgressForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\PdfProgressForm.Designer.cs">
      <DependentUpon>PdfProgressForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\PdfTextSearchResultsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\PdfTextSearchResultsForm.Designer.cs">
      <DependentUpon>PdfTextSearchResultsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\PdfClearPageContent.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\PdfClearPageContent.Designer.cs">
      <DependentUpon>PdfClearPageContent.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\PdfADocumentConversion.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\PdfADocumentConversion.Designer.cs">
      <DependentUpon>PdfADocumentConversion.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\Pdf\FileHelper\PdfDocumentConverterFileHelper.cs" />
    <Compile Include="Modules\Pdf\PdfFileNameEditor.cs" />
    <Compile Include="Modules\Pdf\PdfRedactAnnotationProperties.cs" />
    <Compile Include="Modules\Pdf\PdfRubberStampAnnotationFacadeWrapper.cs" />
    <Compile Include="Modules\PdfRubberStampAnnotations.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\PdfRubberStampAnnotations.Designer.cs">
      <DependentUpon>PdfRubberStampAnnotations.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\PdfPageContentCreation.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\PdfPageContentCreation.Designer.cs">
      <DependentUpon>PdfPageContentCreation.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\Pdf\PageContent\PageImageContent.cs" />
    <Compile Include="Modules\Pdf\PageContent\PagePathContent.cs" />
    <Compile Include="Modules\Pdf\PageContent\PageShapeContent.cs" />
    <Compile Include="Modules\Pdf\PageContent\PageTextContent.cs" />
    <Compile Include="Modules\Pdf\PdfContentData.cs" />
    <Compile Include="Modules\Pdf\PageContent\PageContentData.cs" />
    <Compile Include="Modules\Pdf\PageContent\PageContentController.cs" />
    <Compile Include="Modules\Pdf\PdfPathDragItem.cs" />
    <Compile Include="Modules\Pdf\PdfViewerHelpers.cs" />
    <Compile Include="Modules\Pdf\TypeConverters\FontConverter.cs" />
    <Compile Include="Modules\Pdf\SignatureAppearanceBuilderForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Modules\Pdf\SignatureAppearanceBuilderForm.Designer.cs">
      <DependentUpon>SignatureAppearanceBuilderForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\Pdf\TypeConverters\PdfAcroFormButtonStyleConverter.cs" />
    <Compile Include="Modules\PdfFileAttachmentDemo.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\PdfFileAttachmentDemo.Designer.cs">
      <DependentUpon>PdfFileAttachmentDemo.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\PdfFormFieldEditing.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\PdfFormFieldEditing.Designer.cs">
      <DependentUpon>PdfFormFieldEditing.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\PdfFormCreation.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\PdfFormCreation.Designer.cs">
      <DependentUpon>PdfFormCreation.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\PdfFormFlattening.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\PdfFormFlattening.Designer.cs">
      <DependentUpon>PdfFormFlattening.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\PdfTextMarkupAnnotations.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\PdfTextMarkupAnnotations.Designer.cs">
      <DependentUpon>PdfTextMarkupAnnotations.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\Pdf\TypeConverters\PdfRubberStampIconNameTypeConverter.cs" />
    <Compile Include="Modules\Pdf\TypeConverters\PdfStringFormatConverter.cs" />
    <Compile Include="Modules\Pdf\UnboundRowPropertyDescriptor.cs" />
    <Compile Include="Modules\Pdf\ColorPicker\PdfRGBColorConverter.cs" />
    <Compile Include="Modules\Pdf\ColorPicker\PdfRGBColorPickEdit.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Modules\Pdf\ColorPicker\PdfRGBColorPickEditViewInfo.cs" />
    <Compile Include="Modules\Pdf\ColorPicker\RepositoryItemPdfRGBColorPickEdit.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Modules\Pdf\DocumentFormController.cs" />
    <Compile Include="Modules\Pdf\FileHelper\PdfDocumentProcessorAndViewerFileHelper.cs" />
    <Compile Include="Modules\Pdf\FileHelper\PdfDocumentProcessorFileHelper.cs" />
    <Compile Include="Modules\Pdf\FileHelper\PdfDocumentSignerFileHelper.cs" />
    <Compile Include="Modules\Pdf\FileHelper\PdfViewerFileHelper.cs" />
    <Compile Include="Modules\Pdf\FormFieldAppearance.cs" />
    <Compile Include="Modules\Pdf\FormFieldData\ArrayBasedFormFieldData.cs" />
    <Compile Include="Modules\Pdf\FormFieldData\CheckBoxFormFieldData.cs" />
    <Compile Include="Modules\Pdf\FormFieldData\ComboBoxFormFieldData.cs" />
    <Compile Include="Modules\Pdf\FormFieldData\FormFieldData.cs" />
    <Compile Include="Modules\Pdf\FormFieldData\ListBoxFormFieldData.cs" />
    <Compile Include="Modules\Pdf\FormFieldData\SignatureFormFieldData.cs" />
    <Compile Include="Modules\Pdf\FormFieldData\TextBoxFormFieldData.cs" />
    <Compile Include="Modules\Pdf\PdfPageDragItem.cs" />
    <Compile Include="Modules\Pdf\DragPoint.cs" />
    <Compile Include="Modules\Pdf\FormFieldFontEditor.cs" />
    <Compile Include="Modules\Pdf\PdfContentRectangle.cs" />
    <Compile Include="Modules\Pdf\TypeConverters\PdfButtonWidgetIconOptionsTypeConverter.cs" />
    <Compile Include="Modules\Pdf\TypeConverters\PdfRectangleTypeConverter.cs" />
    <Compile Include="Modules\Pdf\TypeConverters\PdfRGBColorTypeConverter.cs" />
    <Compile Include="Modules\SpreadsheetAccessiblePDF.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\SpreadsheetAccessiblePDF.Designer.cs">
      <DependentUpon>SpreadsheetAccessiblePDF.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\SpreadsheetDocumentEncryption.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\SpreadsheetDocumentEncryption.Designer.cs">
      <DependentUpon>SpreadsheetDocumentEncryption.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\PdfPasswordProtection.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\PdfPasswordProtection.Designer.cs">
      <DependentUpon>PdfPasswordProtection.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\PdfSignatureDemo.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\PdfSignatureDemo.Designer.cs">
      <DependentUpon>PdfSignatureDemo.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\PdfFormFilling.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\PdfFormFilling.Designer.cs">
      <DependentUpon>PdfFormFilling.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\PdfPageMerging.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\PdfPageMerging.Designer.cs">
      <DependentUpon>PdfPageMerging.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\SpreadsheetDocumentConversion.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\SpreadsheetDocumentConversion.Designer.cs">
      <DependentUpon>SpreadsheetDocumentConversion.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\SpreadsheetDocumentProtection.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\SpreadsheetDocumentProtection.Designer.cs">
      <DependentUpon>SpreadsheetDocumentProtection.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\SpreadsheetDocumentMerging.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\SpreadsheetDocumentMerging.Designer.cs">
      <DependentUpon>SpreadsheetDocumentMerging.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\Pdf\FileHelper\PdfFileHelper.cs" />
    <Compile Include="Forms\PdfTextExtractionResultsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\PdfTextExtractionResultsForm.Designer.cs">
      <DependentUpon>PdfTextExtractionResultsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\SpreadsheetDocumentSignatureDemo.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\SpreadsheetDocumentSignatureDemo.Designer.cs">
      <DependentUpon>SpreadsheetDocumentSignatureDemo.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\SpreadsheetPreview.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\SpreadsheetPreview.Designer.cs">
      <DependentUpon>SpreadsheetPreview.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosRegistration.cs" />
    <Compile Include="DemoUtils.cs" />
    <Compile Include="Forms\PasswordForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\PasswordForm.Designer.cs">
      <DependentUpon>PasswordForm.cs</DependentUpon>
    </Compile>
    <Compile Include="frmMain.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Modules\About.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\About.Designer.cs">
      <DependentUpon>About.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\WordRTFAcroFormPDF.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\WordRTFAcroFormPDF.Designer.cs">
      <DependentUpon>WordRTFAcroFormPDF.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\WordRTFAccessiblePDF.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\WordRTFAccessiblePDF.Designer.cs">
      <DependentUpon>WordRTFAccessiblePDF.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\WordRtfDocumentWatermark.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\WordRtfDocumentWatermark.Designer.cs">
      <DependentUpon>WordRtfDocumentWatermark.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\WordCompareDocuments.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\WordCompareDocuments.Designer.cs">
      <DependentUpon>WordCompareDocuments.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\WordRTFDocumentEncryption.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\WordRTFDocumentEncryption.Designer.cs">
      <DependentUpon>WordRTFDocumentEncryption.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\WordRTFDocumentProtection.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\WordRTFDocumentProtection.Designer.cs">
      <DependentUpon>WordRTFDocumentProtection.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\WordRTFFindAndReplace.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\WordRTFFindAndReplace.Designer.cs">
      <DependentUpon>WordRTFFindAndReplace.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\MailMerge\DocumentGenerator.cs" />
    <Compile Include="Modules\SpreadsheetDocumentTemplate.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\SpreadsheetDocumentTemplate.Designer.cs">
      <DependentUpon>SpreadsheetDocumentTemplate.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\SpreadsheetMailMerge.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\SpreadsheetMailMerge.Designer.cs">
      <DependentUpon>SpreadsheetMailMerge.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\WordRTFDocumentConversion.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\WordRTFDocumentConversion.Designer.cs">
      <DependentUpon>WordRTFDocumentConversion.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\WordRTFMailMerge.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\WordRTFMailMerge.Designer.cs">
      <DependentUpon>WordRTFMailMerge.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\Utils.cs" />
    <Compile Include="Modules\WordRTFSignatureDemo.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\WordRTFSignatureDemo.Designer.cs">
      <DependentUpon>WordRTFSignatureDemo.cs</DependentUpon>
    </Compile>
    <Compile Include="nwindDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>nwindDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Modules\Pdf\PdfTutorialControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\Pdf\PdfTutorialControl.Designer.cs">
      <DependentUpon>PdfTutorialControl.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\Resources.Designer.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="Modules\SignatureTutorialControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Forms\PasswordForm.resx">
      <DependentUpon>PasswordForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\PdfFindAndMarkupForm.resx">
      <DependentUpon>PdfFindAndMarkupForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\PdfProgressForm.resx">
      <DependentUpon>PdfProgressForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\PdfTextSearchResultsForm.resx">
      <DependentUpon>PdfTextSearchResultsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\PdfTextExtractionResultsForm.resx">
      <DependentUpon>PdfTextExtractionResultsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\PdfClearPageContent.resx">
      <DependentUpon>PdfClearPageContent.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\PdfADocumentConversion.resx">
      <DependentUpon>PdfADocumentConversion.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\PdfRubberStampAnnotations.resx">
      <DependentUpon>PdfRubberStampAnnotations.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\PdfPageContentCreation.resx">
      <DependentUpon>PdfPageContentCreation.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\PdfFormFieldEditing.resx">
      <DependentUpon>PdfFormFieldEditing.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\PdfFormCreation.resx">
      <DependentUpon>PdfFormCreation.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\PdfFormFlattening.resx">
      <DependentUpon>PdfFormFlattening.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\PdfPasswordProtection.resx">
      <DependentUpon>PdfPasswordProtection.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\PdfFileAttachmentDemo.resx">
      <DependentUpon>PdfFileAttachmentDemo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\PdfSignatureDemo.resx">
      <DependentUpon>PdfSignatureDemo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\PdfFormFilling.resx">
      <DependentUpon>PdfFormFilling.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\PdfPageMerging.resx">
      <DependentUpon>PdfPageMerging.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\PdfTextMarkupAnnotations.resx">
      <DependentUpon>PdfTextMarkupAnnotations.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\Pdf\SignatureAppearanceBuilderForm.resx">
      <DependentUpon>SignatureAppearanceBuilderForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\SpreadsheetAccessiblePDF.resx">
      <DependentUpon>SpreadsheetAccessiblePDF.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\SpreadsheetDocumentConversion.resx">
      <DependentUpon>SpreadsheetDocumentConversion.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\SpreadsheetDocumentProtection.resx">
      <DependentUpon>SpreadsheetDocumentProtection.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\SpreadsheetDocumentMerging.resx">
      <DependentUpon>SpreadsheetDocumentMerging.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\SpreadsheetDocumentSignatureDemo.resx">
      <DependentUpon>SpreadsheetDocumentSignatureDemo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\WordRTFAcroFormPDF.resx">
      <DependentUpon>WordRTFAcroFormPDF.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\WordRTFAccessiblePDF.resx">
      <DependentUpon>WordRTFAccessiblePDF.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\WordRtfDocumentWatermark.resx">
      <DependentUpon>WordRtfDocumentWatermark.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\WordCompareDocuments.resx">
      <DependentUpon>WordCompareDocuments.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\WordRTFDocumentEncryption.resx">
      <DependentUpon>WordRTFDocumentEncryption.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\WordRTFDocumentProtection.resx">
      <DependentUpon>WordRTFDocumentProtection.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\WordRTFFindAndReplace.resx">
      <DependentUpon>WordRTFFindAndReplace.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\SpreadsheetDocumentEncryption.resx">
      <DependentUpon>SpreadsheetDocumentEncryption.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\SpreadsheetPreview.resx">
      <DependentUpon>SpreadsheetPreview.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\SpreadsheetDocumentTemplate.resx">
      <DependentUpon>SpreadsheetDocumentTemplate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\WordRTFDocumentConversion.resx">
      <DependentUpon>WordRTFDocumentConversion.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\WordRTFMailMerge.resx">
      <DependentUpon>WordRTFMailMerge.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\SpreadsheetMailMerge.resx">
      <DependentUpon>SpreadsheetMailMerge.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Modules\WordRTFSignatureDemo.resx">
      <DependentUpon>WordRTFSignatureDemo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources1.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <EmbeddedResource Include="Docs.png" />
    <EmbeddedResource Include="Docs_light.png" />
    <Content Include="nwindDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>nwindDataSet.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </Content>
    <None Include="nwindDataSet.xsc">
      <DependentUpon>nwindDataSet.xsd</DependentUpon>
    </None>
    <None Include="nwindDataSet.xss">
      <DependentUpon>nwindDataSet.xsd</DependentUpon>
    </None>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Modules\About.resx">
      <DependentUpon>About.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="MainDemo.ico" />
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <!--ONLY NETCORE3  
    <PropertyGroup>
	<PlatformTarget>x86</PlatformTarget>
        <AppendRuntimeIdentifierToOutputPath>false</AppendRuntimeIdentifierToOutputPath>
        <CopyLocalLockFileAssemblies>false</CopyLocalLockFileAssemblies>
        <GenerateRuntimeConfigDevFile>true</GenerateRuntimeConfigDevFile>
    </PropertyGroup>
ONLY NETCORE3-->
  <!--ONLY NETCORE3
<ItemGroup>
  <PackageReference Include="DevExpress.Design.Nuget" Version="25.1.0" Condition="$(DEMO_REMOVE)==''" />
  <PackageReference Include="System.Data.OleDb" Version="8.0.1" />
</ItemGroup>
ONLY NETCORE3-->
</Project>