﻿using System;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using DevExpress.DXperience.Demos;
using DevExpress.Pdf;
using DevExpress.XtraBars.Navigation;
using DevExpress.XtraEditors;
using DevExpress.XtraPdfViewer;
using DevExpress.XtraVerticalGrid.Events;

namespace DevExpress.Docs.Demos {
    public partial class PdfFormCreation : TutorialControlBase {
        static readonly string demoDocumentPath = DemoUtils.GetRelativePath("FormDemo.pdf");
        static readonly string editingTempFilePath = DemoUtils.GetTempFileName();

        string sourceTempFilePath;
        DocumentFormController controller;
        FormFieldData selectedItem;
        PdfPageDragItem dragManager;
        Cursor currentCursor = null;
        string creatingFieldType;
        public override bool NoGap {
            get { return true; }
        }
        bool CursorIsOverPage {
            get { return pdfViewer.GetDocumentPosition(pdfViewer.PointToClient(MousePosition), false) != null; }
        }
        public PdfFormCreation() {
            InitializeComponent();
            try {
                sourceTempFilePath = DemoUtils.GetTempFileName();
                using(PdfDocumentProcessor processor = new PdfDocumentProcessor()) {
                    processor.LoadDocument(demoDocumentPath);
                    processor.ResetFormData();
                    processor.FlattenForm();
                    processor.SaveDocument(sourceTempFilePath);
                }
                controller = new DocumentFormController(pdfViewer, sourceTempFilePath, editingTempFilePath);
                controller.UpdateDocument();
                accordionControl.MouseDown += AccordionControlMouseDown;
                accordionControl.KeyDown += ProcessCommonKeys;
                accordionControl.DragEnter += AccordionControlDragEnter;
                accordionControl.QueryContinueDrag += AccordionControlQueryContinueDrag;
                pdfViewer.Paint += PdfViewerPaint;
                pdfViewer.KeyDown += ProcessCommonKeys;
                pdfViewer.KeyDown += PdfViewerKeyDown;
                pdfViewer.MouseDown += PdfViewerMouseDown;
                pdfViewer.MouseMove += PdfViewerMouseMove;
                pdfViewer.MouseUp += PdfViewerMouseUp;
                pdfViewer.DragEnter += PdfViewerDragEnter;
                pdfViewer.DragOver += PdfViewerDragOver;
                pdfViewer.DragDrop += PdfViewerDragDrop;
                PdfViewerHelpers.EnableCustomToolMouseNavigation(pdfViewer);
                propertyGridControl.CellValueChanged += PropertyGridControlValueChanged;
                propertyGridControl.KeyDown += ProcessCommonKeys;
            }
            catch { }
        }
        void SelectField(FormFieldData item) {
            if(selectedItem != item) {
                selectedItem = item;
                dragManager = item == null ? null : new PdfPageDragItem(pdfViewer, item, controller);
                propertyGridControl.SelectedObject = null;
                propertyGridControl.SelectedObject = item;
                pdfViewer.Focus();
            }
        }
        void CreateField(string fieldType, Point location) {
            FormFieldData field = FormFieldData.Create(fieldType, pdfViewer.GetDocumentPosition(location, false), controller);
            if(field != null) {
                controller.AddField(field);
                SelectField(field);
            }
        }
        void EndFieldCreation() {
            creatingFieldType = null;
            currentCursor = null;
            pdfViewer.Cursor = Cursors.Default;
            accordionControl.SelectedElement = null;
        }
        void PropertyGridControlValueChanged(object sender, CellValueChangedEventArgs e) {
            dragManager.Update();
        }
        void PdfViewerMouseDown(object sender, MouseEventArgs e) {
            if(e.Button == MouseButtons.Left && String.IsNullOrEmpty(creatingFieldType) && (dragManager == null || !dragManager.StartDrag(e.Location))) {
                SelectField(controller.GetFormFieldFromPoint(e.Location));
                pdfViewer.Invalidate();
            }
        }
        void PdfViewerMouseMove(object sender, MouseEventArgs e) {
            if(dragManager != null && currentCursor == null)
                dragManager.UpdateCursor(e.Location);
            else
                pdfViewer.Cursor = currentCursor != null && CursorIsOverPage ? currentCursor : Cursors.Default;
            if(e.Button == MouseButtons.Left && dragManager != null && String.IsNullOrEmpty(creatingFieldType))
                dragManager.ContinueDrag(e.Location);
        }
        void PdfViewerMouseUp(object sender, MouseEventArgs e) {
            if(e.Button == MouseButtons.Left)
                if(CursorIsOverPage && !String.IsNullOrEmpty(creatingFieldType)) {
                    CreateField(creatingFieldType, e.Location);
                    EndFieldCreation();
                }
                else if(dragManager != null) {
                    dragManager.EndDrag(e.Location);
                    propertyGridControl.UpdateData();
                }
        }
        void PdfViewerDragDrop(object sender, DragEventArgs e) {
            CreateField(e.Data.GetData(DataFormats.Text) as string, pdfViewer.PointToClient(new Point(e.X, e.Y)));
        }
        void PdfViewerDragOver(object sender, DragEventArgs e) {
            e.Effect = FormFieldType.IsValidType(e.Data.GetData(DataFormats.Text) as string) && pdfViewer.GetDocumentPosition(pdfViewer.PointToClient(new Point(e.X, e.Y)), false) == null ? DragDropEffects.None : DragDropEffects.All;
        }
        void PdfViewerDragEnter(object sender, DragEventArgs e) {
            PdfViewerDragOver(sender, e);
        }
        void PdfViewerKeyDown(object sender, KeyEventArgs e) {
            switch(e.KeyCode) {
                case Keys.Escape:
                    EndFieldCreation();
                    break;
                case Keys.Delete:
                    if(selectedItem != null) {
                        selectedItem.Dispose();
                        controller.RemoveField(selectedItem);
                        SelectField(null);
                    }
                    break;
                case Keys.PageDown:
                    pdfViewer.CurrentPageNumber = Math.Min(pdfViewer.CurrentPageNumber + 1, pdfViewer.PageCount);
                    break;
                case Keys.PageUp:
                    pdfViewer.CurrentPageNumber = Math.Max(pdfViewer.CurrentPageNumber - 1, 1);
                    break;
            }
            if(selectedItem != null) {
                switch(e.KeyCode) {
                    case Keys.Left:
                        selectedItem.Rectangle.Move(-1, 0);
                        break;
                    case Keys.Right:
                        selectedItem.Rectangle.Move(1, 0);
                        break;
                    case Keys.Up:
                        selectedItem.Rectangle.Move(0, 1);
                        break;
                    case Keys.Down:
                        selectedItem.Rectangle.Move(0, -1);
                        break;
                }
                dragManager.Update();
            }
        }
        void PdfViewerPaint(object sender, PaintEventArgs e) {
            controller.Draw(e.Graphics);
            if(selectedItem != null)
                controller.DrawSelectedItem(e.Graphics, selectedItem.GetClientRectangle(pdfViewer));
            if(dragManager != null)
                dragManager.DrawDragPoints(e.Graphics, SystemBrushes.Highlight);
        }
        void AccordionControlMouseDown(object sender, MouseEventArgs e) {
            AccordionControlHitInfo hitInfo = accordionControl.CalcHitInfo(e.Location);
            AccordionElementBaseViewInfo info = hitInfo.ItemInfo;
            if(hitInfo.HitTest.HasFlag(AccordionControlHitTest.Item) && info != null && info.Element != null) {
                string name = info.Element.Name;
                creatingFieldType = null;
                if(name == accordionControlTextFieldElement.Name)
                    creatingFieldType = FormFieldType.TextBox;
                else if(name == accordionControlComboBoxElement.Name)
                    creatingFieldType = FormFieldType.ComboBox;
                else if(name == accordionControlCheckBoxElement.Name)
                    creatingFieldType = FormFieldType.CheckBox;
                else if(name == accordionControlListBoxElement.Name)
                    creatingFieldType = FormFieldType.ListBox;
                else if(name == accordionControlSignatureElement.Name)
                    creatingFieldType = FormFieldType.Signature;
                if(string.IsNullOrEmpty(creatingFieldType))
                    EndFieldCreation();
                else
                    accordionControl.DoDragDrop(creatingFieldType, DragDropEffects.All);
            }
        }
        void AccordionControlDragEnter(object sender, DragEventArgs e) {
            e.Effect = DragDropEffects.None;
        }
        void AccordionControlQueryContinueDrag(object sender, QueryContinueDragEventArgs e) {
            if(e.Action == DragAction.Drop) {
                if(!CursorIsOverPage)
                    e.Action = DragAction.Cancel;
                else EndFieldCreation();
            }
            if(e.Action == DragAction.Cancel)
                currentCursor = Cursors.Cross;
        }
        void AccordionControlOpenElementClick(object sender, EventArgs e) {
            using(PdfDocumentProcessor processor = new PdfDocumentProcessor()) {
                using(PdfDocumentProcessorAndViewerFileHelper fileHelper = new PdfDocumentProcessorAndViewerFileHelper(processor, pdfViewer)) {
                    if(fileHelper.LoadDocumentWithDialog()) {
                        try {
                            DemoUtils.DeleteTempFile(sourceTempFilePath);
                            sourceTempFilePath = DemoUtils.GetTempFileName();
                            processor.SaveDocument(sourceTempFilePath);
                            controller = new DocumentFormController(pdfViewer, sourceTempFilePath, editingTempFilePath);
                            controller.UpdateDocument();
                            SelectField(null);
                            pdfViewer.ZoomMode = PdfZoomMode.PageLevel;
                        }
                        catch {
                            XtraMessageBox.Show("An error occurred while processing the PDF document", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }
                    }
                }
            }
        }
        void AccordionControlSaveElementClick(object sender, EventArgs e) {
            if(new PdfViewerFileHelper(pdfViewer).SaveDocument())
                pdfViewer.LoadDocument(editingTempFilePath);
        }
        void AccordionControlRemoveElementClick(object sender, EventArgs e) {
            controller.RemoveExistingForm();
        }
        void ProcessCommonKeys(object sender, KeyEventArgs e) {
            if(e.Control) {
                switch(e.KeyCode) {
                    case Keys.S:
                        AccordionControlSaveElementClick(this, EventArgs.Empty);
                        break;
                    case Keys.O:
                        AccordionControlOpenElementClick(this, EventArgs.Empty);
                        break;
                }
            }
        }
        void UnsubscribeFromEvents() {
            if(pdfViewer != null) {
                pdfViewer.Paint -= PdfViewerPaint;
                pdfViewer.KeyDown -= PdfViewerKeyDown;
                pdfViewer.KeyDown -= ProcessCommonKeys;
                pdfViewer.MouseDown -= PdfViewerMouseDown;
                pdfViewer.MouseMove -= PdfViewerMouseMove;
                pdfViewer.MouseUp -= PdfViewerMouseUp;
                pdfViewer.DragEnter -= PdfViewerDragEnter;
                pdfViewer.DragDrop -= PdfViewerDragDrop;
                pdfViewer.DragOver -= PdfViewerDragOver;
            }
            if(accordionControl != null) {
                accordionControl.MouseDown -= AccordionControlMouseDown;
                accordionControl.KeyDown -= ProcessCommonKeys;
                accordionControl.DragEnter -= AccordionControlDragEnter;
                accordionControl.QueryContinueDrag -= AccordionControlQueryContinueDrag;
            }
            if(propertyGridControl != null) {
                propertyGridControl.CellValueChanged -= PropertyGridControlValueChanged;
                propertyGridControl.KeyDown -= ProcessCommonKeys;
            }
        }
        protected override bool ProcessCmdKey(ref Message msg, Keys keyData) {
            if(keyData == Keys.Escape)
                EndFieldCreation();
            return base.ProcessCmdKey(ref msg, keyData);
        }
    }
}
