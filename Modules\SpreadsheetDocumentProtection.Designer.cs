﻿namespace DevExpress.Docs.Demos {
    partial class SpreadsheetDocumentProtection {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing) {
            if (disposing && (components != null)) {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent() {
            this.spreadsheetPreview1 = new DevExpress.Docs.Demos.SpreadsheetPreview();
            this.sidePanel1 = new DevExpress.XtraEditors.SidePanel();
            this.btnProtectAndSave = new DevExpress.XtraEditors.SimpleButton();
            this.lblPermissions = new DevExpress.XtraEditors.LabelControl();
            this.edPermissions = new DevExpress.XtraEditors.CheckedListBoxControl();
            this.edWorksheetPassword = new DevExpress.XtraEditors.TextEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.cbProtectWorksheet = new DevExpress.XtraEditors.CheckEdit();
            this.edWorkbookPassword = new DevExpress.XtraEditors.TextEdit();
            this.lblWorkbookPassword = new DevExpress.XtraEditors.LabelControl();
            this.cbProtectWorkbookWindows = new DevExpress.XtraEditors.CheckEdit();
            this.cbProtectWorkbookStructure = new DevExpress.XtraEditors.CheckEdit();
            this.cbProtectWorkbook = new DevExpress.XtraEditors.CheckEdit();
            this.sidePanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edPermissions)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edWorksheetPassword.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbProtectWorksheet.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edWorkbookPassword.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbProtectWorkbookWindows.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbProtectWorkbookStructure.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbProtectWorkbook.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // spreadsheetPreview1
            // 
            this.spreadsheetPreview1.CanShowBorders = false;
            this.spreadsheetPreview1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.spreadsheetPreview1.Location = new System.Drawing.Point(0, 0);
            this.spreadsheetPreview1.Name = "spreadsheetPreview1";
            this.spreadsheetPreview1.Size = new System.Drawing.Size(572, 565);
            this.spreadsheetPreview1.TabIndex = 72;
            this.spreadsheetPreview1.Workbook = null;
            // 
            // sidePanel1
            // 
            this.sidePanel1.AllowResize = false;
            this.sidePanel1.Controls.Add(this.btnProtectAndSave);
            this.sidePanel1.Controls.Add(this.lblPermissions);
            this.sidePanel1.Controls.Add(this.edPermissions);
            this.sidePanel1.Controls.Add(this.edWorksheetPassword);
            this.sidePanel1.Controls.Add(this.labelControl1);
            this.sidePanel1.Controls.Add(this.cbProtectWorksheet);
            this.sidePanel1.Controls.Add(this.edWorkbookPassword);
            this.sidePanel1.Controls.Add(this.lblWorkbookPassword);
            this.sidePanel1.Controls.Add(this.cbProtectWorkbookWindows);
            this.sidePanel1.Controls.Add(this.cbProtectWorkbookStructure);
            this.sidePanel1.Controls.Add(this.cbProtectWorkbook);
            this.sidePanel1.Dock = System.Windows.Forms.DockStyle.Right;
            this.sidePanel1.Location = new System.Drawing.Point(572, 0);
            this.sidePanel1.Name = "sidePanel1";
            this.sidePanel1.Size = new System.Drawing.Size(209, 565);
            this.sidePanel1.TabIndex = 75;
            this.sidePanel1.Text = "sidePanel1";
            // 
            // btnProtectAndSave
            // 
            this.btnProtectAndSave.Location = new System.Drawing.Point(28, 526);
            this.btnProtectAndSave.Name = "btnProtectAndSave";
            this.btnProtectAndSave.Size = new System.Drawing.Size(166, 23);
            this.btnProtectAndSave.TabIndex = 95;
            this.btnProtectAndSave.Text = "Protect and Save...";
            this.btnProtectAndSave.Click += new System.EventHandler(this.btnProtectAndSave_Click);
            // 
            // lblPermissions
            // 
            this.lblPermissions.Location = new System.Drawing.Point(28, 230);
            this.lblPermissions.Name = "lblPermissions";
            this.lblPermissions.Size = new System.Drawing.Size(84, 13);
            this.lblPermissions.TabIndex = 94;
            this.lblPermissions.Text = "Allow all users to:";
            // 
            // edPermissions
            // 
            this.edPermissions.AccessibleName = "Permissions";
            this.edPermissions.AccessibleRole = System.Windows.Forms.AccessibleRole.List;
            this.edPermissions.CheckOnClick = true;
            this.edPermissions.Items.AddRange(new DevExpress.XtraEditors.Controls.CheckedListBoxItem[] {
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("Select locked cells", System.Windows.Forms.CheckState.Checked),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("Select unlocked cells", System.Windows.Forms.CheckState.Checked),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("Format cells"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("Format columns"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("Format rows"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("Insert columns"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("Insert rows"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("Insert hyperlinks"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("Delete columns"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("Delete rows"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("Sort"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("Use AutoFilter"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("Use PivotTable reports"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("Edit objects"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("Edit scenarios")});
            this.edPermissions.Location = new System.Drawing.Point(28, 249);
            this.edPermissions.Name = "edPermissions";
            this.edPermissions.Size = new System.Drawing.Size(166, 260);
            this.edPermissions.TabIndex = 93;
            // 
            // edWorksheetPassword
            // 
            this.edWorksheetPassword.Location = new System.Drawing.Point(28, 201);
            this.edWorksheetPassword.Name = "edWorksheetPassword";
            this.edWorksheetPassword.Properties.MaxLength = 255;
            this.edWorksheetPassword.Properties.PasswordChar = '*';
            this.edWorksheetPassword.Size = new System.Drawing.Size(166, 20);
            this.edWorksheetPassword.TabIndex = 92;
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(28, 182);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(166, 13);
            this.labelControl1.TabIndex = 91;
            this.labelControl1.Text = "Password to unprotect worksheet:";
            // 
            // cbProtectWorksheet
            // 
            this.cbProtectWorksheet.EditValue = true;
            this.cbProtectWorksheet.Location = new System.Drawing.Point(12, 154);
            this.cbProtectWorksheet.Name = "cbProtectWorksheet";
            this.cbProtectWorksheet.Properties.Caption = "Protect Worksheet";
            this.cbProtectWorksheet.Size = new System.Drawing.Size(117, 20);
            this.cbProtectWorksheet.TabIndex = 90;
            this.cbProtectWorksheet.CheckedChanged += new System.EventHandler(this.cbProtect_CheckedChanged);
            // 
            // edWorkbookPassword
            // 
            this.edWorkbookPassword.Location = new System.Drawing.Point(28, 110);
            this.edWorkbookPassword.Name = "edWorkbookPassword";
            this.edWorkbookPassword.Properties.MaxLength = 255;
            this.edWorkbookPassword.Properties.PasswordChar = '*';
            this.edWorkbookPassword.Size = new System.Drawing.Size(166, 20);
            this.edWorkbookPassword.TabIndex = 89;
            // 
            // lblWorkbookPassword
            // 
            this.lblWorkbookPassword.Location = new System.Drawing.Point(28, 91);
            this.lblWorkbookPassword.Name = "lblWorkbookPassword";
            this.lblWorkbookPassword.Size = new System.Drawing.Size(99, 13);
            this.lblWorkbookPassword.TabIndex = 88;
            this.lblWorkbookPassword.Text = "Password (optional):";
            // 
            // cbProtectWorkbookWindows
            // 
            this.cbProtectWorkbookWindows.Location = new System.Drawing.Point(28, 66);
            this.cbProtectWorkbookWindows.Name = "cbProtectWorkbookWindows";
            this.cbProtectWorkbookWindows.Properties.Caption = "Windows";
            this.cbProtectWorkbookWindows.Size = new System.Drawing.Size(99, 20);
            this.cbProtectWorkbookWindows.TabIndex = 87;
            // 
            // cbProtectWorkbookStructure
            // 
            this.cbProtectWorkbookStructure.EditValue = true;
            this.cbProtectWorkbookStructure.Location = new System.Drawing.Point(28, 41);
            this.cbProtectWorkbookStructure.Name = "cbProtectWorkbookStructure";
            this.cbProtectWorkbookStructure.Properties.Caption = "Structure";
            this.cbProtectWorkbookStructure.Size = new System.Drawing.Size(99, 20);
            this.cbProtectWorkbookStructure.TabIndex = 86;
            // 
            // cbProtectWorkbook
            // 
            this.cbProtectWorkbook.EditValue = true;
            this.cbProtectWorkbook.Location = new System.Drawing.Point(12, 16);
            this.cbProtectWorkbook.Name = "cbProtectWorkbook";
            this.cbProtectWorkbook.Properties.Caption = "Protect Workbook";
            this.cbProtectWorkbook.Size = new System.Drawing.Size(117, 20);
            this.cbProtectWorkbook.TabIndex = 85;
            this.cbProtectWorkbook.CheckedChanged += new System.EventHandler(this.cbProtect_CheckedChanged);
            // 
            // SpreadsheetDocumentProtection
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.spreadsheetPreview1);
            this.Controls.Add(this.sidePanel1);
            this.Name = "SpreadsheetDocumentProtection";
            this.Size = new System.Drawing.Size(781, 565);
            this.sidePanel1.ResumeLayout(false);
            this.sidePanel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edPermissions)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edWorksheetPassword.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbProtectWorksheet.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edWorkbookPassword.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbProtectWorkbookWindows.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbProtectWorkbookStructure.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbProtectWorkbook.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private SpreadsheetPreview spreadsheetPreview1;
        private XtraEditors.SidePanel sidePanel1;
        protected XtraEditors.SimpleButton btnProtectAndSave;
        private XtraEditors.LabelControl lblPermissions;
        private XtraEditors.CheckedListBoxControl edPermissions;
        private XtraEditors.TextEdit edWorksheetPassword;
        private XtraEditors.LabelControl labelControl1;
        protected XtraEditors.CheckEdit cbProtectWorksheet;
        private XtraEditors.TextEdit edWorkbookPassword;
        private XtraEditors.LabelControl lblWorkbookPassword;
        protected XtraEditors.CheckEdit cbProtectWorkbookWindows;
        protected XtraEditors.CheckEdit cbProtectWorkbookStructure;
        protected XtraEditors.CheckEdit cbProtectWorkbook;
    }
}
