﻿using System.ComponentModel;
using System.Drawing;
using DevExpress.Pdf;

namespace DevExpress.Docs.Demos {
    public enum ShapeType { Rectangle, Ellipse }
    public class PageShapeContent : PageContentData {
        float fWidth;
        Color fBorderColor;
        Color fFillColor;
        ShapeType fShape;

        public ShapeType ShapeType {
            get { return fShape; }
            set {
                fShape = value;
                Controller.UpdateDocument();
            }
        }
        public float Width {
            get { return fWidth; }
            set {
                fWidth = value;
                Controller.UpdateDocument();
            }
        }

        [TypeConverter(typeof(ColorConverter))]
        public Color BorderColor {
            get { return fBorderColor; }
            set {
                fBorderColor = value;
                Controller.UpdateDocument();
            }
        }

        [TypeConverter(typeof(ColorConverter))]
        public Color FillColor {
            get { return fFillColor; }
            set {
                fFillColor = value;
                Controller.UpdateDocument();
            }
        }

        public PageShapeContent(PdfDocumentPosition position, PageContentController controller) : base(position, controller) {
            Width = 5;
            BorderColor = Color.Red;
            FillColor = Color.Yellow;
        }
        public override void FillContent(PdfGraphics graphics, PdfRectangle pageCropBox) {
            RectangleF rect = new RectangleF(Rectangle.Left, (float)(pageCropBox.Height - Rectangle.Top), (float)Rectangle.InnerRectangle.Width, (float)Rectangle.InnerRectangle.Height);
            switch(ShapeType) {
                case ShapeType.Rectangle:
                    graphics.FillRectangle(new SolidBrush(FillColor), rect);
                    graphics.DrawRectangle(new Pen(BorderColor, Width), rect);
                    break;
                case ShapeType.Ellipse:
                    graphics.FillEllipse(new SolidBrush(FillColor), rect);
                    graphics.DrawEllipse(new Pen(BorderColor, Width), rect);
                    break;
            }
        }
    }
}
