﻿namespace DevExpress.Docs.Demos
{
	partial class PdfTextMarkupAnnotations
	{
		/// <summary> 
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		#region Component Designer generated code

		/// <summary> 
		/// Required method for Designer support - do not modify 
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            this.pdfViewer = new DevExpress.XtraPdfViewer.PdfViewer();
            this.barManager = new DevExpress.XtraBars.BarManager(this.components);
            this.barAndDockingController = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.deleteAnnotationItem = new DevExpress.XtraBars.BarButtonItem();
            this.treeList = new DevExpress.XtraTreeList.TreeList();
            this.titleColumn = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.propertyGridControl = new DevExpress.XtraVerticalGrid.PropertyGridControl();
            this.buttonOpen = new DevExpress.XtraEditors.SimpleButton();
            this.buttonSaveAs = new DevExpress.XtraEditors.SimpleButton();
            this.buttonFind = new DevExpress.XtraEditors.SimpleButton();
            this.popupMenu = new DevExpress.XtraBars.PopupMenu(this.components);
            this.actionsSidePanel = new DevExpress.XtraEditors.SidePanel();
            this.actionsTablePanel = new DevExpress.Utils.Layout.TablePanel();
            this.annotationListGroup = new DevExpress.XtraEditors.GroupControl();
            this.annotationPropertiesGroup = new DevExpress.XtraEditors.GroupControl();
            ((System.ComponentModel.ISupportInitialize)(this.barManager)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.treeList)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.propertyGridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenu)).BeginInit();
            this.actionsSidePanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.actionsTablePanel)).BeginInit();
            this.actionsTablePanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.annotationListGroup)).BeginInit();
            this.annotationListGroup.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.annotationPropertiesGroup)).BeginInit();
            this.annotationPropertiesGroup.SuspendLayout();
            this.SuspendLayout();
            // 
            // pdfViewer
            // 
            this.pdfViewer.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pdfViewer.Location = new System.Drawing.Point(0, 0);
            this.pdfViewer.MenuManager = this.barManager;
            this.pdfViewer.Name = "pdfViewer";
            this.pdfViewer.NavigationPaneInitialVisibility = DevExpress.XtraPdfViewer.PdfNavigationPaneVisibility.Hidden;
            this.pdfViewer.ReadOnly = true;
            this.pdfViewer.Size = new System.Drawing.Size(383, 564);
            this.pdfViewer.TabIndex = 7;
            this.pdfViewer.TabStop = false;
            this.pdfViewer.ZoomMode = DevExpress.XtraPdfViewer.PdfZoomMode.FitToWidth;
            // 
            // barManager
            // 
            this.barManager.Controller = this.barAndDockingController;
            this.barManager.DockControls.Add(this.barDockControlTop);
            this.barManager.DockControls.Add(this.barDockControlBottom);
            this.barManager.DockControls.Add(this.barDockControlLeft);
            this.barManager.DockControls.Add(this.barDockControlRight);
            this.barManager.Form = this;
            this.barManager.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.deleteAnnotationItem});
            // 
            // barAndDockingController
            // 
            this.barAndDockingController.PropertiesBar.AllowLinkLighting = false;
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            this.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControlTop.Location = new System.Drawing.Point(0, 0);
            this.barDockControlTop.Manager = this.barManager;
            this.barDockControlTop.Margin = new System.Windows.Forms.Padding(2);
            this.barDockControlTop.Size = new System.Drawing.Size(737, 0);
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            this.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControlBottom.Location = new System.Drawing.Point(0, 564);
            this.barDockControlBottom.Manager = this.barManager;
            this.barDockControlBottom.Margin = new System.Windows.Forms.Padding(2);
            this.barDockControlBottom.Size = new System.Drawing.Size(737, 0);
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            this.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControlLeft.Location = new System.Drawing.Point(0, 0);
            this.barDockControlLeft.Manager = this.barManager;
            this.barDockControlLeft.Margin = new System.Windows.Forms.Padding(2);
            this.barDockControlLeft.Size = new System.Drawing.Size(0, 564);
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            this.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControlRight.Location = new System.Drawing.Point(737, 0);
            this.barDockControlRight.Manager = this.barManager;
            this.barDockControlRight.Margin = new System.Windows.Forms.Padding(2);
            this.barDockControlRight.Size = new System.Drawing.Size(0, 564);
            // 
            // deleteAnnotationItem
            // 
            this.deleteAnnotationItem.Caption = "Delete current annotation";
            this.deleteAnnotationItem.Id = 0;
            this.deleteAnnotationItem.Name = "deleteAnnotationItem";
            this.deleteAnnotationItem.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.OnDeleteAnnotationItemClick);
            // 
            // treeList
            // 
            this.treeList.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.treeList.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.titleColumn});
            this.treeList.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeList.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeList.FixedLineWidth = 1;
            this.treeList.HorzScrollStep = 2;
            this.treeList.KeyFieldName = "Id";
            this.treeList.Location = new System.Drawing.Point(2, 23);
            this.treeList.Margin = new System.Windows.Forms.Padding(2);
            this.treeList.MinWidth = 16;
            this.treeList.Name = "treeList";
            this.treeList.OptionsBehavior.Editable = false;
            this.treeList.OptionsClipboard.AllowCopy = DevExpress.Utils.DefaultBoolean.False;
            this.treeList.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.treeList.OptionsView.FocusRectStyle = DevExpress.XtraTreeList.DrawFocusRectStyle.None;
            this.treeList.OptionsView.ShowColumns = false;
            this.treeList.OptionsView.ShowHorzLines = false;
            this.treeList.OptionsView.ShowIndicator = false;
            this.treeList.OptionsView.ShowVertLines = false;
            this.treeList.ParentFieldName = "PageNumber";
            this.treeList.Size = new System.Drawing.Size(319, 200);
            this.treeList.TabIndex = 0;
            this.treeList.TreeLevelWidth = 12;
            this.treeList.FocusedNodeChanged += new DevExpress.XtraTreeList.FocusedNodeChangedEventHandler(this.OnFocusedNodeChanged);
            this.treeList.KeyDown += new System.Windows.Forms.KeyEventHandler(this.OnTreeListKeyDown);
            this.treeList.MouseDown += new System.Windows.Forms.MouseEventHandler(this.OnTreeListMouseDown);
            // 
            // titleColumn
            // 
            this.titleColumn.Caption = "titleColumn";
            this.titleColumn.FieldName = "Title";
            this.titleColumn.MinWidth = 16;
            this.titleColumn.Name = "titleColumn";
            this.titleColumn.Visible = true;
            this.titleColumn.VisibleIndex = 0;
            this.titleColumn.Width = 50;
            // 
            // propertyGridControl
            // 
            this.propertyGridControl.BandsInterval = 1;
            this.propertyGridControl.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.propertyGridControl.Cursor = System.Windows.Forms.Cursors.Default;
            this.propertyGridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.propertyGridControl.Location = new System.Drawing.Point(2, 23);
            this.propertyGridControl.Margin = new System.Windows.Forms.Padding(2);
            this.propertyGridControl.Name = "propertyGridControl";
            this.propertyGridControl.OptionsView.AllowReadOnlyRowAppearance = DevExpress.Utils.DefaultBoolean.True;
            this.propertyGridControl.OptionsView.FixedLineWidth = 1;
            this.propertyGridControl.OptionsView.ShowRootCategories = false;
            this.propertyGridControl.Size = new System.Drawing.Size(319, 200);
            this.propertyGridControl.TabIndex = 0;
            this.propertyGridControl.CellValueChanged += new DevExpress.XtraVerticalGrid.Events.CellValueChangedEventHandler(this.OnCellValueChanged);
            // 
            // buttonOpen
            // 
            this.actionsTablePanel.SetColumn(this.buttonOpen, 0);
            this.buttonOpen.Dock = System.Windows.Forms.DockStyle.Fill;
            this.buttonOpen.Location = new System.Drawing.Point(15, 15);
            this.buttonOpen.Name = "buttonOpen";
            this.actionsTablePanel.SetRow(this.buttonOpen, 0);
            this.buttonOpen.Size = new System.Drawing.Size(323, 20);
            this.buttonOpen.TabIndex = 4;
            this.buttonOpen.Text = "Open...";
            this.buttonOpen.Click += new System.EventHandler(this.OnButtonOpenClick);
            // 
            // buttonSaveAs
            // 
            this.actionsTablePanel.SetColumn(this.buttonSaveAs, 0);
            this.buttonSaveAs.Dock = System.Windows.Forms.DockStyle.Fill;
            this.buttonSaveAs.Location = new System.Drawing.Point(15, 67);
            this.buttonSaveAs.Name = "buttonSaveAs";
            this.actionsTablePanel.SetRow(this.buttonSaveAs, 2);
            this.buttonSaveAs.Size = new System.Drawing.Size(323, 20);
            this.buttonSaveAs.TabIndex = 8;
            this.buttonSaveAs.Text = "Save as...";
            this.buttonSaveAs.Click += new System.EventHandler(this.OnButtonSaveAsClick);
            // 
            // buttonFind
            // 
            this.actionsTablePanel.SetColumn(this.buttonFind, 0);
            this.buttonFind.Dock = System.Windows.Forms.DockStyle.Fill;
            this.buttonFind.Location = new System.Drawing.Point(15, 41);
            this.buttonFind.Name = "buttonFind";
            this.actionsTablePanel.SetRow(this.buttonFind, 1);
            this.buttonFind.Size = new System.Drawing.Size(323, 20);
            this.buttonFind.TabIndex = 7;
            this.buttonFind.Text = "Find and Markup...";
            this.buttonFind.Click += new System.EventHandler(this.OnButtonFindClick);
            // 
            // popupMenu
            // 
            this.popupMenu.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.deleteAnnotationItem)});
            this.popupMenu.Manager = this.barManager;
            this.popupMenu.Name = "popupMenu";
            // 
            // actionsSidePanel
            // 
            this.actionsSidePanel.Controls.Add(this.actionsTablePanel);
            this.actionsSidePanel.Dock = System.Windows.Forms.DockStyle.Right;
            this.actionsSidePanel.Location = new System.Drawing.Point(383, 0);
            this.actionsSidePanel.MinimumSize = new System.Drawing.Size(200, 0);
            this.actionsSidePanel.Name = "actionsSidePanel";
            this.actionsSidePanel.Size = new System.Drawing.Size(354, 564);
            this.actionsSidePanel.TabIndex = 8;
            this.actionsSidePanel.Text = "sidePanel1";
            // 
            // actionsTablePanel
            // 
            this.actionsTablePanel.AutoSize = true;
            this.actionsTablePanel.Columns.AddRange(new DevExpress.Utils.Layout.TablePanelColumn[] {
            new DevExpress.Utils.Layout.TablePanelColumn(DevExpress.Utils.Layout.TablePanelEntityStyle.Relative, 5F)});
            this.actionsTablePanel.Controls.Add(this.buttonOpen);
            this.actionsTablePanel.Controls.Add(this.buttonFind);
            this.actionsTablePanel.Controls.Add(this.buttonSaveAs);
            this.actionsTablePanel.Controls.Add(this.annotationListGroup);
            this.actionsTablePanel.Controls.Add(this.annotationPropertiesGroup);
            this.actionsTablePanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.actionsTablePanel.Location = new System.Drawing.Point(1, 0);
            this.actionsTablePanel.Name = "actionsTablePanel";
            this.actionsTablePanel.Padding = new System.Windows.Forms.Padding(12);
            this.actionsTablePanel.Rows.AddRange(new DevExpress.Utils.Layout.TablePanelRow[] {
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Relative, 50F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Relative, 50F)});
            this.actionsTablePanel.Size = new System.Drawing.Size(353, 564);
            this.actionsTablePanel.TabIndex = 0;
            // 
            // annotationListGroup
            // 
            this.actionsTablePanel.SetColumn(this.annotationListGroup, 0);
            this.annotationListGroup.Controls.Add(this.treeList);
            this.annotationListGroup.Dock = System.Windows.Forms.DockStyle.Fill;
            this.annotationListGroup.GroupStyle = DevExpress.Utils.GroupStyle.Card;
            this.annotationListGroup.Location = new System.Drawing.Point(15, 93);
            this.annotationListGroup.Name = "annotationListGroup";
            this.actionsTablePanel.SetRow(this.annotationListGroup, 3);
            this.annotationListGroup.Size = new System.Drawing.Size(323, 225);
            this.annotationListGroup.TabIndex = 1;
            this.annotationListGroup.Text = "Text Markup Annotations";
            // 
            // annotationPropertiesGroup
            // 
            this.actionsTablePanel.SetColumn(this.annotationPropertiesGroup, 0);
            this.annotationPropertiesGroup.Controls.Add(this.propertyGridControl);
            this.annotationPropertiesGroup.Dock = System.Windows.Forms.DockStyle.Fill;
            this.annotationPropertiesGroup.GroupStyle = DevExpress.Utils.GroupStyle.Card;
            this.annotationPropertiesGroup.Location = new System.Drawing.Point(15, 324);
            this.annotationPropertiesGroup.Name = "annotationPropertiesGroup";
            this.actionsTablePanel.SetRow(this.annotationPropertiesGroup, 4);
            this.annotationPropertiesGroup.Size = new System.Drawing.Size(323, 225);
            this.annotationPropertiesGroup.TabIndex = 1;
            this.annotationPropertiesGroup.Text = "Annotation Properties";
            // 
            // PdfTextMarkupAnnotations
            // 
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.pdfViewer);
            this.Controls.Add(this.actionsSidePanel);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.Name = "PdfTextMarkupAnnotations";
            this.Size = new System.Drawing.Size(737, 564);
            ((System.ComponentModel.ISupportInitialize)(this.barManager)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.treeList)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.propertyGridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenu)).EndInit();
            this.actionsSidePanel.ResumeLayout(false);
            this.actionsSidePanel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.actionsTablePanel)).EndInit();
            this.actionsTablePanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.annotationListGroup)).EndInit();
            this.annotationListGroup.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.annotationPropertiesGroup)).EndInit();
            this.annotationPropertiesGroup.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		#endregion
        private XtraEditors.SimpleButton buttonOpen;
        private XtraTreeList.TreeList treeList;
        private XtraTreeList.Columns.TreeListColumn titleColumn;
        private XtraEditors.SimpleButton buttonFind;
        private XtraEditors.SimpleButton buttonSaveAs;
        private XtraPdfViewer.PdfViewer pdfViewer;
        private XtraVerticalGrid.PropertyGridControl propertyGridControl;
        private XtraBars.PopupMenu popupMenu;
        private XtraBars.BarButtonItem deleteAnnotationItem;
        private XtraBars.BarManager barManager;
        private XtraBars.BarDockControl barDockControlTop;
        private XtraBars.BarDockControl barDockControlBottom;
        private XtraBars.BarDockControl barDockControlLeft;
        private XtraBars.BarDockControl barDockControlRight;
        private XtraBars.BarAndDockingController barAndDockingController;
        private XtraEditors.SidePanel actionsSidePanel;
        private Utils.Layout.TablePanel actionsTablePanel;
        private XtraEditors.GroupControl annotationListGroup;
        private XtraEditors.GroupControl annotationPropertiesGroup;
    }
}
