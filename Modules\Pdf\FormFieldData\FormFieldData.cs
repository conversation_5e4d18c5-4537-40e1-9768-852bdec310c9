﻿using System;
using System.ComponentModel;
using DevExpress.Pdf;

namespace DevExpress.Docs.Demos {
    public static class FormFieldType {
        public const string TextBox = "textBox";
        public const string ComboBox = "comboBox";
        public const string ListBox = "listBox";
        public const string CheckBox = "checkBox";
        public const string Signature = "signature";

        public static bool IsValidType(string type) {
            return !string.IsNullOrEmpty(type) && (type == TextBox || type == Signature || type == ListBox || type == ComboBox || type == CheckBox);
        }
    }
    public abstract class FormFieldData : PdfContentData, IDisposable {
        public static FormFieldData Create(string fieldType, PdfDocumentPosition position, DocumentFormController controller) {
            if(position != null) {
                switch(fieldType) {
                    case FormFieldType.ComboBox:
                        return new ComboBoxFormFieldData(position, controller);
                    case FormFieldType.ListBox:
                        return new ListBoxFormFieldData(position, controller);
                    case FormFieldType.CheckBox:
                        return new CheckBoxFormFieldData(position, controller);
                    case FormFieldType.Signature:
                        return new SignatureFormFieldData(position, controller);
                    case FormFieldType.TextBox:
                        return new TextBoxFormFieldData(position, controller);
                }
            }
            return null;
        }


        readonly FormFieldAppearance fAppearance;
        string fName;
        PdfAcroFormStringAlignment fTextAlignment = PdfAcroFormStringAlignment.Near;
        bool fRequired;
        bool fReadOnly;
        bool fPrint = true;
        string fToolTip;

        [Category(AppearanceCategory)]
        [TypeConverter(typeof(ExpandableObjectConverter))]
        public FormFieldAppearance Appearance { get { return fAppearance; } }

        [Category(DesignCategory)]
        public string Name {
            get { return fName; }
            set {
                string oldName = fName;
                fName = value;
                try {
                    UpdateModel();
                }
                catch {
                    fName = oldName;
                    throw;
                }
            }
        }

        [Category(AppearanceCategory)]
        public PdfAcroFormStringAlignment TextAlignment {
            get { return fTextAlignment; }
            set {
                fTextAlignment = value;
                UpdateModel();
            }
        }

        [Category(BehaviorCategory)]
        public bool Required {
            get { return fRequired; }
            set {
                fRequired = value;
                UpdateModel();
            }
        }

        [Category(BehaviorCategory)]
        public bool ReadOnly {
            get { return fReadOnly; }
            set {
                fReadOnly = value;
                UpdateModel();
            }
        }

        [Category(BehaviorCategory)]
        public bool Print {
            get { return fPrint; }
            set {
                fPrint = value;
                UpdateModel();
            }
        }

        [Category(BehaviorCategory)]
        public string ToolTip {
            get { return fToolTip; }
            set {
                fToolTip = value;
                UpdateModel();
            }
        }

        public FormFieldData(PdfDocumentPosition position, DocumentFormController controller) : base(position, controller) {
            fName = controller.GetNextName();
            fAppearance = new FormFieldAppearance(controller);
        }

        public PdfAcroFormField CreateAcroFormField() {
            PdfAcroFormCommonVisualField formField = CreateVisualFormField();
            formField.TextAlignment = TextAlignment;
            formField.Appearance = Appearance.CreateAcroFormFieldAppearance();
            formField.ReadOnly = ReadOnly;
            formField.Required = Required;
            formField.Print = Print;
            formField.ToolTip = ToolTip;
            return formField;
        }
        public override void Dispose() {
            if(fAppearance != null)
                fAppearance.Dispose();
        }
        protected abstract PdfAcroFormCommonVisualField CreateVisualFormField();
    }
}
