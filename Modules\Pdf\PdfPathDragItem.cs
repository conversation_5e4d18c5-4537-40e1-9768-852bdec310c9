﻿using System.Drawing;
using DevExpress.Pdf;
using DevExpress.XtraPdfViewer;

namespace DevExpress.Docs.Demos {
    public class PdfPathDragItem : PdfPageDragItem {
        PagePathContent PathItem { get { return (PagePathContent)Item; } }

        public PdfPathDragItem(PdfViewer viewer, PagePathContent item, IDocumentController controller) : base(viewer, item, controller) {
        }
        public override void Update() {
            DragPoints = DragPoint.CreateDragPoints(PathItem.Points);
            Viewer.Invalidate();
        }
        public override void Click(PointF clientPoint) {
            DragPoint p = null;
            PdfDocumentPosition position = Viewer.GetDocumentPosition(clientPoint, true);
            foreach(DragPoint dragPoint in DragPoints)
                if(dragPoint.Contains(position.Point))
                    p = dragPoint;
            if(p == null) {
                PathItem.Points.Add(position.Point);
                UpdateDocument();
            }
            else if(PathItem.Points.Count > 2) {
                PathItem.Points.RemoveAt(p.Index);
                UpdateDocument();
            }
        }
        void UpdateDocument() {
            PathItem.UpdateRectangle();
            Controller.UpdateDocument();
            Update();
        }
        public override void ContinueDrag(PointF clientPoint) {
            if(IsDragging) {
                PdfDocumentPosition position = Viewer.GetDocumentPosition(clientPoint, true);
                if(position.PageNumber == Item.PageNumber) {
                    PathItem.Points[DragIndex] = position.Point;
                    PathItem.UpdateRectangle();
                    Update();
                }
            }
        }
        public override void DrawSelectedItem(Graphics g) {
            System.Collections.Generic.List<PdfPoint> points = PathItem.Points;
            for(int i = 1; i < points.Count; i++) {
                PointF p1 = Viewer.GetClientPoint(new PdfDocumentPosition(PathItem.PageNumber, points[i - 1]));
                PointF p2 = Viewer.GetClientPoint(new PdfDocumentPosition(PathItem.PageNumber, points[i]));
                g.DrawLine(PageContentController.highlight, new Point((int)p1.X, (int)p1.Y), new Point((int)p2.X, (int)p2.Y));
            }
        }
    }
}
