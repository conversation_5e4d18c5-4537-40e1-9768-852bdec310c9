﻿using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using DevExpress.Pdf;

namespace DevExpress.Docs.Demos {
    public partial class PdfClearPageContent : PdfTutorialControl {
        readonly Pen selectionInProgresPen = new Pen(SystemColors.Highlight, 1) { DashStyle = DashStyle.Dash };
        PdfDocumentPosition startPos;
        PdfDocumentPosition endPos;
        bool selectionInProgress;
        public PdfClearPageContent() {
            InitializeComponent();
            pdfViewer.DetachStreamAfterLoadComplete = true;
            pdfViewer.ZoomMode = XtraPdfViewer.PdfZoomMode.FitToWidth;
            pdfViewer.CursorMode = XtraPdfViewer.PdfCursorMode.Custom;
            pdfViewer.MouseDown += OnViewerMouseDown;
            pdfViewer.MouseMove += OnViewerMouseMove;
            pdfViewer.MouseUp += OnViewerMouseUp;
            pdfViewer.Paint += OnViewerPaint;
            PdfViewerHelpers.EnableCustomToolMouseNavigation(pdfViewer);
            startPos = new PdfDocumentPosition(1, new PdfPoint(100, 500));
            endPos = new PdfDocumentPosition(1, new PdfPoint(400, 750));
            try {
                pdfViewer.LoadDocument(DemoUtils.GetRelativePath("PageContent.pdf"));
                CreatePermanentRedactAnnotation();
            }
            catch { Enabled = false; }
        }
        void OnViewerPaint(object sender, PaintEventArgs e) {
            if(startPos != null && endPos != null) {
                PointF start = pdfViewer.GetClientPoint(startPos);
                PointF end = pdfViewer.GetClientPoint(endPos);
                RectangleF rect = RectangleF.FromLTRB(Math.Min(start.X, end.X), Math.Min(start.Y, end.Y), Math.Max(start.X, end.X), Math.Max(start.Y, end.Y));
                e.Graphics.DrawRectangle(selectionInProgress ? selectionInProgresPen : SystemPens.Highlight, rect.X, rect.Y, rect.Width, rect.Height);
            }
        }
        void UpdateButtons() {
            clearContentBtn.Enabled = redactContentBtn.Enabled = endPos != null && startPos != null;
        }
        void OnViewerMouseMove(object sender, MouseEventArgs e) {
            PdfDocumentPosition position = pdfViewer.GetDocumentPosition(e.Location, false);
            pdfViewer.Cursor = position != null ? Cursors.Cross : Cursors.Arrow;
            if(selectionInProgress && position != null && position.PageNumber == startPos.PageNumber) {
                endPos = position;
                pdfViewer.Invalidate();
            }
            UpdateButtons();
        }
        void OnViewerMouseDown(object sender, MouseEventArgs e) {
            startPos = pdfViewer.GetDocumentPosition(e.Location, false);
            selectionInProgress = startPos != null;
            endPos = null;
            pdfViewer.Invalidate();
            UpdateButtons();
        }
        void OnViewerMouseUp(object sender, MouseEventArgs e) {
            selectionInProgress = false;
            pdfViewer.Invalidate();
            UpdateButtons();
        }
        void CreatePermanentRedactAnnotation() {
            PdfPageFacade page = pdfViewer.GetDocumentFacade().Pages[0];
            propertyGridControl.SelectedObject = new PdfRedactAnnotationProperties();
        }
        void ProcessRedactAnnotation() {
            PdfPageFacade page = pdfViewer.GetDocumentFacade().Pages[startPos.PageNumber - 1];
            PdfRedactAnnotationFacade actualRedactAnnotation = page.AddRedactAnnotation(new PdfRectangle(Math.Min(startPos.Point.X, endPos.Point.X), Math.Min(startPos.Point.Y, endPos.Point.Y), Math.Max(startPos.Point.X, endPos.Point.X), Math.Max(startPos.Point.Y, endPos.Point.Y)));

            PdfRedactAnnotationProperties properties = propertyGridControl.SelectedObject as PdfRedactAnnotationProperties;
            actualRedactAnnotation.FillColor = ConvertToRGBColor(properties.FillColor);
            actualRedactAnnotation.FontColor = ConvertToRGBColor(properties.FontColor);
            actualRedactAnnotation.FontBold = properties.FontBold;
            actualRedactAnnotation.FontItalic = properties.FontItalic;
            actualRedactAnnotation.FontName = properties.FontName;
            actualRedactAnnotation.FontSize = properties.FontSize;
            actualRedactAnnotation.OverlayText = properties.OverlayText;
            actualRedactAnnotation.RepeatText = properties.RepeatText;
            actualRedactAnnotation.TextJustification = properties.TextJustification;
            actualRedactAnnotation.Apply(GetClearContentOptions());
        }
        static PdfRGBColor ConvertToRGBColor(Color color) {
            return new PdfRGBColor(
                color.R / (double)255,
                color.G / (double)255,
                color.B / (double)255);
        }
        PdfClearContentOptions GetClearContentOptions() {
            return new PdfClearContentOptions() {
                ClearAnnotations = !checkAnnotationsOpt.Checked,
                ClearGraphics = !checkGraphicsOpt.Checked,
                ClearImages = !checkImagesOpt.Checked,
                ClearText = !checkTextOpt.Checked
            };
        }
        void OnButtonClearContentClick(object sender, EventArgs e) {
            if(startPos != null && endPos != null) {
                int pageIndex = startPos.PageNumber - 1;
                PdfRectangle rect = new PdfRectangle(Math.Min(startPos.Point.X, endPos.Point.X), Math.Min(startPos.Point.Y, endPos.Point.Y), Math.Max(startPos.Point.X, endPos.Point.X), Math.Max(startPos.Point.Y, endPos.Point.Y));
                startPos = null;
                endPos = null;
                PdfClearContentRegions regions = new PdfClearContentRegions();
                regions.Add(rect);
                pdfViewer.GetDocumentFacade().Pages[pageIndex].ClearContent(regions, GetClearContentOptions());
                UpdateButtons();
            }
        }
        void OnButtonRedactAnnotationClick(object sender, EventArgs e) {
            if(startPos != null && endPos != null)
                ProcessRedactAnnotation();
            UpdateButtons();
        }
        void OnButtonOpenClick(object sender, EventArgs e) {
            if(pdfViewer.LoadDocument()) {
                pdfViewer.ZoomMode = XtraPdfViewer.PdfZoomMode.FitToWidth;
                saveAsBtn.Enabled = pdfViewer.IsDocumentOpened;
                startPos = null;
                endPos = null;
                UpdateButtons();
            }
        }
        void OnSaveButtonClick(object sender, EventArgs e) {
            pdfViewer.SaveDocument();
        }
        protected override void Dispose(bool disposing) {
            if(disposing) {
                selectionInProgresPen.Dispose();
                if(components != null)
                    components.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
