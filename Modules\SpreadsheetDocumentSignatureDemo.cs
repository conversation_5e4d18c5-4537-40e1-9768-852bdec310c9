﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using DevExpress.Office.DigitalSignatures;
using DevExpress.Spreadsheet;
using DevExpress.XtraEditors;
using DevExpress.XtraSpreadsheet.Localization;

namespace DevExpress.Docs.Demos {
    public partial class SpreadsheetDocumentSignatureDemo : SignatureTutorialControl {
        public SpreadsheetDocumentSignatureDemo() {
            InitializeComponent();
            spreadsheetPreview.CanShowBorders = true;
            PopulateHashAlgorithms(hashAlgorithmComboBox);
            PopulateCertificates(panelOptions, lbCerts);
            PopulateCommitmentTypes(edtReason);
            LoadDocument();
        }
        public override bool NoGap {
            get { return true; }
        }
        void LoadDocument() {
            try {
                Workbook workbook = new Workbook();
                workbook.LoadDocument(DemoUtils.GetRelativePath("AnnualSalesReport.xlsx"));
                workbook.Worksheets[0].PrintOptions.FitToPage = true;
                spreadsheetPreview.Workbook = workbook;
                spreadsheetPreview.UpdatePreview();
            }
            catch(Exception ex) {
                XtraMessageBox.Show(ex.Message, Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        void OnButtonSignClick(object sender, EventArgs e) {
            if(!IsSigningPossible()) {
                XtraMessageBox.Show("Before you sign a document, you must save it in XLSX or XLS format.", "Signature", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            try {
                string saveFileName = ShowSignAndSaveFileDialog();
                if(!string.IsNullOrEmpty(saveFileName))
                    SignAndOpen(saveFileName, "AnnualSalesReport.xlsx", CreateSignatureOptions(), CreateSignatureInfo());
            }
            catch(Exception exception) {
                XtraMessageBox.Show(exception.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        SignatureOptions CreateSignatureOptions() {
            var certificate = (CertificateItem)lbCerts.SelectedItem;
            var digestMethod = (HashAlgorithmType)hashAlgorithmComboBox.SelectedItem;
            return CreateSignatureOptions(certificate, tsaURITextEdit.Text, digestMethod);
        }
        SignatureInfo CreateSignatureInfo() {
            SignatureInfo signatureInfo = new SignatureInfo();
            signatureInfo.CommitmentType = (CommitmentType)edtReason.EditValue;
            signatureInfo.Time = DateTime.UtcNow;
            signatureInfo.ClaimedRoles.Clear();
            signatureInfo.ClaimedRoles.Add(edtRole.Text);
            signatureInfo.Country = edtCountry.Text;
            signatureInfo.City = edtCity.Text;
            signatureInfo.StateOrProvince = edtState.Text;
            signatureInfo.Address1 = edtAddress1.Text;
            signatureInfo.Address2 = edtAddress2.Text;
            signatureInfo.PostalCode = edtPostalCode.Text;
            signatureInfo.Comments = edtComments.Text;
            return signatureInfo;
        }
        static readonly Dictionary<DocumentFormat, string> filters = CreateFilterStringTable();
        protected override string CreateFilterString() {
            string filter = string.Empty;
            if(!filters.TryGetValue(DocumentFormat.Xlsx, out filter))
                return string.Empty;
            return filter;
        }
        static Dictionary<DocumentFormat, string> CreateFilterStringTable() {
            Dictionary<DocumentFormat, string> result = new Dictionary<DocumentFormat, string>();
            result.Add(DocumentFormat.Xlsx, XtraSpreadsheetLocalizer.GetString(XtraSpreadsheetStringId.FileFilterDescription_OpenXmlFiles) + " (*.xlsx)|*.xlsx");
            result.Add(DocumentFormat.Xls, XtraSpreadsheetLocalizer.GetString(XtraSpreadsheetStringId.FileFilterDescription_DocFiles) + " (*.xls)|*.xls");
            result.Add(DocumentFormat.Xlsm, XtraSpreadsheetLocalizer.GetString(XtraSpreadsheetStringId.FileFilterDescription_XlsmFiles) + " (*.xlsm)|*.xlsm");
            result.Add(DocumentFormat.Xltx, XtraSpreadsheetLocalizer.GetString(XtraSpreadsheetStringId.FileFilterDescription_XltxFiles) + " (*.xltx)|*.xltx");
            result.Add(DocumentFormat.Xltm, XtraSpreadsheetLocalizer.GetString(XtraSpreadsheetStringId.FileFilterDescription_XltmFiles) + " (*.xltm)|*.xltm");
            result.Add(DocumentFormat.Xlt, XtraSpreadsheetLocalizer.GetString(XtraSpreadsheetStringId.FileFilterDescription_XltFiles) + " (*.xlt)|*.xlt");
            return result;
        }
        protected override string GetFileNameForSaving() {
            string fileName = DemoUtils.GetRelativePath("AnnualSalesReport.xlsx");
            if(string.IsNullOrEmpty(fileName))
                fileName = "Document";
            return System.IO.Path.GetFileNameWithoutExtension(fileName) + "-signed" + System.IO.Path.GetExtension(fileName);
        }
        void OnButtonNewCertificateClick(object sender, EventArgs e) {
            RegisterCertificate(lbCerts);
        }
        static readonly HashSet<DocumentFormat> formatsForSigning = CreateFormatsForSigningTable();
        static HashSet<DocumentFormat> CreateFormatsForSigningTable() {
            HashSet<DocumentFormat> result = new HashSet<DocumentFormat>();
            result.Add(DocumentFormat.Xlsx);
            result.Add(DocumentFormat.Xls);
            result.Add(DocumentFormat.Xlt);
            result.Add(DocumentFormat.Xltx);
            result.Add(DocumentFormat.Xltm);
            result.Add(DocumentFormat.Xlsm);
            result.Add(DocumentFormat.OpenXml);
            return result;
        }
        bool IsSigningPossible() {
            if(lbCerts.Items.Count <= 0)
                return false;
            DocumentFormat format = DocumentFormat.Xlsx;
            return formatsForSigning.Contains(format);
        }
        protected override void Dispose(bool disposing) {
            if(disposing) {
                if(components != null)
                    components.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
