﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="nwindDataSet" targetNamespace="http://tempuri.org/nwindDataSet.xsd" xmlns:mstns="http://tempuri.org/nwindDataSet.xsd" xmlns="http://tempuri.org/nwindDataSet.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="EmployeesTableAdapter" GeneratorDataComponentClassName="EmployeesTableAdapter" Name="Employees" UserDataComponentName="EmployeesTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="nwindConnectionString (Settings)" DbObjectName="Employees" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM `Employees` WHERE ((`EmployeeID` = ?) AND ((? = 1 AND `LastName` IS NULL) OR (`LastName` = ?)) AND ((? = 1 AND `FirstName` IS NULL) OR (`FirstName` = ?)) AND ((? = 1 AND `Title` IS NULL) OR (`Title` = ?)) AND ((? = 1 AND `TitleOfCourtesy` IS NULL) OR (`TitleOfCourtesy` = ?)) AND ((? = 1 AND `BirthDate` IS NULL) OR (`BirthDate` = ?)) AND ((? = 1 AND `HireDate` IS NULL) OR (`HireDate` = ?)) AND ((? = 1 AND `Address` IS NULL) OR (`Address` = ?)) AND ((? = 1 AND `City` IS NULL) OR (`City` = ?)) AND ((? = 1 AND `Region` IS NULL) OR (`Region` = ?)) AND ((? = 1 AND `PostalCode` IS NULL) OR (`PostalCode` = ?)) AND ((? = 1 AND `Country` IS NULL) OR (`Country` = ?)) AND ((? = 1 AND `HomePhone` IS NULL) OR (`HomePhone` = ?)) AND ((? = 1 AND `Extension` IS NULL) OR (`Extension` = ?)) AND ((? = 1 AND `ReportsTo` IS NULL) OR (`ReportsTo` = ?)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_EmployeeID" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="EmployeeID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_LastName" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="LastName" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_LastName" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="LastName" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_FirstName" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="FirstName" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_FirstName" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="FirstName" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Title" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Title" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_Title" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Title" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_TitleOfCourtesy" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="TitleOfCourtesy" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_TitleOfCourtesy" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="TitleOfCourtesy" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_BirthDate" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="BirthDate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="Original_BirthDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="BirthDate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_HireDate" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="HireDate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="Original_HireDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="HireDate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Address" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Address" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_Address" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Address" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_City" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_City" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Region" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Region" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_Region" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Region" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_PostalCode" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="PostalCode" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_PostalCode" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="PostalCode" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Country" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Country" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_Country" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Country" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_HomePhone" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="HomePhone" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_HomePhone" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="HomePhone" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Extension" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Extension" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_Extension" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Extension" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_ReportsTo" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="ReportsTo" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_ReportsTo" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="ReportsTo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO `Employees` (`LastName`, `FirstName`, `Title`, `TitleOfCourtesy`, `BirthDate`, `HireDate`, `Address`, `City`, `Region`, `PostalCode`, `Country`, `HomePhone`, `Extension`, `Photo`, `Notes`, `ReportsTo`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="LastName" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="LastName" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="FirstName" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="FirstName" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Title" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Title" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="TitleOfCourtesy" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="TitleOfCourtesy" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="BirthDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="BirthDate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="HireDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="HireDate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Address" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Address" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="City" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Region" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Region" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="PostalCode" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="PostalCode" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Country" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Country" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="HomePhone" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="HomePhone" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Extension" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Extension" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Binary" Direction="Input" ParameterName="Photo" Precision="0" ProviderType="LongVarBinary" Scale="0" Size="0" SourceColumn="Photo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Notes" Precision="0" ProviderType="LongVarWChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="ReportsTo" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="ReportsTo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>SELECT EmployeeID, LastName, FirstName, Title, TitleOfCourtesy, BirthDate, HireDate, Address, City, Region, PostalCode, Country, HomePhone, Extension, Photo, Notes, ReportsTo FROM Employees</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE `Employees` SET `LastName` = ?, `FirstName` = ?, `Title` = ?, `TitleOfCourtesy` = ?, `BirthDate` = ?, `HireDate` = ?, `Address` = ?, `City` = ?, `Region` = ?, `PostalCode` = ?, `Country` = ?, `HomePhone` = ?, `Extension` = ?, `Photo` = ?, `Notes` = ?, `ReportsTo` = ? WHERE ((`EmployeeID` = ?) AND ((? = 1 AND `LastName` IS NULL) OR (`LastName` = ?)) AND ((? = 1 AND `FirstName` IS NULL) OR (`FirstName` = ?)) AND ((? = 1 AND `Title` IS NULL) OR (`Title` = ?)) AND ((? = 1 AND `TitleOfCourtesy` IS NULL) OR (`TitleOfCourtesy` = ?)) AND ((? = 1 AND `BirthDate` IS NULL) OR (`BirthDate` = ?)) AND ((? = 1 AND `HireDate` IS NULL) OR (`HireDate` = ?)) AND ((? = 1 AND `Address` IS NULL) OR (`Address` = ?)) AND ((? = 1 AND `City` IS NULL) OR (`City` = ?)) AND ((? = 1 AND `Region` IS NULL) OR (`Region` = ?)) AND ((? = 1 AND `PostalCode` IS NULL) OR (`PostalCode` = ?)) AND ((? = 1 AND `Country` IS NULL) OR (`Country` = ?)) AND ((? = 1 AND `HomePhone` IS NULL) OR (`HomePhone` = ?)) AND ((? = 1 AND `Extension` IS NULL) OR (`Extension` = ?)) AND ((? = 1 AND `ReportsTo` IS NULL) OR (`ReportsTo` = ?)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="LastName" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="LastName" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="FirstName" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="FirstName" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Title" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Title" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="TitleOfCourtesy" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="TitleOfCourtesy" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="BirthDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="BirthDate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="HireDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="HireDate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Address" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Address" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="City" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Region" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Region" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="PostalCode" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="PostalCode" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Country" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Country" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="HomePhone" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="HomePhone" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Extension" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Extension" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Binary" Direction="Input" ParameterName="Photo" Precision="0" ProviderType="LongVarBinary" Scale="0" Size="0" SourceColumn="Photo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Notes" Precision="0" ProviderType="LongVarWChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="ReportsTo" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="ReportsTo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_EmployeeID" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="EmployeeID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_LastName" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="LastName" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_LastName" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="LastName" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_FirstName" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="FirstName" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_FirstName" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="FirstName" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Title" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Title" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_Title" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Title" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_TitleOfCourtesy" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="TitleOfCourtesy" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_TitleOfCourtesy" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="TitleOfCourtesy" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_BirthDate" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="BirthDate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="Original_BirthDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="BirthDate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_HireDate" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="HireDate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="Original_HireDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="HireDate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Address" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Address" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_Address" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Address" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_City" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_City" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Region" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Region" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_Region" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Region" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_PostalCode" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="PostalCode" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_PostalCode" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="PostalCode" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Country" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Country" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_Country" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Country" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_HomePhone" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="HomePhone" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_HomePhone" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="HomePhone" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Extension" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Extension" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_Extension" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Extension" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_ReportsTo" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="ReportsTo" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_ReportsTo" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="ReportsTo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="EmployeeID" DataSetColumn="EmployeeID" />
              <Mapping SourceColumn="LastName" DataSetColumn="LastName" />
              <Mapping SourceColumn="FirstName" DataSetColumn="FirstName" />
              <Mapping SourceColumn="Title" DataSetColumn="Title" />
              <Mapping SourceColumn="TitleOfCourtesy" DataSetColumn="TitleOfCourtesy" />
              <Mapping SourceColumn="BirthDate" DataSetColumn="BirthDate" />
              <Mapping SourceColumn="HireDate" DataSetColumn="HireDate" />
              <Mapping SourceColumn="Address" DataSetColumn="Address" />
              <Mapping SourceColumn="City" DataSetColumn="City" />
              <Mapping SourceColumn="Region" DataSetColumn="Region" />
              <Mapping SourceColumn="PostalCode" DataSetColumn="PostalCode" />
              <Mapping SourceColumn="Country" DataSetColumn="Country" />
              <Mapping SourceColumn="HomePhone" DataSetColumn="HomePhone" />
              <Mapping SourceColumn="Extension" DataSetColumn="Extension" />
              <Mapping SourceColumn="Photo" DataSetColumn="Photo" />
              <Mapping SourceColumn="Notes" DataSetColumn="Notes" />
              <Mapping SourceColumn="ReportsTo" DataSetColumn="ReportsTo" />
            </Mappings>
            <Sources>
            </Sources>
          </TableAdapter>
        </Tables>
        <Sources>
        </Sources>
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="nwindDataSet" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="nwindDataSet" msprop:Generator_DataSetName="nwindDataSet">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="Employees" msprop:Generator_UserTableName="Employees" msprop:Generator_RowDeletedName="EmployeesRowDeleted" msprop:Generator_TableClassName="EmployeesDataTable" msprop:Generator_RowChangedName="EmployeesRowChanged" msprop:Generator_RowClassName="EmployeesRow" msprop:Generator_RowChangingName="EmployeesRowChanging" msprop:Generator_RowEvArgName="EmployeesRowChangeEvent" msprop:Generator_RowEvHandlerName="EmployeesRowChangeEventHandler" msprop:Generator_TablePropName="Employees" msprop:Generator_TableVarName="tableEmployees" msprop:Generator_RowDeletingName="EmployeesRowDeleting">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="EmployeeID" msdata:AutoIncrement="true" msprop:Generator_UserColumnName="EmployeeID" msprop:Generator_ColumnPropNameInRow="EmployeeID" msprop:Generator_ColumnVarNameInTable="columnEmployeeID" msprop:Generator_ColumnPropNameInTable="EmployeeIDColumn" type="xs:int" />
              <xs:element name="LastName" msprop:Generator_UserColumnName="LastName" msprop:Generator_ColumnPropNameInRow="LastName" msprop:Generator_ColumnVarNameInTable="columnLastName" msprop:Generator_ColumnPropNameInTable="LastNameColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="FirstName" msprop:Generator_UserColumnName="FirstName" msprop:Generator_ColumnPropNameInRow="FirstName" msprop:Generator_ColumnVarNameInTable="columnFirstName" msprop:Generator_ColumnPropNameInTable="FirstNameColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Title" msprop:Generator_UserColumnName="Title" msprop:Generator_ColumnPropNameInRow="Title" msprop:Generator_ColumnVarNameInTable="columnTitle" msprop:Generator_ColumnPropNameInTable="TitleColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="TitleOfCourtesy" msprop:Generator_UserColumnName="TitleOfCourtesy" msprop:Generator_ColumnPropNameInRow="TitleOfCourtesy" msprop:Generator_ColumnVarNameInTable="columnTitleOfCourtesy" msprop:Generator_ColumnPropNameInTable="TitleOfCourtesyColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="25" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="BirthDate" msprop:Generator_UserColumnName="BirthDate" msprop:Generator_ColumnPropNameInRow="BirthDate" msprop:Generator_ColumnVarNameInTable="columnBirthDate" msprop:Generator_ColumnPropNameInTable="BirthDateColumn" type="xs:dateTime" minOccurs="0" />
              <xs:element name="HireDate" msprop:Generator_UserColumnName="HireDate" msprop:Generator_ColumnPropNameInRow="HireDate" msprop:Generator_ColumnVarNameInTable="columnHireDate" msprop:Generator_ColumnPropNameInTable="HireDateColumn" type="xs:dateTime" minOccurs="0" />
              <xs:element name="Address" msprop:Generator_UserColumnName="Address" msprop:Generator_ColumnPropNameInRow="Address" msprop:Generator_ColumnVarNameInTable="columnAddress" msprop:Generator_ColumnPropNameInTable="AddressColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="60" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="City" msprop:Generator_UserColumnName="City" msprop:Generator_ColumnPropNameInRow="City" msprop:Generator_ColumnVarNameInTable="columnCity" msprop:Generator_ColumnPropNameInTable="CityColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="15" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Region" msprop:Generator_UserColumnName="Region" msprop:Generator_ColumnPropNameInRow="Region" msprop:Generator_ColumnVarNameInTable="columnRegion" msprop:Generator_ColumnPropNameInTable="RegionColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="15" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="PostalCode" msprop:Generator_UserColumnName="PostalCode" msprop:Generator_ColumnPropNameInRow="PostalCode" msprop:Generator_ColumnVarNameInTable="columnPostalCode" msprop:Generator_ColumnPropNameInTable="PostalCodeColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Country" msprop:Generator_UserColumnName="Country" msprop:Generator_ColumnPropNameInRow="Country" msprop:Generator_ColumnVarNameInTable="columnCountry" msprop:Generator_ColumnPropNameInTable="CountryColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="15" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="HomePhone" msprop:Generator_UserColumnName="HomePhone" msprop:Generator_ColumnPropNameInRow="HomePhone" msprop:Generator_ColumnVarNameInTable="columnHomePhone" msprop:Generator_ColumnPropNameInTable="HomePhoneColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="24" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Extension" msprop:Generator_UserColumnName="Extension" msprop:Generator_ColumnPropNameInRow="Extension" msprop:Generator_ColumnVarNameInTable="columnExtension" msprop:Generator_ColumnPropNameInTable="ExtensionColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="4" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Photo" msprop:Generator_UserColumnName="Photo" msprop:Generator_ColumnPropNameInRow="Photo" msprop:Generator_ColumnVarNameInTable="columnPhoto" msprop:Generator_ColumnPropNameInTable="PhotoColumn" type="xs:base64Binary" minOccurs="0" />
              <xs:element name="Notes" msprop:Generator_UserColumnName="Notes" msprop:Generator_ColumnPropNameInRow="Notes" msprop:Generator_ColumnVarNameInTable="columnNotes" msprop:Generator_ColumnPropNameInTable="NotesColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="536870910" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ReportsTo" msprop:Generator_UserColumnName="ReportsTo" msprop:Generator_ColumnPropNameInRow="ReportsTo" msprop:Generator_ColumnVarNameInTable="columnReportsTo" msprop:Generator_ColumnPropNameInTable="ReportsToColumn" type="xs:int" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Employees" />
      <xs:field xpath="mstns:EmployeeID" />
    </xs:unique>
  </xs:element>
</xs:schema>