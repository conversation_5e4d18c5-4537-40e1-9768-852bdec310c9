﻿using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using DevExpress.Pdf;
using DevExpress.XtraPdfViewer;

namespace DevExpress.Docs.Demos {
    public class PagePathContent : PageContentData {
        float fWidth;
        Color fBorderColor;
        Color fFillColor;
        bool fClosed;

        public float Width {
            get { return fWidth; }
            set {
                fWidth = value;
                Controller.UpdateDocument();
            }
        }
        public bool Closed {
            get { return fClosed; }
            set {
                fClosed = value;
                Controller.UpdateDocument();
            }
        }

        [TypeConverter(typeof(ColorConverter))]
        public Color BorderColor {
            get { return fBorderColor; }
            set {
                fBorderColor = value;
                Controller.UpdateDocument();
            }
        }

        [TypeConverter(typeof(ColorConverter))]
        public Color FillColor {
            get { return fFillColor; }
            set {
                fFillColor = value;
                Controller.UpdateDocument();
            }
        }

        internal List<PdfPoint> Points { get; }

        public void UpdateRectangle() {
            double left = 1e9, right = -1e9, top = -1e9, bottom = 1e9;
            foreach(PdfPoint p in Points) {
                if(p.X < left) left = p.X;
                if(p.X > right) right = p.X;
                if(p.Y > top) top = p.Y;
                if(p.Y < bottom) bottom = p.Y;
            }
            Rectangle.InnerRectangle = new PdfRectangle(left, bottom, right, top);
        }
        public override PdfPageDragItem CreateDragItem(PdfViewer pdfViewer) {
            return new PdfPathDragItem(pdfViewer, this, Controller);
        }
        public PagePathContent(PdfDocumentPosition position, PageContentController controller) : base(position, controller) {
            Width = 5;
            BorderColor = Color.Green;
            FillColor = Color.AliceBlue;
            Points = new List<PdfPoint>() { Rectangle.InnerRectangle.BottomLeft, Rectangle.InnerRectangle.TopRight };
        }
        public override void FillContent(PdfGraphics graphics, PdfRectangle pageCropBox) {
            RectangleF rect = new RectangleF(Rectangle.Left, (float)(pageCropBox.Height - Rectangle.Top), (float)Rectangle.InnerRectangle.Width, (float)Rectangle.InnerRectangle.Height);
            List<PointF> points = new List<PointF>();
            foreach(PdfPoint p in Points)
                points.Add(new PointF((float)p.X, (float)(pageCropBox.Height - p.Y)));
            if(Closed) {
                points.Add(points[0]);
                graphics.FillPolygon(new SolidBrush(FillColor), points.ToArray());
            }
            graphics.DrawLines(new Pen(BorderColor, Width), points.ToArray());
        }
    }
}
