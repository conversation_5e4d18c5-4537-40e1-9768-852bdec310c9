{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\DevExpress Demos 25.1\\Components\\Office File API\\CS\\OfficeFileAPIDemos\\OfficeFileAPIDemos.NetCore.Desktop.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\DevExpress Demos 25.1\\Components\\Office File API\\CS\\OfficeFileAPIDemos\\OfficeFileAPIDemos.NetCore.Desktop.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\DevExpress Demos 25.1\\Components\\Office File API\\CS\\OfficeFileAPIDemos\\OfficeFileAPIDemos.NetCore.Desktop.csproj", "projectName": "OfficeFileAPIDemos", "projectPath": "C:\\Users\\<USER>\\Documents\\DevExpress Demos 25.1\\Components\\Office File API\\CS\\OfficeFileAPIDemos\\OfficeFileAPIDemos.NetCore.Desktop.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\DevExpress Demos 25.1\\Components\\Office File API\\CS\\OfficeFileAPIDemos\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 25.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 25.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"DevExpress.Document.Processor": {"target": "Package", "version": "[25.1.3, )"}, "DevExpress.Win.BonusSkins": {"target": "Package", "version": "[25.1.3, )"}, "DevExpress.Win.Design": {"target": "Package", "version": "[25.1.3, )"}, "System.Data.OleDb": {"target": "Package", "version": "[8.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}