﻿using System;
using DevExpress.XtraEditors;

namespace DevExpress.Docs.Demos {
    public partial class WordRTFFindAndReplace {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing) {
            if (disposing && (components != null)) {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code
        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        /// 
        private void InitializeComponent() {
            this.printPreviewControl = new DevExpress.XtraPrinting.Control.PrintControl();
            this.sidePanel1 = new DevExpress.XtraEditors.SidePanel();
            this.btnExport = new DevExpress.XtraEditors.SimpleButton();
            this.replaceButton = new DevExpress.XtraEditors.SimpleButton();
            this.findButton = new DevExpress.XtraEditors.SimpleButton();
            this.replace = new DevExpress.XtraEditors.LabelControl();
            this.replaceTE = new DevExpress.XtraEditors.TextEdit();
            this.find = new DevExpress.XtraEditors.LabelControl();
            this.findTE = new DevExpress.XtraEditors.TextEdit();
            this.sidePanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.replaceTE.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.findTE.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // printPreviewControl
            // 
            this.printPreviewControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.printPreviewControl.IsMetric = false;
            this.printPreviewControl.Location = new System.Drawing.Point(0, 81);
            this.printPreviewControl.Margin = new System.Windows.Forms.Padding(0);
            this.printPreviewControl.Name = "printPreviewControl";
            this.printPreviewControl.Size = new System.Drawing.Size(784, 351);
            this.printPreviewControl.TabIndex = 0;
            // 
            // sidePanel1
            // 
            this.sidePanel1.AllowResize = false;
            this.sidePanel1.Controls.Add(this.btnExport);
            this.sidePanel1.Controls.Add(this.replaceButton);
            this.sidePanel1.Controls.Add(this.findButton);
            this.sidePanel1.Controls.Add(this.replace);
            this.sidePanel1.Controls.Add(this.replaceTE);
            this.sidePanel1.Controls.Add(this.find);
            this.sidePanel1.Controls.Add(this.findTE);
            this.sidePanel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.sidePanel1.Location = new System.Drawing.Point(0, 0);
            this.sidePanel1.Name = "sidePanel1";
            this.sidePanel1.Size = new System.Drawing.Size(784, 81);
            this.sidePanel1.TabIndex = 1;
            this.sidePanel1.Text = "sidePanel1";
            // 
            // btnExport
            // 
            this.btnExport.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnExport.Location = new System.Drawing.Point(678, 43);
            this.btnExport.Name = "btnExport";
            this.btnExport.Size = new System.Drawing.Size(89, 24);
            this.btnExport.TabIndex = 6;
            this.btnExport.Text = "Export";
            this.btnExport.Click += new System.EventHandler(this.btnExport_Click);
            // 
            // replaceButton
            // 
            this.replaceButton.Location = new System.Drawing.Point(222, 43);
            this.replaceButton.Name = "replaceButton";
            this.replaceButton.Size = new System.Drawing.Size(85, 24);
            this.replaceButton.TabIndex = 5;
            this.replaceButton.Text = "Replace All";
            this.replaceButton.Click += new System.EventHandler(this.replaceButton_Click);
            // 
            // findButton
            // 
            this.findButton.Location = new System.Drawing.Point(222, 14);
            this.findButton.Name = "findButton";
            this.findButton.Size = new System.Drawing.Size(85, 24);
            this.findButton.TabIndex = 3;
            this.findButton.Text = "Find All";
            this.findButton.Click += new System.EventHandler(this.findButton_Click);
            // 
            // replace
            // 
            this.replace.Location = new System.Drawing.Point(17, 48);
            this.replace.Name = "replace";
            this.replace.Size = new System.Drawing.Size(65, 13);
            this.replace.TabIndex = 51;
            this.replace.Text = "Replace with:";
            // 
            // replaceTE
            // 
            this.replaceTE.EditValue = "";
            this.replaceTE.Location = new System.Drawing.Point(91, 45);
            this.replaceTE.Name = "replaceTE";
            this.replaceTE.Size = new System.Drawing.Size(125, 20);
            this.replaceTE.TabIndex = 4;
            // 
            // find
            // 
            this.find.Location = new System.Drawing.Point(17, 19);
            this.find.Name = "find";
            this.find.Size = new System.Drawing.Size(24, 13);
            this.find.TabIndex = 49;
            this.find.Text = "Find:";
            // 
            // findTE
            // 
            this.findTE.EditValue = "";
            this.findTE.Location = new System.Drawing.Point(91, 16);
            this.findTE.Name = "findTE";
            this.findTE.Size = new System.Drawing.Size(126, 20);
            this.findTE.TabIndex = 2;
            // 
            // WordRTFFindAndReplace
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.printPreviewControl);
            this.Controls.Add(this.sidePanel1);
            this.Margin = new System.Windows.Forms.Padding(0);
            this.Name = "WordRTFFindAndReplace";
            this.sidePanel1.ResumeLayout(false);
            this.sidePanel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.replaceTE.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.findTE.Properties)).EndInit();
            this.ResumeLayout(false);

        }
        #endregion
        private XtraPrinting.Control.PrintControl printPreviewControl;
        private SidePanel sidePanel1;
        private SimpleButton btnExport;
        private SimpleButton replaceButton;
        private SimpleButton findButton;
        private LabelControl replace;
        private TextEdit replaceTE;
        private LabelControl find;
        private TextEdit findTE;
    }
}
