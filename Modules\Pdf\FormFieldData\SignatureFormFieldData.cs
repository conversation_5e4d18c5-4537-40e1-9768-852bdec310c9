﻿using System.ComponentModel;
using System.ComponentModel.Design;
using System.Drawing.Design;
using DevExpress.Pdf;

namespace DevExpress.Docs.Demos {
    public class SignatureFormFieldData : FormFieldData {
        string fText;
        PdfAcroFormStringAlignment fLineAlignment;

        [Category(AppearanceCategory), Editor(typeof(MultilineStringEditor), typeof(UITypeEditor))]
        public string Text {
            get { return fText; }
            set {
                fText = value;
                UpdateModel();
            }
        }

        [Category(AppearanceCategory)]
        public PdfAcroFormStringAlignment LineAlignment {
            get { return fLineAlignment; }
            set {
                fLineAlignment = value;
                UpdateModel();
            }
        }

        public SignatureFormFieldData(PdfDocumentPosition position, DocumentFormController controller)
            : base(position, controller) {
        }
        protected override PdfAcroFormCommonVisualField CreateVisualFormField() {
            return new PdfAcroFormSignatureField(Name, PageNumber, Rectangle.InnerRectangle) { Text = Text, LineAlignment = LineAlignment };
        }
    }
}
