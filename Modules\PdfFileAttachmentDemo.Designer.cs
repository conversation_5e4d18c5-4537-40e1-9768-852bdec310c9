﻿namespace DevExpress.Docs.Demos
{
    partial class PdfFileAttachmentDemo
	{
		/// <summary> 
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		#region Component Designer generated code

		/// <summary> 
		/// Required method for Designer support - do not modify 
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
            this.pdfViewer = new DevExpress.XtraPdfViewer.PdfViewer();
            this.deleteButton = new DevExpress.XtraEditors.SimpleButton();
            this.buttonAttachFile = new DevExpress.XtraEditors.SimpleButton();
            this.lbFileAttachments = new DevExpress.XtraEditors.ListBoxControl();
            this.labelFileAttachments = new DevExpress.XtraEditors.LabelControl();
            this.sidePanel = new DevExpress.XtraEditors.SidePanel();
            this.sidePanelTableLayout = new DevExpress.Utils.Layout.TablePanel();
            ((System.ComponentModel.ISupportInitialize)(this.lbFileAttachments)).BeginInit();
            this.sidePanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.sidePanelTableLayout)).BeginInit();
            this.sidePanelTableLayout.SuspendLayout();
            this.SuspendLayout();
            // 
            // pdfViewer
            // 
            this.pdfViewer.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pdfViewer.Location = new System.Drawing.Point(0, 0);
            this.pdfViewer.Name = "pdfViewer";
            this.pdfViewer.NavigationPaneInitialVisibility = DevExpress.XtraPdfViewer.PdfNavigationPaneVisibility.Collapsed;
            this.pdfViewer.NavigationPaneWidth = 300;
            this.pdfViewer.ReadOnly = true;
            this.pdfViewer.Size = new System.Drawing.Size(512, 564);
            this.pdfViewer.TabIndex = 6;
            this.pdfViewer.TabStop = false;
            this.pdfViewer.ZoomMode = DevExpress.XtraPdfViewer.PdfZoomMode.FitToWidth;
            // 
            // deleteButton
            // 
            this.sidePanelTableLayout.SetColumn(this.deleteButton, 0);
            this.deleteButton.Dock = System.Windows.Forms.DockStyle.Fill;
            this.deleteButton.Location = new System.Drawing.Point(15, 282);
            this.deleteButton.Margin = new System.Windows.Forms.Padding(3, 12, 3, 3);
            this.deleteButton.Name = "deleteButton";
            this.sidePanelTableLayout.SetRow(this.deleteButton, 3);
            this.deleteButton.Size = new System.Drawing.Size(194, 23);
            this.deleteButton.TabIndex = 26;
            this.deleteButton.Text = "Delete Selected File";
            this.deleteButton.Click += new System.EventHandler(this.OnDeleteFile);
            // 
            // buttonAttachFile
            // 
            this.sidePanelTableLayout.SetColumn(this.buttonAttachFile, 0);
            this.buttonAttachFile.Dock = System.Windows.Forms.DockStyle.Fill;
            this.buttonAttachFile.Location = new System.Drawing.Point(15, 15);
            this.buttonAttachFile.Name = "buttonAttachFile";
            this.sidePanelTableLayout.SetRow(this.buttonAttachFile, 0);
            this.buttonAttachFile.Size = new System.Drawing.Size(194, 23);
            this.buttonAttachFile.TabIndex = 1;
            this.buttonAttachFile.Text = "Attach File...";
            this.buttonAttachFile.Click += new System.EventHandler(this.OnAttachFile);
            // 
            // lbFileAttachments
            // 
            this.sidePanelTableLayout.SetColumn(this.lbFileAttachments, 0);
            this.lbFileAttachments.DisplayMember = "FileName";
            this.lbFileAttachments.ItemAutoHeight = true;
            this.lbFileAttachments.Location = new System.Drawing.Point(15, 72);
            this.lbFileAttachments.Name = "lbFileAttachments";
            this.sidePanelTableLayout.SetRow(this.lbFileAttachments, 2);
            this.lbFileAttachments.Size = new System.Drawing.Size(194, 195);
            this.lbFileAttachments.TabIndex = 0;
            // 
            // labelFileAttachments
            // 
            this.sidePanelTableLayout.SetColumn(this.labelFileAttachments, 0);
            this.labelFileAttachments.Location = new System.Drawing.Point(15, 53);
            this.labelFileAttachments.Margin = new System.Windows.Forms.Padding(3, 12, 3, 3);
            this.labelFileAttachments.Name = "labelFileAttachments";
            this.sidePanelTableLayout.SetRow(this.labelFileAttachments, 1);
            this.labelFileAttachments.Size = new System.Drawing.Size(84, 13);
            this.labelFileAttachments.TabIndex = 25;
            this.labelFileAttachments.Text = "File Attachments:";
            // 
            // sidePanel
            // 
            this.sidePanel.Controls.Add(this.sidePanelTableLayout);
            this.sidePanel.Dock = System.Windows.Forms.DockStyle.Right;
            this.sidePanel.Location = new System.Drawing.Point(512, 0);
            this.sidePanel.MinimumSize = new System.Drawing.Size(200, 0);
            this.sidePanel.Name = "sidePanel";
            this.sidePanel.Size = new System.Drawing.Size(225, 564);
            this.sidePanel.TabIndex = 0;
            this.sidePanel.Text = "sidePanel1";
            // 
            // sidePanelTableLayout
            // 
            this.sidePanelTableLayout.Columns.AddRange(new DevExpress.Utils.Layout.TablePanelColumn[] {
            new DevExpress.Utils.Layout.TablePanelColumn(DevExpress.Utils.Layout.TablePanelEntityStyle.Relative, 5F)});
            this.sidePanelTableLayout.Controls.Add(this.deleteButton);
            this.sidePanelTableLayout.Controls.Add(this.buttonAttachFile);
            this.sidePanelTableLayout.Controls.Add(this.lbFileAttachments);
            this.sidePanelTableLayout.Controls.Add(this.labelFileAttachments);
            this.sidePanelTableLayout.Dock = System.Windows.Forms.DockStyle.Fill;
            this.sidePanelTableLayout.Location = new System.Drawing.Point(1, 0);
            this.sidePanelTableLayout.Name = "sidePanelTableLayout";
            this.sidePanelTableLayout.Padding = new System.Windows.Forms.Padding(12);
            this.sidePanelTableLayout.Rows.AddRange(new DevExpress.Utils.Layout.TablePanelRow[] {
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.AutoSize, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.AutoSize, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.AutoSize, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.AutoSize, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.AutoSize, 26F)});
            this.sidePanelTableLayout.Size = new System.Drawing.Size(224, 564);
            this.sidePanelTableLayout.TabIndex = 27;
            // 
            // PdfFileAttachmentDemo
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.pdfViewer);
            this.Controls.Add(this.sidePanel);
            this.Name = "PdfFileAttachmentDemo";
            this.Size = new System.Drawing.Size(737, 564);
            ((System.ComponentModel.ISupportInitialize)(this.lbFileAttachments)).EndInit();
            this.sidePanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.sidePanelTableLayout)).EndInit();
            this.sidePanelTableLayout.ResumeLayout(false);
            this.sidePanelTableLayout.PerformLayout();
            this.ResumeLayout(false);

		}

		#endregion
        private XtraPdfViewer.PdfViewer pdfViewer;
        private XtraEditors.LabelControl labelFileAttachments;
        private XtraEditors.ListBoxControl lbFileAttachments;
        private XtraEditors.SimpleButton buttonAttachFile;
        private XtraEditors.SimpleButton deleteButton;
        private Utils.Layout.TablePanel sidePanelTableLayout;
        private XtraEditors.SidePanel sidePanel;
    }
}
