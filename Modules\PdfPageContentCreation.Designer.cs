﻿using System.IO;
namespace DevExpress.Docs.Demos {
    partial class PdfPageContentCreation {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing) {
            if (disposing) {
                UnsubscribeFromEvents();
                if (pdfViewer != null)
                    pdfViewer.CloseDocument();
                DemoUtils.DeleteTempFile(sourceTempFilePath);
                DemoUtils.DeleteTempFile(editingTempFilePath);
                if (components != null)
                    components.Dispose();
                if (controller != null)
                    controller.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent() {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(PdfPageContentCreation));
            DevExpress.Utils.SuperToolTip superToolTip5 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipTitleItem toolTipTitleItem5 = new DevExpress.Utils.ToolTipTitleItem();
            DevExpress.Utils.SuperToolTip superToolTip6 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipTitleItem toolTipTitleItem6 = new DevExpress.Utils.ToolTipTitleItem();
            this.accordionContentContainer1 = new DevExpress.XtraBars.Navigation.AccordionContentContainer();
            this.accordionControlElement2 = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.pdfViewer = new DevExpress.XtraPdfViewer.PdfViewer();
            this.sidePanel = new DevExpress.XtraEditors.SidePanel();
            this.accordionControl = new DevExpress.XtraBars.Navigation.AccordionControl();
            this.accordionControlFileElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlNewElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlOpenElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlSaveElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlAddPageElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlFormFieldsElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlTextElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlImageElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlShapeElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlPathElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlLayoutElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlBringToFrontElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlSendToBackElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlBringForwardElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.accordionControlSendBackwardElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
            this.propertyGridControl = new DevExpress.XtraVerticalGrid.PropertyGridControl();
            this.layoutControl1 = new DevExpress.XtraLayout.LayoutControl();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.accordionLayoutItem = new DevExpress.XtraLayout.LayoutControlItem();
            this.splitter = new DevExpress.XtraLayout.SplitterItem();
            this.propertyGridGroup = new DevExpress.XtraEditors.GroupControl();
            this.propertyGridLayoutItem = new DevExpress.XtraLayout.LayoutControlItem();
            this.sidePanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.accordionControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.propertyGridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl1)).BeginInit();
            this.layoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.accordionLayoutItem)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitter)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.propertyGridGroup)).BeginInit();
            this.propertyGridGroup.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.propertyGridLayoutItem)).BeginInit();
            this.SuspendLayout();
            // 
            // accordionContentContainer1
            // 
            this.accordionContentContainer1.Appearance.BackColor = System.Drawing.SystemColors.Control;
            this.accordionContentContainer1.Appearance.Options.UseBackColor = true;
            this.accordionContentContainer1.Name = "accordionContentContainer1";
            this.accordionContentContainer1.Size = new System.Drawing.Size(216, 76);
            this.accordionContentContainer1.TabIndex = 1;
            // 
            // accordionControlElement2
            // 
            this.accordionControlElement2.ContentContainer = this.accordionContentContainer1;
            this.accordionControlElement2.Name = "accordionControlElement2";
            this.accordionControlElement2.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlElement2.Text = "Element2";
            // 
            // pdfViewer
            // 
            this.pdfViewer.AllowDrop = true;
            this.pdfViewer.CursorMode = DevExpress.XtraPdfViewer.PdfCursorMode.Custom;
            this.pdfViewer.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pdfViewer.Location = new System.Drawing.Point(0, 0);
            this.pdfViewer.Name = "pdfViewer";
            this.pdfViewer.NavigationPanePageVisibility = DevExpress.XtraPdfViewer.PdfNavigationPanePageVisibility.None;
            this.pdfViewer.Size = new System.Drawing.Size(423, 564);
            this.pdfViewer.TabIndex = 0;
            this.pdfViewer.ZoomMode = DevExpress.XtraPdfViewer.PdfZoomMode.PageLevel;
            // 
            // sidePanel
            // 
            this.sidePanel.Controls.Add(this.layoutControl1);
            this.sidePanel.Dock = System.Windows.Forms.DockStyle.Right;
            this.sidePanel.Location = new System.Drawing.Point(423, 0);
            this.sidePanel.MinimumSize = new System.Drawing.Size(200, 0);
            this.sidePanel.Name = "sidePanel";
            this.sidePanel.Size = new System.Drawing.Size(314, 564);
            this.sidePanel.TabIndex = 4;
            // 
            // accordionControl
            // 
            this.accordionControl.AllowDrop = true;
            this.accordionControl.AllowItemSelection = true;
            this.accordionControl.Elements.AddRange(new DevExpress.XtraBars.Navigation.AccordionControlElement[] {
            this.accordionControlFileElement,
            this.accordionControlFormFieldsElement,
            this.accordionControlLayoutElement});
            this.accordionControl.Location = new System.Drawing.Point(12, 12);
            this.accordionControl.Name = "accordionControl";
            this.accordionControl.ScrollBarMode = DevExpress.XtraBars.Navigation.ScrollBarMode.Hidden;
            this.accordionControl.Size = new System.Drawing.Size(289, 383);
            this.accordionControl.StyleController = this.layoutControl1;
            this.accordionControl.TabIndex = 1;
            // 
            // accordionControlFileElement
            // 
            this.accordionControlFileElement.Elements.AddRange(new DevExpress.XtraBars.Navigation.AccordionControlElement[] {
            this.accordionControlNewElement,
            this.accordionControlOpenElement,
            this.accordionControlSaveElement,
            this.accordionControlAddPageElement});
            this.accordionControlFileElement.Expanded = true;
            this.accordionControlFileElement.Name = "accordionControlFileElement";
            this.accordionControlFileElement.Text = "File";
            // 
            // accordionControlNewElement
            // 
            this.accordionControlNewElement.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("accordionControlNewElement.ImageOptions.Image")));
            this.accordionControlNewElement.Name = "accordionControlNewElement";
            this.accordionControlNewElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlNewElement.Text = "New";
            this.accordionControlNewElement.Click += new System.EventHandler(this.accordionControlNewElement_Click);
            // 
            // accordionControlOpenElement
            // 
            this.accordionControlOpenElement.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("accordionControlOpenElement.ImageOptions.Image")));
            this.accordionControlOpenElement.Name = "accordionControlOpenElement";
            this.accordionControlOpenElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            toolTipTitleItem5.Text = "Open... (Ctrl+O)";
            superToolTip5.Items.Add(toolTipTitleItem5);
            this.accordionControlOpenElement.SuperTip = superToolTip5;
            this.accordionControlOpenElement.Text = "Open...";
            this.accordionControlOpenElement.Click += new System.EventHandler(this.AccordionControlOpenElementClick);
            // 
            // accordionControlSaveElement
            // 
            this.accordionControlSaveElement.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("accordionControlSaveElement.ImageOptions.Image")));
            this.accordionControlSaveElement.Name = "accordionControlSaveElement";
            this.accordionControlSaveElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            toolTipTitleItem6.Text = "Save as... (Ctrl+S)";
            superToolTip6.Items.Add(toolTipTitleItem6);
            this.accordionControlSaveElement.SuperTip = superToolTip6;
            this.accordionControlSaveElement.Text = "Save As...";
            this.accordionControlSaveElement.Click += new System.EventHandler(this.AccordionControlSaveElementClick);
            // 
            // accordionControlAddPageElement
            // 
            this.accordionControlAddPageElement.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("accordionControlAddPageElement.ImageOptions.Image")));
            this.accordionControlAddPageElement.Name = "accordionControlAddPageElement";
            this.accordionControlAddPageElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlAddPageElement.Text = "Add page";
            this.accordionControlAddPageElement.Click += new System.EventHandler(this.accordionControlAddPageElement_Click);
            // 
            // accordionControlFormFieldsElement
            // 
            this.accordionControlFormFieldsElement.Elements.AddRange(new DevExpress.XtraBars.Navigation.AccordionControlElement[] {
            this.accordionControlTextElement,
            this.accordionControlImageElement,
            this.accordionControlShapeElement,
            this.accordionControlPathElement});
            this.accordionControlFormFieldsElement.Expanded = true;
            this.accordionControlFormFieldsElement.Name = "accordionControlFormFieldsElement";
            this.accordionControlFormFieldsElement.Text = "Add Content";
            // 
            // accordionControlTextElement
            // 
            this.accordionControlTextElement.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("accordionControlTextElement.ImageOptions.Image")));
            this.accordionControlTextElement.Name = "accordionControlTextElement";
            this.accordionControlTextElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlTextElement.Text = "Text";
            // 
            // accordionControlImageElement
            // 
            this.accordionControlImageElement.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("accordionControlImageElement.ImageOptions.Image")));
            this.accordionControlImageElement.Name = "accordionControlImageElement";
            this.accordionControlImageElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlImageElement.Text = "Image";
            // 
            // accordionControlShapeElement
            // 
            this.accordionControlShapeElement.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("accordionControlShapeElement.ImageOptions.Image")));
            this.accordionControlShapeElement.Name = "accordionControlShapeElement";
            this.accordionControlShapeElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlShapeElement.Text = "Shape";
            // 
            // accordionControlPathElement
            // 
            this.accordionControlPathElement.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("accordionControlPathElement.ImageOptions.Image")));
            this.accordionControlPathElement.Name = "accordionControlPathElement";
            this.accordionControlPathElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlPathElement.Text = "Path";
            // 
            // accordionControlLayoutElement
            // 
            this.accordionControlLayoutElement.Elements.AddRange(new DevExpress.XtraBars.Navigation.AccordionControlElement[] {
            this.accordionControlBringToFrontElement,
            this.accordionControlSendToBackElement,
            this.accordionControlBringForwardElement,
            this.accordionControlSendBackwardElement});
            this.accordionControlLayoutElement.Name = "accordionControlLayoutElement";
            this.accordionControlLayoutElement.Text = "Layout";
            this.accordionControlLayoutElement.Visible = false;
            // 
            // accordionControlBringToFrontElement
            // 
            this.accordionControlBringToFrontElement.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("accordionControlBringToFrontElement.ImageOptions.Image")));
            this.accordionControlBringToFrontElement.Name = "accordionControlBringToFrontElement";
            this.accordionControlBringToFrontElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlBringToFrontElement.Text = "Bring To Front";
            this.accordionControlBringToFrontElement.Click += new System.EventHandler(this.AccordionControlBringToFrontElementClick);
            // 
            // accordionControlSendToBackElement
            // 
            this.accordionControlSendToBackElement.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("accordionControlSendToBackElement.ImageOptions.Image")));
            this.accordionControlSendToBackElement.Name = "accordionControlSendToBackElement";
            this.accordionControlSendToBackElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlSendToBackElement.Text = "Send To Back";
            this.accordionControlSendToBackElement.Click += new System.EventHandler(this.AccordionControlSendToBackElementClick);
            // 
            // accordionControlBringForwardElement
            // 
            this.accordionControlBringForwardElement.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("accordionControlBringForwardElement.ImageOptions.Image")));
            this.accordionControlBringForwardElement.Name = "accordionControlBringForwardElement";
            this.accordionControlBringForwardElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlBringForwardElement.Text = "Bring Forward";
            this.accordionControlBringForwardElement.Click += new System.EventHandler(this.accordionControlBringForwardElement_Click);
            // 
            // accordionControlSendBackwardElement
            // 
            this.accordionControlSendBackwardElement.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("accordionControlSendBackwardElement.ImageOptions.Image")));
            this.accordionControlSendBackwardElement.Name = "accordionControlSendBackwardElement";
            this.accordionControlSendBackwardElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
            this.accordionControlSendBackwardElement.Text = "Send Backward";
            this.accordionControlSendBackwardElement.Click += new System.EventHandler(this.accordionControlSendBackwardElement_Click);
            // 
            // propertyGridControl
            // 
            this.propertyGridControl.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.propertyGridControl.Cursor = System.Windows.Forms.Cursors.Default;
            this.propertyGridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.propertyGridControl.Location = new System.Drawing.Point(2, 23);
            this.propertyGridControl.Name = "propertyGridControl";
            this.propertyGridControl.OptionsView.AllowReadOnlyRowAppearance = DevExpress.Utils.DefaultBoolean.True;
            this.propertyGridControl.OptionsView.ShowRootCategories = false;
            this.propertyGridControl.Size = new System.Drawing.Size(285, 118);
            this.propertyGridControl.TabIndex = 2;
            // 
            // layoutControl1
            // 
            this.layoutControl1.Controls.Add(this.propertyGridGroup);
            this.layoutControl1.Controls.Add(this.accordionControl);
            this.layoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.layoutControl1.Location = new System.Drawing.Point(1, 0);
            this.layoutControl1.Name = "layoutControl1";
            this.layoutControl1.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = new System.Drawing.Rectangle(537, 426, 650, 400);
            this.layoutControl1.Root = this.Root;
            this.layoutControl1.Size = new System.Drawing.Size(313, 564);
            this.layoutControl1.TabIndex = 5;
            this.layoutControl1.Text = "layoutControl1";
            // 
            // Root
            // 
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.accordionLayoutItem,
            this.splitter,
            this.propertyGridLayoutItem});
            this.Root.Name = "Root";
            this.Root.Size = new System.Drawing.Size(313, 564);
            this.Root.TextVisible = false;
            // 
            // accordionLayoutItem
            // 
            this.accordionLayoutItem.Control = this.accordionControl;
            this.accordionLayoutItem.Location = new System.Drawing.Point(0, 0);
            this.accordionLayoutItem.Name = "accordionLayoutItem";
            this.accordionLayoutItem.Size = new System.Drawing.Size(293, 387);
            this.accordionLayoutItem.TextSize = new System.Drawing.Size(0, 0);
            this.accordionLayoutItem.TextVisible = false;
            // 
            // splitter
            // 
            this.splitter.AllowHotTrack = true;
            this.splitter.Location = new System.Drawing.Point(0, 387);
            this.splitter.Name = "splitter";
            this.splitter.ShowSplitGlyph = DevExpress.Utils.DefaultBoolean.True;
            this.splitter.Size = new System.Drawing.Size(293, 10);
            // 
            // propertyGridGroup
            // 
            this.propertyGridGroup.Controls.Add(this.propertyGridControl);
            this.propertyGridGroup.Dock = System.Windows.Forms.DockStyle.Fill;
            this.propertyGridGroup.Location = new System.Drawing.Point(12, 409);
            this.propertyGridGroup.Name = "propertyGridGroup";
            this.propertyGridGroup.Size = new System.Drawing.Size(289, 143);
            this.propertyGridGroup.TabIndex = 4;
            this.propertyGridGroup.Text = "Content Properties";
            // 
            // propertyGridLayoutItem
            // 
            this.propertyGridLayoutItem.Control = this.propertyGridGroup;
            this.propertyGridLayoutItem.Location = new System.Drawing.Point(0, 397);
            this.propertyGridLayoutItem.Name = "propertyGridLayoutItem";
            this.propertyGridLayoutItem.Size = new System.Drawing.Size(293, 147);
            this.propertyGridLayoutItem.TextSize = new System.Drawing.Size(0, 0);
            this.propertyGridLayoutItem.TextVisible = false;
            // 
            // PdfPageContentCreation
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.pdfViewer);
            this.Controls.Add(this.sidePanel);
            this.Name = "PdfPageContentCreation";
            this.Size = new System.Drawing.Size(737, 564);
            this.sidePanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.accordionControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.propertyGridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl1)).EndInit();
            this.layoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.accordionLayoutItem)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitter)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.propertyGridGroup)).EndInit();
            this.propertyGridGroup.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.propertyGridLayoutItem)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion
        private XtraBars.Navigation.AccordionContentContainer accordionContentContainer1;
        private XtraBars.Navigation.AccordionControlElement accordionControlElement2;
        private XtraPdfViewer.PdfViewer pdfViewer;
        private XtraEditors.SidePanel sidePanel;
        private XtraBars.Navigation.AccordionControl accordionControl;
        private XtraBars.Navigation.AccordionControlElement accordionControlFileElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlNewElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlOpenElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlSaveElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlAddPageElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlFormFieldsElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlTextElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlImageElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlShapeElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlPathElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlLayoutElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlBringToFrontElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlSendToBackElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlBringForwardElement;
        private XtraBars.Navigation.AccordionControlElement accordionControlSendBackwardElement;
        private XtraVerticalGrid.PropertyGridControl propertyGridControl;
        private XtraLayout.LayoutControl layoutControl1;
        private XtraEditors.GroupControl propertyGridGroup;
        private XtraLayout.LayoutControlGroup Root;
        private XtraLayout.LayoutControlItem accordionLayoutItem;
        private XtraLayout.SplitterItem splitter;
        private XtraLayout.LayoutControlItem propertyGridLayoutItem;
    }
}
