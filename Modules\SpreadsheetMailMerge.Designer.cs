﻿using DevExpress.XtraEditors;

namespace DevExpress.Docs.Demos {
    public partial class SpreadsheetMailMerge {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing) {
            if (disposing && (components != null)) {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent() {
            this.sidePanel1 = new DevExpress.XtraEditors.SidePanel();
            this.btnSaveAs = new DevExpress.XtraEditors.SimpleButton();
            this.lblOrderId = new DevExpress.XtraEditors.LabelControl();
            this.edOrderId = new DevExpress.XtraEditors.ComboBoxEdit();
            this.spreadsheetPreview1 = new DevExpress.Docs.Demos.SpreadsheetPreview();
            this.sidePanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edOrderId.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // sidePanel1
            // 
            this.sidePanel1.AllowResize = false;
            this.sidePanel1.Controls.Add(this.btnSaveAs);
            this.sidePanel1.Controls.Add(this.lblOrderId);
            this.sidePanel1.Controls.Add(this.edOrderId);
            this.sidePanel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.sidePanel1.Location = new System.Drawing.Point(0, 0);
            this.sidePanel1.Name = "sidePanel1";
            this.sidePanel1.Size = new System.Drawing.Size(784, 53);
            this.sidePanel1.TabIndex = 37;
            this.sidePanel1.Text = "sidePanel1";
            // 
            // btnSaveAs
            // 
            this.btnSaveAs.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSaveAs.Location = new System.Drawing.Point(669, 15);
            this.btnSaveAs.Name = "btnSaveAs";
            this.btnSaveAs.Size = new System.Drawing.Size(98, 23);
            this.btnSaveAs.TabIndex = 41;
            this.btnSaveAs.Text = "Save As...";
            this.btnSaveAs.Click += new System.EventHandler(this.SaveAs_Click);
            // 
            // lblOrderId
            // 
            this.lblOrderId.Location = new System.Drawing.Point(16, 19);
            this.lblOrderId.Name = "lblOrderId";
            this.lblOrderId.Size = new System.Drawing.Size(45, 13);
            this.lblOrderId.TabIndex = 37;
            this.lblOrderId.Text = "Order Id:";
            // 
            // edOrderId
            // 
            this.edOrderId.EditValue = "10300";
            this.edOrderId.Location = new System.Drawing.Point(67, 16);
            this.edOrderId.Name = "edOrderId";
            this.edOrderId.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.edOrderId.Properties.Items.AddRange(new object[] {
            "10290",
            "10291",
            "10292",
            "10293",
            "10294",
            "10295",
            "10296",
            "10297",
            "10298",
            "10299",
            "10300",
            "10301",
            "10302",
            "10303",
            "10304",
            "10305",
            "10306",
            "10307",
            "10308",
            "10309"});
            this.edOrderId.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.edOrderId.Size = new System.Drawing.Size(120, 20);
            this.edOrderId.TabIndex = 38;
            this.edOrderId.SelectedValueChanged += new System.EventHandler(this.OrderId_SelectedValueChanged);
            // 
            // spreadsheetPreview1
            // 
            this.spreadsheetPreview1.CanShowBorders = false;
            this.spreadsheetPreview1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.spreadsheetPreview1.Location = new System.Drawing.Point(0, 53);
            this.spreadsheetPreview1.Name = "spreadsheetPreview1";
            this.spreadsheetPreview1.Size = new System.Drawing.Size(784, 379);
            this.spreadsheetPreview1.TabIndex = 38;
            this.spreadsheetPreview1.Workbook = null;
            // 
            // SpreadsheetMailMerge
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.spreadsheetPreview1);
            this.Controls.Add(this.sidePanel1);
            this.Name = "SpreadsheetMailMerge";
            this.sidePanel1.ResumeLayout(false);
            this.sidePanel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edOrderId.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion
        private SidePanel sidePanel1;
        private LabelControl lblOrderId;
        private ComboBoxEdit edOrderId;
        protected SimpleButton btnSaveAs;
        private SpreadsheetPreview spreadsheetPreview1;
    }
}
