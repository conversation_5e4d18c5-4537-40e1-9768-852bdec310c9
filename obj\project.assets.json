{"version": 3, "targets": {"net8.0-windows7.0": {"DevExpress.Charts/25.1.3": {"type": "package", "dependencies": {"DevExpress.Charts.Core": "[25.1.3]", "DevExpress.Data": "[25.1.3]", "DevExpress.DataVisualization.Core": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "System.Drawing.Common": "8.0.15"}, "compile": {"lib/net8.0/DevExpress.XtraCharts.v25.1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.XtraCharts.v25.1.dll": {"related": ".xml"}}}, "DevExpress.Charts.Core/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]"}, "compile": {"lib/net8.0/DevExpress.Charts.v25.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Charts.v25.1.Core.dll": {"related": ".xml"}}}, "DevExpress.CodeParser/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "System.CodeDom": "4.4.0"}, "compile": {"lib/net8.0/DevExpress.CodeParser.v25.1.dll": {}}, "runtime": {"lib/net8.0/DevExpress.CodeParser.v25.1.dll": {}}}, "DevExpress.Data/25.1.3": {"type": "package", "dependencies": {"System.Drawing.Common": "8.0.15"}, "compile": {"lib/net8.0/DevExpress.Data.v25.1.dll": {"related": ".dll.dfx;.xml"}}, "runtime": {"lib/net8.0/DevExpress.Data.v25.1.dll": {"related": ".dll.dfx;.xml"}}}, "DevExpress.Data.Desktop/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "System.Data.OleDb": "8.0.1"}, "compile": {"lib/net8.0-windows/DevExpress.Data.Desktop.v25.1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Data.Desktop.v25.1.dll": {"related": ".xml"}}, "build": {"build/net8.0-windows/DevExpress.Data.Desktop.props": {}}}, "DevExpress.DataAccess/25.1.3": {"type": "package", "dependencies": {"DevExpress.CodeParser": "[25.1.3]", "DevExpress.Data": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Office.Core": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.RichEdit.Core": "[25.1.3]", "DevExpress.Xpo": "[25.1.3]", "System.Configuration.ConfigurationManager": "8.0.1"}, "compile": {"lib/net8.0/DevExpress.DataAccess.v25.1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.DataAccess.v25.1.dll": {"related": ".xml"}}}, "DevExpress.DataAccess.UI/25.1.3": {"type": "package", "dependencies": {"DevExpress.CodeParser": "[25.1.3]", "DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]", "DevExpress.DataAccess": "[25.1.3]", "DevExpress.Diagram.Core": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.RichEdit.Core": "[25.1.3]", "DevExpress.Utils": "[25.1.3]", "DevExpress.Utils.UI": "[25.1.3]", "DevExpress.Win.Diagram": "[25.1.3]", "DevExpress.Win.Grid": "[25.1.3]", "DevExpress.Win.Navigation": "[25.1.3]", "DevExpress.Win.Printing": "[25.1.3]", "DevExpress.Win.RichEdit": "[25.1.3]", "DevExpress.Win.TreeList": "[25.1.3]", "DevExpress.Xpo": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.DataAccess.v25.1.UI.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.DataAccess.v25.1.UI.dll": {"related": ".xml"}}}, "DevExpress.DataVisualization.Core/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]"}, "compile": {"lib/net8.0/DevExpress.DataVisualization.v25.1.Core.dll": {}}, "runtime": {"lib/net8.0/DevExpress.DataVisualization.v25.1.Core.dll": {}}}, "DevExpress.Diagram.Core/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.Diagram.v25.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Diagram.v25.1.Core.dll": {"related": ".xml"}}, "build": {"build/net8.0-windows/DevExpress.Diagram.Core.props": {}}}, "DevExpress.Document.Processor/25.1.3": {"type": "package", "dependencies": {"DevExpress.Charts": "[25.1.3]", "DevExpress.Data": "[25.1.3]", "DevExpress.DataVisualization.Core": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Office.Core": "[25.1.3]", "DevExpress.Pdf.Core": "[25.1.3]", "DevExpress.Pdf.Drawing": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.RichEdit.Core": "[25.1.3]", "DevExpress.Spreadsheet.Core": "[25.1.3]", "DevExpress.TreeMap": "[25.1.3]", "System.Drawing.Common": "8.0.15", "System.Security.Cryptography.Xml": "8.0.2"}, "compile": {"lib/net8.0/DevExpress.Docs.v25.1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Docs.v25.1.dll": {"related": ".xml"}}}, "DevExpress.Drawing/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "System.Drawing.Common": "8.0.15"}, "compile": {"lib/net8.0/DevExpress.Drawing.v25.1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Drawing.v25.1.dll": {"related": ".xml"}}}, "DevExpress.Gauges.Core/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "System.Drawing.Common": "8.0.15"}, "compile": {"lib/net8.0/DevExpress.XtraGauges.v25.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.XtraGauges.v25.1.Core.dll": {"related": ".xml"}}}, "DevExpress.Images/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "System.Drawing.Common": "8.0.15"}, "compile": {"lib/net8.0/DevExpress.Images.v25.1.dll": {}}, "runtime": {"lib/net8.0/DevExpress.Images.v25.1.dll": {}}}, "DevExpress.Map.Core/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "System.Drawing.Common": "8.0.15"}, "compile": {"lib/net8.0/DevExpress.Map.v25.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Map.v25.1.Core.dll": {"related": ".xml"}}}, "DevExpress.Mvvm/25.1.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Mvvm.v25.1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Mvvm.v25.1.dll": {"related": ".xml"}}}, "DevExpress.Office.Core/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Pdf.Core": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "System.Drawing.Common": "8.0.15"}, "compile": {"lib/net8.0/DevExpress.Office.v25.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Office.v25.1.Core.dll": {"related": ".xml"}}}, "DevExpress.Pdf.Core/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "System.Drawing.Common": "8.0.15", "System.Security.Cryptography.Pkcs": "8.0.1"}, "compile": {"lib/net8.0/DevExpress.Pdf.v25.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Pdf.v25.1.Core.dll": {"related": ".xml"}}}, "DevExpress.Pdf.Drawing/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Pdf.Core": "[25.1.3]", "System.Drawing.Common": "8.0.15"}, "compile": {"lib/net8.0/DevExpress.Pdf.v25.1.Drawing.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Pdf.v25.1.Drawing.dll": {"related": ".xml"}}}, "DevExpress.PivotGrid.Core/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "System.Data.OleDb": "8.0.1", "System.Drawing.Common": "8.0.15"}, "compile": {"lib/net8.0/DevExpress.PivotGrid.v25.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.PivotGrid.v25.1.Core.dll": {"related": ".xml"}}}, "DevExpress.Printing.Core/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Pdf.Core": "[25.1.3]", "DevExpress.Pdf.Drawing": "[25.1.3]", "System.Drawing.Common": "8.0.15", "System.ServiceModel.Http": "8.1.2"}, "compile": {"lib/net8.0/DevExpress.Printing.v25.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Printing.v25.1.Core.dll": {"related": ".xml"}}}, "DevExpress.Reporting.Core/25.1.3": {"type": "package", "dependencies": {"DevExpress.Charts": "[25.1.3]", "DevExpress.Charts.Core": "[25.1.3]", "DevExpress.CodeParser": "[25.1.3]", "DevExpress.Data": "[25.1.3]", "DevExpress.DataAccess": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Gauges.Core": "[25.1.3]", "DevExpress.Office.Core": "[25.1.3]", "DevExpress.Pdf.Core": "[25.1.3]", "DevExpress.Pdf.Drawing": "[25.1.3]", "DevExpress.PivotGrid.Core": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.RichEdit.Core": "[25.1.3]", "DevExpress.RichEdit.Export": "[25.1.3]", "DevExpress.Sparkline.Core": "[25.1.3]", "DevExpress.Xpo": "[25.1.3]", "System.CodeDom": "4.4.0", "System.Drawing.Common": "8.0.15"}, "compile": {"lib/net8.0/DevExpress.XtraReports.v25.1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.XtraReports.v25.1.dll": {"related": ".xml"}}, "build": {"buildTransitive/DevExpress.Reporting.Core.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/DevExpress.Reporting.Core.targets": {}}}, "DevExpress.RichEdit.Core/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Office.Core": "[25.1.3]", "DevExpress.Pdf.Core": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "System.Drawing.Common": "8.0.15"}, "compile": {"lib/net8.0/DevExpress.RichEdit.v25.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.RichEdit.v25.1.Core.dll": {"related": ".xml"}}}, "DevExpress.RichEdit.Export/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Office.Core": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.RichEdit.Core": "[25.1.3]", "System.Drawing.Common": "8.0.15"}, "compile": {"lib/net8.0/DevExpress.RichEdit.v25.1.Export.dll": {}}, "runtime": {"lib/net8.0/DevExpress.RichEdit.v25.1.Export.dll": {}}}, "DevExpress.Scheduler.Core/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "System.Drawing.Common": "8.0.15"}, "compile": {"lib/net8.0/DevExpress.XtraScheduler.v25.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.XtraScheduler.v25.1.Core.dll": {"related": ".xml"}}}, "DevExpress.Scheduler.CoreDesktop/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Images": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.Scheduler.Core": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.XtraScheduler.v25.1.Core.Desktop.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.XtraScheduler.v25.1.Core.Desktop.dll": {"related": ".xml"}}}, "DevExpress.Sparkline.Core/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "System.Drawing.Common": "8.0.15"}, "compile": {"lib/net8.0/DevExpress.Sparkline.v25.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Sparkline.v25.1.Core.dll": {"related": ".xml"}}}, "DevExpress.SpellChecker.Core/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]"}, "compile": {"lib/net8.0/DevExpress.SpellChecker.v25.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.SpellChecker.v25.1.Core.dll": {"related": ".xml"}}}, "DevExpress.Spreadsheet.Core/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.DataAccess": "[25.1.3]", "DevExpress.DataVisualization.Core": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Office.Core": "[25.1.3]", "DevExpress.Pdf.Core": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.Sparkline.Core": "[25.1.3]", "System.Drawing.Common": "8.0.15"}, "compile": {"lib/net8.0/DevExpress.Spreadsheet.v25.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Spreadsheet.v25.1.Core.dll": {"related": ".xml"}}}, "DevExpress.TreeMap/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.DataVisualization.Core": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.TreeMap.Core": "[25.1.3]", "System.Drawing.Common": "8.0.15"}, "compile": {"lib/net8.0/DevExpress.XtraTreeMap.v25.1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.XtraTreeMap.v25.1.dll": {"related": ".xml"}}}, "DevExpress.TreeMap.Core/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]"}, "compile": {"lib/net8.0/DevExpress.TreeMap.v25.1.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.TreeMap.v25.1.Core.dll": {"related": ".xml"}}}, "DevExpress.Utils/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.Utils.v25.1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Utils.v25.1.dll": {"related": ".xml"}}}, "DevExpress.Utils.UI/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]", "DevExpress.DataAccess": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.RichEdit.Core": "[25.1.3]", "DevExpress.Utils": "[25.1.3]", "DevExpress.Win.Navigation": "[25.1.3]", "DevExpress.Win.Printing": "[25.1.3]", "DevExpress.Win.RichEdit": "[25.1.3]", "DevExpress.Win.TreeList": "[25.1.3]", "DevExpress.Win.VerticalGrid": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.Utils.v25.1.UI.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Utils.v25.1.UI.dll": {"related": ".xml"}}}, "DevExpress.Win/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.RichEdit.Export": "[25.1.3]", "DevExpress.Utils": "[25.1.3]", "DevExpress.Win.Grid": "[25.1.3]", "DevExpress.Win.Navigation": "[25.1.3]", "DevExpress.Win.PivotGrid": "[25.1.3]", "DevExpress.Win.Printing": "[25.1.3]", "DevExpress.Win.TreeList": "[25.1.3]", "DevExpress.Win.VerticalGrid": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.XtraNavBar.v25.1.dll": {"related": ".xml"}, "lib/net8.0-windows/DevExpress.XtraWizard.v25.1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.XtraNavBar.v25.1.dll": {"related": ".xml"}, "lib/net8.0-windows/DevExpress.XtraWizard.v25.1.dll": {"related": ".xml"}}}, "DevExpress.Win.BonusSkins/25.1.3": {"type": "package", "dependencies": {"DevExpress.Utils": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.BonusSkins.v25.1.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.BonusSkins.v25.1.dll": {}}}, "DevExpress.Win.Charts/25.1.3": {"type": "package", "dependencies": {"DevExpress.Charts": "[25.1.3]", "DevExpress.Charts.Core": "[25.1.3]", "DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]", "DevExpress.DataAccess.UI": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.Utils": "[25.1.3]", "DevExpress.Utils.UI": "[25.1.3]", "DevExpress.Win": "[25.1.3]", "DevExpress.Win.Grid": "[25.1.3]", "DevExpress.Win.Navigation": "[25.1.3]", "DevExpress.Win.Printing": "[25.1.3]", "DevExpress.Win.TreeList": "[25.1.3]", "DevExpress.Win.VerticalGrid": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.XtraCharts.v25.1.Extensions.dll": {}, "lib/net8.0-windows/DevExpress.XtraCharts.v25.1.UI.dll": {"related": ".xml"}, "lib/net8.0-windows/DevExpress.XtraCharts.v25.1.Wizard.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.XtraCharts.v25.1.Extensions.dll": {}, "lib/net8.0-windows/DevExpress.XtraCharts.v25.1.UI.dll": {"related": ".xml"}, "lib/net8.0-windows/DevExpress.XtraCharts.v25.1.Wizard.dll": {"related": ".xml"}}}, "DevExpress.Win.Design/25.1.3": {"type": "package", "dependencies": {"DevExpress.Mvvm": "[25.1.3]", "DevExpress.Win": "[25.1.3]", "DevExpress.Win.Charts": "[25.1.3]", "DevExpress.Win.Diagram": "[25.1.3]", "DevExpress.Win.Dialogs": "[25.1.3]", "DevExpress.Win.Gantt": "[25.1.3]", "DevExpress.Win.Gauges": "[25.1.3]", "DevExpress.Win.Map": "[25.1.3]", "DevExpress.Win.PdfViewer": "[25.1.3]", "DevExpress.Win.Reporting": "[25.1.3]", "DevExpress.Win.RichEdit": "[25.1.3]", "DevExpress.Win.SchedulerExtensions": "[25.1.3]", "DevExpress.Win.SpellChecker": "[25.1.3]", "DevExpress.Win.Spreadsheet": "[25.1.3]", "DevExpress.Win.TreeMap": "[25.1.3]", "DevExpress.Xpo": "[25.1.3]"}}, "DevExpress.Win.Diagram/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]", "DevExpress.Diagram.Core": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Pdf.Core": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.Utils": "[25.1.3]", "DevExpress.Win.Navigation": "[25.1.3]", "DevExpress.Win.Printing": "[25.1.3]", "DevExpress.Win.VerticalGrid": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.XtraDiagram.v25.1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.XtraDiagram.v25.1.dll": {"related": ".xml"}}}, "DevExpress.Win.Dialogs/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Utils": "[25.1.3]", "DevExpress.Win.Dialogs.Core": "[25.1.3]", "DevExpress.Win.Grid": "[25.1.3]", "DevExpress.Win.Navigation": "[25.1.3]", "DevExpress.Win.TreeList": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.XtraDialogs.v25.1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.XtraDialogs.v25.1.dll": {"related": ".xml"}}}, "DevExpress.Win.Dialogs.Core/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.Dialogs.v25.1.Core.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Dialogs.v25.1.Core.dll": {}}}, "DevExpress.Win.Gantt/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.Utils": "[25.1.3]", "DevExpress.Win.Navigation": "[25.1.3]", "DevExpress.Win.Printing": "[25.1.3]", "DevExpress.Win.TreeList": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.XtraGantt.v25.1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.XtraGantt.v25.1.dll": {"related": ".xml"}}}, "DevExpress.Win.Gauges/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Gauges.Core": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.Utils": "[25.1.3]", "DevExpress.Win.Navigation": "[25.1.3]", "DevExpress.Win.Printing": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.XtraGauges.v25.1.Presets.dll": {"related": ".xml"}, "lib/net8.0-windows/DevExpress.XtraGauges.v25.1.Win.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.XtraGauges.v25.1.Presets.dll": {"related": ".xml"}, "lib/net8.0-windows/DevExpress.XtraGauges.v25.1.Win.dll": {"related": ".xml"}}}, "DevExpress.Win.Grid/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.Utils": "[25.1.3]", "DevExpress.Win.Navigation": "[25.1.3]", "DevExpress.Win.Printing": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.XtraGrid.v25.1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.XtraGrid.v25.1.dll": {"related": ".xml"}}}, "DevExpress.Win.Map/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Map.Core": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.Utils": "[25.1.3]", "DevExpress.Win.Navigation": "[25.1.3]", "DevExpress.Win.Printing": "[25.1.3]", "System.Data.SqlClient": "4.8.6"}, "compile": {"lib/net8.0-windows/DevExpress.XtraMap.v25.1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.XtraMap.v25.1.dll": {"related": ".xml"}}}, "DevExpress.Win.Navigation/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.Sparkline.Core": "[25.1.3]", "DevExpress.Utils": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.XtraBars.v25.1.dll": {"related": ".xml"}, "lib/net8.0-windows/DevExpress.XtraEditors.v25.1.dll": {"related": ".xml"}, "lib/net8.0-windows/DevExpress.XtraLayout.v25.1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.XtraBars.v25.1.dll": {"related": ".xml"}, "lib/net8.0-windows/DevExpress.XtraEditors.v25.1.dll": {"related": ".xml"}, "lib/net8.0-windows/DevExpress.XtraLayout.v25.1.dll": {"related": ".xml"}}, "build": {"build/net8.0-windows/DevExpress.Win.Navigation.targets": {}}}, "DevExpress.Win.PdfViewer/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Pdf.Core": "[25.1.3]", "DevExpress.Pdf.Drawing": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.Utils": "[25.1.3]", "DevExpress.Win.Navigation": "[25.1.3]", "DevExpress.Win.TreeList": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.XtraPdfViewer.v25.1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.XtraPdfViewer.v25.1.dll": {"related": ".xml"}}}, "DevExpress.Win.PivotGrid/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.PivotGrid.Core": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.Utils": "[25.1.3]", "DevExpress.Win.Navigation": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.XtraPivotGrid.v25.1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.XtraPivotGrid.v25.1.dll": {"related": ".xml"}}}, "DevExpress.Win.Printing/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.RichEdit.Core": "[25.1.3]", "DevExpress.Utils": "[25.1.3]", "DevExpress.Win.Navigation": "[25.1.3]", "DevExpress.Win.TreeList": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.XtraPrinting.v25.1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.XtraPrinting.v25.1.dll": {"related": ".xml"}}}, "DevExpress.Win.Reporting/25.1.3": {"type": "package", "dependencies": {"DevExpress.Charts": "[25.1.3]", "DevExpress.Charts.Core": "[25.1.3]", "DevExpress.CodeParser": "[25.1.3]", "DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]", "DevExpress.DataAccess": "[25.1.3]", "DevExpress.DataAccess.UI": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Gauges.Core": "[25.1.3]", "DevExpress.Office.Core": "[25.1.3]", "DevExpress.PivotGrid.Core": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.Reporting.Core": "[25.1.3]", "DevExpress.RichEdit.Core": "[25.1.3]", "DevExpress.Sparkline.Core": "[25.1.3]", "DevExpress.Utils": "[25.1.3]", "DevExpress.Utils.UI": "[25.1.3]", "DevExpress.Win": "[25.1.3]", "DevExpress.Win.Charts": "[25.1.3]", "DevExpress.Win.Grid": "[25.1.3]", "DevExpress.Win.Navigation": "[25.1.3]", "DevExpress.Win.PivotGrid": "[25.1.3]", "DevExpress.Win.Printing": "[25.1.3]", "DevExpress.Win.RichEdit": "[25.1.3]", "DevExpress.Win.TreeList": "[25.1.3]", "DevExpress.Win.VerticalGrid": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.XtraReports.v25.1.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.XtraReports.v25.1.Extensions.dll": {"related": ".xml"}}}, "DevExpress.Win.RichEdit/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Images": "[25.1.3]", "DevExpress.Office.Core": "[25.1.3]", "DevExpress.Pdf.Core": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.RichEdit.Core": "[25.1.3]", "DevExpress.Utils": "[25.1.3]", "DevExpress.Win.Grid": "[25.1.3]", "DevExpress.Win.Navigation": "[25.1.3]", "DevExpress.Win.Printing": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.XtraRichEdit.v25.1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.XtraRichEdit.v25.1.dll": {"related": ".xml"}}}, "DevExpress.Win.Scheduler/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.Scheduler.Core": "[25.1.3]", "DevExpress.Scheduler.CoreDesktop": "[25.1.3]", "DevExpress.Utils": "[25.1.3]", "DevExpress.Win.Grid": "[25.1.3]", "DevExpress.Win.Navigation": "[25.1.3]", "DevExpress.Win.Printing": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.XtraScheduler.v25.1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.XtraScheduler.v25.1.dll": {"related": ".xml"}}}, "DevExpress.Win.SchedulerExtensions/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.Reporting.Core": "[25.1.3]", "DevExpress.Scheduler.Core": "[25.1.3]", "DevExpress.Scheduler.CoreDesktop": "[25.1.3]", "DevExpress.SpellChecker.Core": "[25.1.3]", "DevExpress.Utils": "[25.1.3]", "DevExpress.Win.Navigation": "[25.1.3]", "DevExpress.Win.Printing": "[25.1.3]", "DevExpress.Win.Reporting": "[25.1.3]", "DevExpress.Win.Scheduler": "[25.1.3]", "DevExpress.Win.SchedulerReporting": "[25.1.3]", "DevExpress.Win.SpellChecker": "[25.1.3]", "DevExpress.Win.TreeList": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.XtraScheduler.v25.1.Extensions.dll": {"related": ".xml"}, "lib/net8.0-windows/DevExpress.XtraScheduler.v25.1.Reporting.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.XtraScheduler.v25.1.Extensions.dll": {"related": ".xml"}, "lib/net8.0-windows/DevExpress.XtraScheduler.v25.1.Reporting.Extensions.dll": {"related": ".xml"}}}, "DevExpress.Win.SchedulerReporting/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.Reporting.Core": "[25.1.3]", "DevExpress.Scheduler.Core": "[25.1.3]", "DevExpress.Scheduler.CoreDesktop": "[25.1.3]", "DevExpress.Utils": "[25.1.3]", "DevExpress.Win.Navigation": "[25.1.3]", "DevExpress.Win.Printing": "[25.1.3]", "DevExpress.Win.Scheduler": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.XtraScheduler.v25.1.Reporting.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.XtraScheduler.v25.1.Reporting.dll": {"related": ".xml"}}}, "DevExpress.Win.SpellChecker/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.SpellChecker.Core": "[25.1.3]", "DevExpress.Utils": "[25.1.3]", "DevExpress.Win.Navigation": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.XtraSpellChecker.v25.1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.XtraSpellChecker.v25.1.dll": {"related": ".xml"}}}, "DevExpress.Win.Spreadsheet/25.1.3": {"type": "package", "dependencies": {"DevExpress.Charts": "[25.1.3]", "DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]", "DevExpress.DataAccess": "[25.1.3]", "DevExpress.DataAccess.UI": "[25.1.3]", "DevExpress.DataVisualization.Core": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Images": "[25.1.3]", "DevExpress.Office.Core": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.RichEdit.Core": "[25.1.3]", "DevExpress.Spreadsheet.Core": "[25.1.3]", "DevExpress.TreeMap": "[25.1.3]", "DevExpress.Utils": "[25.1.3]", "DevExpress.Utils.UI": "[25.1.3]", "DevExpress.Win": "[25.1.3]", "DevExpress.Win.Grid": "[25.1.3]", "DevExpress.Win.Navigation": "[25.1.3]", "DevExpress.Win.Printing": "[25.1.3]", "DevExpress.Win.RichEdit": "[25.1.3]", "DevExpress.Win.TreeList": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.XtraSpreadsheet.v25.1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.XtraSpreadsheet.v25.1.dll": {"related": ".xml"}}}, "DevExpress.Win.TreeList/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.Utils": "[25.1.3]", "DevExpress.Win.Navigation": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.XtraTreeList.v25.1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.XtraTreeList.v25.1.dll": {"related": ".xml"}}}, "DevExpress.Win.TreeMap/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.TreeMap": "[25.1.3]", "DevExpress.TreeMap.Core": "[25.1.3]", "DevExpress.Utils": "[25.1.3]", "DevExpress.Win.Navigation": "[25.1.3]", "DevExpress.Win.Printing": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.XtraTreeMap.v25.1.UI.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.XtraTreeMap.v25.1.UI.dll": {"related": ".xml"}}}, "DevExpress.Win.VerticalGrid/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "DevExpress.Data.Desktop": "[25.1.3]", "DevExpress.Drawing": "[25.1.3]", "DevExpress.Printing.Core": "[25.1.3]", "DevExpress.Utils": "[25.1.3]", "DevExpress.Win.Navigation": "[25.1.3]", "DevExpress.Win.Printing": "[25.1.3]"}, "compile": {"lib/net8.0-windows/DevExpress.XtraVerticalGrid.v25.1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.XtraVerticalGrid.v25.1.dll": {"related": ".xml"}}}, "DevExpress.Xpo/25.1.3": {"type": "package", "dependencies": {"DevExpress.Data": "[25.1.3]", "Microsoft.Extensions.DependencyInjection": "8.0.1", "System.Drawing.Common": "8.0.15", "System.ServiceModel.Http": "8.1.2", "System.ServiceModel.NetTcp": "8.1.2"}, "compile": {"lib/net8.0/DevExpress.Xpo.v25.1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Xpo.v25.1.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.ObjectPool/8.0.10": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Win32.SystemEvents/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"assetType": "native", "rid": "win-arm64"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"assetType": "native", "rid": "win-x64"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"assetType": "native", "rid": "win-x86"}}}, "System.CodeDom/4.4.0": {"type": "package", "compile": {"ref/netstandard2.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.CodeDom.dll": {}}}, "System.Configuration.ConfigurationManager/8.0.1": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "8.0.1", "System.Security.Cryptography.ProtectedData": "8.0.0"}, "compile": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Data.OleDb/8.0.1": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "8.0.1", "System.Diagnostics.PerformanceCounter": "8.0.1"}, "compile": {"lib/net8.0/System.Data.OleDb.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Data.OleDb.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Data.OleDb.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Data.SqlClient/4.8.6": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "compile": {"ref/netcoreapp2.1/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Diagnostics.EventLog/8.0.1": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {"assetType": "runtime", "rid": "win"}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Diagnostics.PerformanceCounter/8.0.1": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "8.0.1"}, "compile": {"lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Drawing.Common/8.0.15": {"type": "package", "dependencies": {"Microsoft.Win32.SystemEvents": "8.0.0"}, "compile": {"lib/net8.0/System.Drawing.Common.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/System.Drawing.Common.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Security.AccessControl/4.7.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "compile": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Security.Cryptography.Xml/8.0.2": {"type": "package", "dependencies": {"System.Security.Cryptography.Pkcs": "8.0.1"}, "compile": {"lib/net8.0/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "compile": {"ref/netcoreapp3.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.ServiceModel.Http/8.1.2": {"type": "package", "dependencies": {"System.ServiceModel.Primitives": "8.1.2"}, "compile": {"ref/net8.0/System.ServiceModel.Http.dll": {}}, "runtime": {"lib/net8.0/System.ServiceModel.Http.dll": {"related": ".pdb"}}, "resource": {"lib/net8.0/cs/System.ServiceModel.Http.resources.dll": {"locale": "cs"}, "lib/net8.0/de/System.ServiceModel.Http.resources.dll": {"locale": "de"}, "lib/net8.0/es/System.ServiceModel.Http.resources.dll": {"locale": "es"}, "lib/net8.0/fr/System.ServiceModel.Http.resources.dll": {"locale": "fr"}, "lib/net8.0/it/System.ServiceModel.Http.resources.dll": {"locale": "it"}, "lib/net8.0/ja/System.ServiceModel.Http.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/System.ServiceModel.Http.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/System.ServiceModel.Http.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/System.ServiceModel.Http.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/System.ServiceModel.Http.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/System.ServiceModel.Http.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/System.ServiceModel.Http.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/System.ServiceModel.Http.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.ServiceModel.NetFramingBase/8.1.2": {"type": "package", "dependencies": {"System.ServiceModel.Primitives": "8.1.2"}, "compile": {"ref/net8.0/System.ServiceModel.NetFramingBase.dll": {}}, "runtime": {"lib/net8.0/System.ServiceModel.NetFramingBase.dll": {"related": ".pdb"}}, "resource": {"lib/net8.0/cs/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "cs"}, "lib/net8.0/de/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "de"}, "lib/net8.0/es/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "es"}, "lib/net8.0/fr/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "fr"}, "lib/net8.0/it/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "it"}, "lib/net8.0/ja/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.ServiceModel.NetTcp/8.1.2": {"type": "package", "dependencies": {"System.ServiceModel.NetFramingBase": "8.1.2", "System.ServiceModel.Primitives": "8.1.2"}, "compile": {"ref/net8.0/System.ServiceModel.NetTcp.dll": {}}, "runtime": {"lib/net8.0/System.ServiceModel.NetTcp.dll": {"related": ".pdb"}}, "resource": {"lib/net8.0/cs/System.ServiceModel.NetTcp.resources.dll": {"locale": "cs"}, "lib/net8.0/de/System.ServiceModel.NetTcp.resources.dll": {"locale": "de"}, "lib/net8.0/es/System.ServiceModel.NetTcp.resources.dll": {"locale": "es"}, "lib/net8.0/fr/System.ServiceModel.NetTcp.resources.dll": {"locale": "fr"}, "lib/net8.0/it/System.ServiceModel.NetTcp.resources.dll": {"locale": "it"}, "lib/net8.0/ja/System.ServiceModel.NetTcp.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/System.ServiceModel.NetTcp.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/System.ServiceModel.NetTcp.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/System.ServiceModel.NetTcp.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/System.ServiceModel.NetTcp.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/System.ServiceModel.NetTcp.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/System.ServiceModel.NetTcp.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/System.ServiceModel.NetTcp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.ServiceModel.Primitives/8.1.2": {"type": "package", "dependencies": {"Microsoft.Extensions.ObjectPool": "8.0.10", "System.Security.Cryptography.Xml": "8.0.2"}, "compile": {"ref/net8.0/System.ServiceModel.Duplex.dll": {}, "ref/net8.0/System.ServiceModel.Primitives.dll": {}, "ref/net8.0/System.ServiceModel.Security.dll": {}, "ref/net8.0/System.ServiceModel.dll": {}}, "runtime": {"lib/net8.0/System.ServiceModel.Duplex.dll": {"related": ".pdb"}, "lib/net8.0/System.ServiceModel.Primitives.dll": {"related": ".pdb"}, "lib/net8.0/System.ServiceModel.Security.dll": {"related": ".pdb"}, "lib/net8.0/System.ServiceModel.dll": {"related": ".Duplex.pdb;.pdb;.Primitives.pdb;.Security.pdb"}}, "resource": {"lib/net8.0/cs/System.ServiceModel.Primitives.resources.dll": {"locale": "cs"}, "lib/net8.0/de/System.ServiceModel.Primitives.resources.dll": {"locale": "de"}, "lib/net8.0/es/System.ServiceModel.Primitives.resources.dll": {"locale": "es"}, "lib/net8.0/fr/System.ServiceModel.Primitives.resources.dll": {"locale": "fr"}, "lib/net8.0/it/System.ServiceModel.Primitives.resources.dll": {"locale": "it"}, "lib/net8.0/ja/System.ServiceModel.Primitives.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/System.ServiceModel.Primitives.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/System.ServiceModel.Primitives.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/System.ServiceModel.Primitives.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/System.ServiceModel.Primitives.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/System.ServiceModel.Primitives.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/System.ServiceModel.Primitives.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/System.ServiceModel.Primitives.resources.dll": {"locale": "zh-Han<PERSON>"}}}}}, "libraries": {"DevExpress.Charts/25.1.3": {"sha512": "4UJvYD+3UexoaSCdTdfJBFkVT8gBeYRTJaFHQQSsUjpYWQF203jRAbj4eWY3cQYQCs4M3a5THTDH4NSNEbV+Kg==", "type": "package", "path": "devexpress.charts/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.charts.25.1.3.nupkg.sha512", "devexpress.charts.nuspec", "lib/net462/DevExpress.XtraCharts.v25.1.dll", "lib/net462/DevExpress.XtraCharts.v25.1.xml", "lib/net8.0/DevExpress.XtraCharts.v25.1.dll", "lib/net8.0/DevExpress.XtraCharts.v25.1.xml"]}, "DevExpress.Charts.Core/25.1.3": {"sha512": "+3xrRgQmhXIcZGop1EFvARI2cKGCppxiqRyKQh1LhvdE7pDTc8elb6P6h1cWX6yvFLSgIYu12EhKb1f6K7G0Hg==", "type": "package", "path": "devexpress.charts.core/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.charts.core.25.1.3.nupkg.sha512", "devexpress.charts.core.nuspec", "lib/net462/DevExpress.Charts.v25.1.Core.dll", "lib/net462/DevExpress.Charts.v25.1.Core.xml", "lib/net8.0/DevExpress.Charts.v25.1.Core.dll", "lib/net8.0/DevExpress.Charts.v25.1.Core.xml"]}, "DevExpress.CodeParser/25.1.3": {"sha512": "ee2pY4Um0R6HqNLdyDIg6Y9Jg6B1TlGvogvRhaiNp8csicDQE0gzdnHzgHr03ax/P3CsjkZzDdP6mUhV3Pq0pw==", "type": "package", "path": "devexpress.codeparser/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.codeparser.25.1.3.nupkg.sha512", "devexpress.codeparser.nuspec", "lib/net462/DevExpress.CodeParser.v25.1.dll", "lib/net8.0/DevExpress.CodeParser.v25.1.dll"]}, "DevExpress.Data/25.1.3": {"sha512": "zqXiuRty9D64USkIFKFQek+wF3Ie70xmLrabt2VDG8kDx1788x3lU8XadWnoTviSAVdt83AcyAZGc77eQAf1eg==", "type": "package", "path": "devexpress.data/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/DevExpress.Generator.dll", "analyzers/dotnet/DevExpress.Generator.dll.dfx", "devexpress.data.25.1.3.nupkg.sha512", "devexpress.data.nuspec", "lib/net462/DevExpress.Data.v25.1.dll", "lib/net462/DevExpress.Data.v25.1.dll.dfx", "lib/net462/DevExpress.Data.v25.1.xml", "lib/net8.0/DevExpress.Data.v25.1.dll", "lib/net8.0/DevExpress.Data.v25.1.dll.dfx", "lib/net8.0/DevExpress.Data.v25.1.xml", "tools/VisualStudioToolsManifest.xml", "tools/install.ps1", "tools/uninstall.ps1"]}, "DevExpress.Data.Desktop/25.1.3": {"sha512": "cKsXvvr3FuvwEZZVpDeAdtpSaHOpdYf80yWth0JyiMEX6v+tq1nDIn2KJ4CfJjwDvSAbVXlMi2vrvA92r2Jy6Q==", "type": "package", "path": "devexpress.data.desktop/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "build/net8.0-windows/DevExpress.Data.Desktop.props", "devexpress.data.desktop.25.1.3.nupkg.sha512", "devexpress.data.desktop.nuspec", "lib/net462/DevExpress.Data.Desktop.v25.1.dll", "lib/net462/DevExpress.Data.Desktop.v25.1.xml", "lib/net8.0-windows/DevExpress.Data.Desktop.v25.1.dll", "lib/net8.0-windows/DevExpress.Data.Desktop.v25.1.xml"]}, "DevExpress.DataAccess/25.1.3": {"sha512": "YEPGVLePtRj4YvaZvvhFxGf3k+34gsNjbfig8y0Z7k5JoslOLBxL1w/OBwl0JNDkoBJT7Y/MoL1saY05rk+fvg==", "type": "package", "path": "devexpress.dataaccess/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.dataaccess.25.1.3.nupkg.sha512", "devexpress.dataaccess.nuspec", "lib/net462/DevExpress.DataAccess.v25.1.dll", "lib/net462/DevExpress.DataAccess.v25.1.xml", "lib/net8.0/DevExpress.DataAccess.v25.1.dll", "lib/net8.0/DevExpress.DataAccess.v25.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.DataAccess.UI/25.1.3": {"sha512": "a8PDAMxLVPFDvzUPX+0B4h8KM8yoVbr2Nvz9u5DGYOJTYuDYLFCOmsIqwmS9ZNlij7ktso7cG2VGEfrA/rKz2w==", "type": "package", "path": "devexpress.dataaccess.ui/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.dataaccess.ui.25.1.3.nupkg.sha512", "devexpress.dataaccess.ui.nuspec", "lib/net462/DevExpress.DataAccess.v25.1.UI.dll", "lib/net462/DevExpress.DataAccess.v25.1.UI.xml", "lib/net8.0-windows/Design/WinForms/Server/DevExpress.Design.Routing.DataAccess.UI.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.DataAccess.v25.1.Design.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.Xpo.v25.1.Design.dll", "lib/net8.0-windows/DevExpress.DataAccess.v25.1.UI.dll", "lib/net8.0-windows/DevExpress.DataAccess.v25.1.UI.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.DataVisualization.Core/25.1.3": {"sha512": "enUoOnhpLJTkLXComJR1Q9/MRmHGoInJIrnp+EpXncbkgbBH5e8BZjwthwAt0MoatNSKVlo/ipMraUqCIV835w==", "type": "package", "path": "devexpress.datavisualization.core/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.datavisualization.core.25.1.3.nupkg.sha512", "devexpress.datavisualization.core.nuspec", "lib/net462/DevExpress.DataVisualization.v25.1.Core.dll", "lib/net8.0/DevExpress.DataVisualization.v25.1.Core.dll"]}, "DevExpress.Diagram.Core/25.1.3": {"sha512": "+UmoDfjY8azJcOcznEGD0xWUbwffEJ791hci38wnqAMrIV0hndFiH92socVzm87XWRORcSvaX5i1Ni66+Hx2xQ==", "type": "package", "path": "devexpress.diagram.core/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "build/net8.0-windows/DevExpress.Diagram.Core.props", "devexpress.diagram.core.25.1.3.nupkg.sha512", "devexpress.diagram.core.nuspec", "lib/net462/DevExpress.Diagram.v25.1.Core.dll", "lib/net462/DevExpress.Diagram.v25.1.Core.xml", "lib/net8.0-windows/DevExpress.Diagram.v25.1.Core.dll", "lib/net8.0-windows/DevExpress.Diagram.v25.1.Core.xml"]}, "DevExpress.Document.Processor/25.1.3": {"sha512": "R1mvB5+qDJdtXyydrf8nLA/YQcQsdOxbKGDVF55odQ1OygF3+YswLVlJWKG8LQxULCZbvCFUTPcVqZTOsT9Odg==", "type": "package", "path": "devexpress.document.processor/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.document.processor.25.1.3.nupkg.sha512", "devexpress.document.processor.nuspec", "lib/net462/DevExpress.Docs.v25.1.dll", "lib/net462/DevExpress.Docs.v25.1.xml", "lib/net8.0/DevExpress.Docs.v25.1.dll", "lib/net8.0/DevExpress.Docs.v25.1.xml"]}, "DevExpress.Drawing/25.1.3": {"sha512": "r9HLuVHH+w0OvwZiLDAtdV2d7yTe9kqU1RGBq0L62/lZ3CTGKqHe3YOXwpzVAhhbynU2JrzbbwFzU3k7vkPpWA==", "type": "package", "path": "devexpress.drawing/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.drawing.25.1.3.nupkg.sha512", "devexpress.drawing.nuspec", "lib/net462/DevExpress.Drawing.v25.1.dll", "lib/net462/DevExpress.Drawing.v25.1.xml", "lib/net8.0/DevExpress.Drawing.v25.1.dll", "lib/net8.0/DevExpress.Drawing.v25.1.xml"]}, "DevExpress.Gauges.Core/25.1.3": {"sha512": "sL3CiMl9CMC5PIZKs2A51549pJBc47U9sjfY+NSDr6YtXx4jer+9it8At+h+EbLgdYbe+8htVILxVsG9viy6uA==", "type": "package", "path": "devexpress.gauges.core/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.gauges.core.25.1.3.nupkg.sha512", "devexpress.gauges.core.nuspec", "lib/net462/DevExpress.XtraGauges.v25.1.Core.dll", "lib/net462/DevExpress.XtraGauges.v25.1.Core.xml", "lib/net8.0/DevExpress.XtraGauges.v25.1.Core.dll", "lib/net8.0/DevExpress.XtraGauges.v25.1.Core.xml"]}, "DevExpress.Images/25.1.3": {"sha512": "DfZTo9xCzYsH54yyPl/zKCp29fqyu1dmuSIdUh+ot6NJdpHe1XPsOnj4fTBR1Ch/VHjxWjl4jdrgkNAKZTuWVg==", "type": "package", "path": "devexpress.images/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.images.25.1.3.nupkg.sha512", "devexpress.images.nuspec", "lib/net462/DevExpress.Images.v25.1.dll", "lib/net8.0/DevExpress.Images.v25.1.dll"]}, "DevExpress.Map.Core/25.1.3": {"sha512": "QpliDOUjGgLYq5clLgqt6fZ/kxabpvsI/WJ2t/sEUXaZtHf3fr6JRBz5TS22j3n3q9ilZtzp2sYMx+pEJvoz5Q==", "type": "package", "path": "devexpress.map.core/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.map.core.25.1.3.nupkg.sha512", "devexpress.map.core.nuspec", "lib/net462/DevExpress.Map.v25.1.Core.dll", "lib/net462/DevExpress.Map.v25.1.Core.xml", "lib/net8.0/DevExpress.Map.v25.1.Core.dll", "lib/net8.0/DevExpress.Map.v25.1.Core.xml"]}, "DevExpress.Mvvm/25.1.3": {"sha512": "U8wDrxfhA+DY60uKWEkyf4ZjekWNOXQMgTFRNQ4K//a/winkzXMBBgVzo1l3MvjOclIcvXNTz8TUYOGqHe/Nfw==", "type": "package", "path": "devexpress.mvvm/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.mvvm.25.1.3.nupkg.sha512", "devexpress.mvvm.nuspec", "lib/net462/DevExpress.Mvvm.v25.1.dll", "lib/net462/DevExpress.Mvvm.v25.1.xml", "lib/net8.0-windows/DevExpress.Mvvm.v25.1.dll", "lib/net8.0-windows/DevExpress.Mvvm.v25.1.xml"]}, "DevExpress.Office.Core/25.1.3": {"sha512": "VJlyx3C6QQ/7NLviIzdSykN/te9H9ci+JJWSAX7LfvWTNap16eGOzjSS8M0Jtb7FkenyHw6VpwoL9tqTUBFrcw==", "type": "package", "path": "devexpress.office.core/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.office.core.25.1.3.nupkg.sha512", "devexpress.office.core.nuspec", "lib/net462/DevExpress.Office.v25.1.Core.dll", "lib/net462/DevExpress.Office.v25.1.Core.xml", "lib/net8.0/DevExpress.Office.v25.1.Core.dll", "lib/net8.0/DevExpress.Office.v25.1.Core.xml"]}, "DevExpress.Pdf.Core/25.1.3": {"sha512": "g0ZV84uzyXLM0WmO+dpGNSF6CgW39oBOUn+snBAvhY8IUsOiF/6ixY1CFZXFO02fqz6LxiT1WVBH+GM7Ly1EAw==", "type": "package", "path": "devexpress.pdf.core/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.pdf.core.25.1.3.nupkg.sha512", "devexpress.pdf.core.nuspec", "lib/net462/DevExpress.Pdf.v25.1.Core.dll", "lib/net462/DevExpress.Pdf.v25.1.Core.xml", "lib/net8.0/DevExpress.Pdf.v25.1.Core.dll", "lib/net8.0/DevExpress.Pdf.v25.1.Core.xml"]}, "DevExpress.Pdf.Drawing/25.1.3": {"sha512": "uLUUPTp5mpDDWNC/uWXKFtx4ncRIxp4yR/6FujsBPocVQkLOqmhw1DlmEc6T3p005UouQqg5FY/Vxsssmohyyg==", "type": "package", "path": "devexpress.pdf.drawing/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.pdf.drawing.25.1.3.nupkg.sha512", "devexpress.pdf.drawing.nuspec", "lib/net462/DevExpress.Pdf.v25.1.Drawing.dll", "lib/net462/DevExpress.Pdf.v25.1.Drawing.xml", "lib/net8.0/DevExpress.Pdf.v25.1.Drawing.dll", "lib/net8.0/DevExpress.Pdf.v25.1.Drawing.xml"]}, "DevExpress.PivotGrid.Core/25.1.3": {"sha512": "Y+8hLpODt6+HiiKY6PKaUYwKo2YnA61zs4UjneSys9bIOM+7PRr58HlcoLmz/SJgXwgClXBbPHT2Wl3msaEJ4Q==", "type": "package", "path": "devexpress.pivotgrid.core/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.pivotgrid.core.25.1.3.nupkg.sha512", "devexpress.pivotgrid.core.nuspec", "lib/net462/DevExpress.PivotGrid.v25.1.Core.dll", "lib/net462/DevExpress.PivotGrid.v25.1.Core.xml", "lib/net8.0/DevExpress.PivotGrid.v25.1.Core.dll", "lib/net8.0/DevExpress.PivotGrid.v25.1.Core.xml"]}, "DevExpress.Printing.Core/25.1.3": {"sha512": "I2aXPYz+v2vZvZr0rMOWzG+CWkMJp9fAEAkXtiUxSqtdTZ73Q/g4Stxj+ra2vhZGMF4yxjlCT5ejQzzsoJMw6g==", "type": "package", "path": "devexpress.printing.core/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.printing.core.25.1.3.nupkg.sha512", "devexpress.printing.core.nuspec", "lib/net462/DevExpress.Printing.v25.1.Core.dll", "lib/net462/DevExpress.Printing.v25.1.Core.xml", "lib/net8.0/DevExpress.Printing.v25.1.Core.dll", "lib/net8.0/DevExpress.Printing.v25.1.Core.xml"]}, "DevExpress.Reporting.Core/25.1.3": {"sha512": "0LJoqGeBmjI3JTU6yUMOtRyOFswnsNKq44mHcegsIYXyoJMPKcBaWqSo3U41zhDLQx73KQvzTMfAPkEK84+OMQ==", "type": "package", "path": "devexpress.reporting.core/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "build/DevExpress.Reporting.Core.targets", "buildMultiTargeting/DevExpress.Reporting.Core.targets", "buildTransitive/DevExpress.Reporting.Core.targets", "devexpress.reporting.core.25.1.3.nupkg.sha512", "devexpress.reporting.core.nuspec", "lib/net462/DevExpress.XtraReports.v25.1.dll", "lib/net462/DevExpress.XtraReports.v25.1.xml", "lib/net8.0/DevExpress.XtraReports.v25.1.dll", "lib/net8.0/DevExpress.XtraReports.v25.1.xml"]}, "DevExpress.RichEdit.Core/25.1.3": {"sha512": "TTiCFfMMa+i4ouaE5IOUNScYIbZPj34yKUY1kuofCChgD1wYSD1kyyBvowONslf0iK5vwKkcT0TyRrAhVTSIGQ==", "type": "package", "path": "devexpress.richedit.core/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.richedit.core.25.1.3.nupkg.sha512", "devexpress.richedit.core.nuspec", "lib/net462/DevExpress.RichEdit.v25.1.Core.dll", "lib/net462/DevExpress.RichEdit.v25.1.Core.xml", "lib/net8.0/DevExpress.RichEdit.v25.1.Core.dll", "lib/net8.0/DevExpress.RichEdit.v25.1.Core.xml"]}, "DevExpress.RichEdit.Export/25.1.3": {"sha512": "GjfoFEK5nhbkuZ3XU94QrcsiuOwYGVYsRjfpTu9nGU5Ailpjg0cSDV4CMXlsGDd6H9Rhfzz2tValrnipXPETVg==", "type": "package", "path": "devexpress.richedit.export/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.richedit.export.25.1.3.nupkg.sha512", "devexpress.richedit.export.nuspec", "lib/net462/DevExpress.RichEdit.v25.1.Export.dll", "lib/net8.0/DevExpress.RichEdit.v25.1.Export.dll"]}, "DevExpress.Scheduler.Core/25.1.3": {"sha512": "NU+Ihz8kqeRoFRgjh3oI4HF4xgFWJyXOx9bWx0bnO4bVwK2IHe4vcDVsLI7CxFrv8wY2EgxUwH5MGTYfumYAWg==", "type": "package", "path": "devexpress.scheduler.core/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.scheduler.core.25.1.3.nupkg.sha512", "devexpress.scheduler.core.nuspec", "lib/net462/DevExpress.XtraScheduler.v25.1.Core.dll", "lib/net462/DevExpress.XtraScheduler.v25.1.Core.xml", "lib/net8.0/DevExpress.XtraScheduler.v25.1.Core.dll", "lib/net8.0/DevExpress.XtraScheduler.v25.1.Core.xml"]}, "DevExpress.Scheduler.CoreDesktop/25.1.3": {"sha512": "w6z3RQNdoH1Q+JVuJA+F8b299DMbkLMAaR9tNzDFfJaVMaFS2NZaT1lycujiK2d9MDd1mOMugwlyvwrAVToYLg==", "type": "package", "path": "devexpress.scheduler.coredesktop/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.scheduler.coredesktop.25.1.3.nupkg.sha512", "devexpress.scheduler.coredesktop.nuspec", "lib/net462/DevExpress.XtraScheduler.v25.1.Core.Desktop.dll", "lib/net462/DevExpress.XtraScheduler.v25.1.Core.Desktop.xml", "lib/net8.0-windows/DevExpress.XtraScheduler.v25.1.Core.Desktop.dll", "lib/net8.0-windows/DevExpress.XtraScheduler.v25.1.Core.Desktop.xml"]}, "DevExpress.Sparkline.Core/25.1.3": {"sha512": "XclXEX4UssJRE+Q7GTw4Fjo37n7W2LHbhHqcXC4zPjY7hB1U8gn9e9Z30J6SCVnyn8JHc8RQil2i8nWJQrw98Q==", "type": "package", "path": "devexpress.sparkline.core/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.sparkline.core.25.1.3.nupkg.sha512", "devexpress.sparkline.core.nuspec", "lib/net462/DevExpress.Sparkline.v25.1.Core.dll", "lib/net462/DevExpress.Sparkline.v25.1.Core.xml", "lib/net8.0/DevExpress.Sparkline.v25.1.Core.dll", "lib/net8.0/DevExpress.Sparkline.v25.1.Core.xml"]}, "DevExpress.SpellChecker.Core/25.1.3": {"sha512": "RsFA7955N3Fe4PQHJGiCWKtAFqb+TAM3bTvFjNLUm9dAQKkLBEjmNQTWGY03G5MSo3nUtuB3LYBv3iFBfznOBQ==", "type": "package", "path": "devexpress.spellchecker.core/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.spellchecker.core.25.1.3.nupkg.sha512", "devexpress.spellchecker.core.nuspec", "lib/net462/DevExpress.SpellChecker.v25.1.Core.dll", "lib/net462/DevExpress.SpellChecker.v25.1.Core.xml", "lib/net8.0/DevExpress.SpellChecker.v25.1.Core.dll", "lib/net8.0/DevExpress.SpellChecker.v25.1.Core.xml"]}, "DevExpress.Spreadsheet.Core/25.1.3": {"sha512": "RUgJYQdPAH1OwIYwGenbqU78TqNERUD9P4YCQdyqyhaglZt9PCyj7mFIzxuRIUHWJ7JIWcxsAkyr8FSaLuH0yg==", "type": "package", "path": "devexpress.spreadsheet.core/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.spreadsheet.core.25.1.3.nupkg.sha512", "devexpress.spreadsheet.core.nuspec", "lib/net462/DevExpress.Spreadsheet.v25.1.Core.dll", "lib/net462/DevExpress.Spreadsheet.v25.1.Core.xml", "lib/net8.0/DevExpress.Spreadsheet.v25.1.Core.dll", "lib/net8.0/DevExpress.Spreadsheet.v25.1.Core.xml"]}, "DevExpress.TreeMap/25.1.3": {"sha512": "MYKsV2I4NGw5HEFQH9WhROtGQo8+0wDhcZJLlfI1wt2EVa/f0GBPfyR18GhWW+emvcTJvhtJWM847bDgDMBTKw==", "type": "package", "path": "devexpress.treemap/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.treemap.25.1.3.nupkg.sha512", "devexpress.treemap.nuspec", "lib/net462/DevExpress.XtraTreeMap.v25.1.dll", "lib/net462/DevExpress.XtraTreeMap.v25.1.xml", "lib/net8.0/DevExpress.XtraTreeMap.v25.1.dll", "lib/net8.0/DevExpress.XtraTreeMap.v25.1.xml"]}, "DevExpress.TreeMap.Core/25.1.3": {"sha512": "gwm0YmaY5xG+9aMxMwZ+/zAukwcwsFOsiBTeqByA9LRr3FP4vLdEanSDMYnHkK+900QUlMw5QJZVnREmBsBm1w==", "type": "package", "path": "devexpress.treemap.core/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.treemap.core.25.1.3.nupkg.sha512", "devexpress.treemap.core.nuspec", "lib/net462/DevExpress.TreeMap.v25.1.Core.dll", "lib/net462/DevExpress.TreeMap.v25.1.Core.xml", "lib/net8.0/DevExpress.TreeMap.v25.1.Core.dll", "lib/net8.0/DevExpress.TreeMap.v25.1.Core.xml"]}, "DevExpress.Utils/25.1.3": {"sha512": "CR4gZ7d2wbE5ra4qT7ar/SaDoFWAC+pZDa5kBHXIzecEw5Hq3wluQhACmWXhGKJmWcCLp80qMRdpW6q0i0/1Vw==", "type": "package", "path": "devexpress.utils/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.utils.25.1.3.nupkg.sha512", "devexpress.utils.nuspec", "lib/net462/DevExpress.Utils.v25.1.dll", "lib/net462/DevExpress.Utils.v25.1.xml", "lib/net8.0-windows/DevExpress.Utils.v25.1.dll", "lib/net8.0-windows/DevExpress.Utils.v25.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Utils.UI/25.1.3": {"sha512": "GTKIe7RRWZgOh0Fo2t/FbamL6Fb/5quTgFCr4pPDv1+jHEVWjSfGVEVq4ou5F0ZmJ4XS9g/b/KdvPoS2fvErbA==", "type": "package", "path": "devexpress.utils.ui/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.utils.ui.25.1.3.nupkg.sha512", "devexpress.utils.ui.nuspec", "lib/net462/DevExpress.Utils.v25.1.UI.dll", "lib/net462/DevExpress.Utils.v25.1.UI.xml", "lib/net8.0-windows/DevExpress.Utils.v25.1.UI.dll", "lib/net8.0-windows/DevExpress.Utils.v25.1.UI.xml"]}, "DevExpress.Win/25.1.3": {"sha512": "oyJLY38fiwubHDCjxeJEe3SsguFVz5deZZdjJhcobRR7owaFajGheEd69ER+vqO26hxbCkC7b0jnAWU8yAPxMw==", "type": "package", "path": "devexpress.win/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.25.1.3.nupkg.sha512", "devexpress.win.nuspec", "lib/net462/DevExpress.XtraNavBar.v25.1.dll", "lib/net462/DevExpress.XtraNavBar.v25.1.xml", "lib/net462/DevExpress.XtraWizard.v25.1.dll", "lib/net462/DevExpress.XtraWizard.v25.1.xml", "lib/net8.0-windows/Design/WinForms/Server/DevExpress.Design.Routing.Win.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.XtraNavBar.v25.1.Design.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.XtraWizard.v25.1.Design.dll", "lib/net8.0-windows/DevExpress.XtraNavBar.v25.1.dll", "lib/net8.0-windows/DevExpress.XtraNavBar.v25.1.xml", "lib/net8.0-windows/DevExpress.XtraWizard.v25.1.dll", "lib/net8.0-windows/DevExpress.XtraWizard.v25.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.BonusSkins/25.1.3": {"sha512": "XsbGSCCqu40iU8UPIOiNIGLMxRZ4z1/BLZlvU95w0im23rZeiAlnCUbAZo7ZBej2E05aStnYGFg/eGfXsquPxw==", "type": "package", "path": "devexpress.win.bonusskins/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.bonusskins.25.1.3.nupkg.sha512", "devexpress.win.bonusskins.nuspec", "lib/net462/DevExpress.BonusSkins.v25.1.dll", "lib/net8.0-windows/DevExpress.BonusSkins.v25.1.dll"]}, "DevExpress.Win.Charts/25.1.3": {"sha512": "6c/wWo4OnHwu1Ixf+skRGfdrSz4xz7bYNgj1SodlCIsainPHx0SV2OvSKbYiXIsC5PY0TAjGk0cU2mi+Iz19Pw==", "type": "package", "path": "devexpress.win.charts/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.charts.25.1.3.nupkg.sha512", "devexpress.win.charts.nuspec", "lib/net462/DevExpress.XtraCharts.v25.1.Extensions.dll", "lib/net462/DevExpress.XtraCharts.v25.1.UI.dll", "lib/net462/DevExpress.XtraCharts.v25.1.UI.xml", "lib/net462/DevExpress.XtraCharts.v25.1.Wizard.dll", "lib/net462/DevExpress.XtraCharts.v25.1.Wizard.xml", "lib/net8.0-windows/Design/WinForms/Server/DevExpress.Design.Routing.Win.Charts.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.XtraCharts.v25.1.Design.dll", "lib/net8.0-windows/DevExpress.XtraCharts.v25.1.Extensions.dll", "lib/net8.0-windows/DevExpress.XtraCharts.v25.1.UI.dll", "lib/net8.0-windows/DevExpress.XtraCharts.v25.1.UI.xml", "lib/net8.0-windows/DevExpress.XtraCharts.v25.1.Wizard.dll", "lib/net8.0-windows/DevExpress.XtraCharts.v25.1.Wizard.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.Design/25.1.3": {"sha512": "uQxG5YEDfASiW2FDOINp9prC5R5horgM8Ro0rwVy+dTVXc79sl4sJgscKr7I+JW5ykPGn+QXdCrGwMHSfmc67g==", "type": "package", "path": "devexpress.win.design/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.design.25.1.3.nupkg.sha512", "devexpress.win.design.nuspec"]}, "DevExpress.Win.Diagram/25.1.3": {"sha512": "Cn3WL8YgG7FgMUoq3CpoWAeffbOFgGBK2r27kMgf4zxxaCyaYvQYZz/lHx51A98tXiy/mfbZ3akK+GuFTciHQA==", "type": "package", "path": "devexpress.win.diagram/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.diagram.25.1.3.nupkg.sha512", "devexpress.win.diagram.nuspec", "lib/net462/DevExpress.XtraDiagram.v25.1.dll", "lib/net462/DevExpress.XtraDiagram.v25.1.xml", "lib/net8.0-windows/Design/WinForms/Server/DevExpress.Design.Routing.Win.Diagram.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.XtraDiagram.v25.1.Design.dll", "lib/net8.0-windows/DevExpress.XtraDiagram.v25.1.dll", "lib/net8.0-windows/DevExpress.XtraDiagram.v25.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.Dialogs/25.1.3": {"sha512": "VsY1CMtGl0y6rvXhYMnYXq861dJeyJ7Z7e4QBzWGPmMon92NgnUWNo0evX2NBeRyHq1eill8vH+bdAomTF8vBw==", "type": "package", "path": "devexpress.win.dialogs/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.dialogs.25.1.3.nupkg.sha512", "devexpress.win.dialogs.nuspec", "lib/net462/DevExpress.XtraDialogs.v25.1.dll", "lib/net462/DevExpress.XtraDialogs.v25.1.xml", "lib/net8.0-windows/Design/WinForms/Server/DevExpress.Design.Routing.Win.Dialogs.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.XtraDialogs.v25.1.Design.dll", "lib/net8.0-windows/DevExpress.XtraDialogs.v25.1.dll", "lib/net8.0-windows/DevExpress.XtraDialogs.v25.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.Dialogs.Core/25.1.3": {"sha512": "IWZvxT9SsCQK1LTjXLRbMHY2CusGNVNfn35wqAxVGNQYDAgntJtZcEPyJ9lxNHMu2xBXwBq7VK7RUf3idTza/A==", "type": "package", "path": "devexpress.win.dialogs.core/25.1.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.dialogs.core.25.1.3.nupkg.sha512", "devexpress.win.dialogs.core.nuspec", "lib/net462/DevExpress.Dialogs.v25.1.Core.dll", "lib/net8.0-windows/DevExpress.Dialogs.v25.1.Core.dll"]}, "DevExpress.Win.Gantt/25.1.3": {"sha512": "/ihGd4XcaY19N+vM5UUXp2jIybZItijrOvv1MdvRx49EPNRinzLsGfaxPkt+y3HeQUF/o8NXbBL8GcHTcp1/Xg==", "type": "package", "path": "devexpress.win.gantt/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.gantt.25.1.3.nupkg.sha512", "devexpress.win.gantt.nuspec", "lib/net462/DevExpress.XtraGantt.v25.1.dll", "lib/net462/DevExpress.XtraGantt.v25.1.xml", "lib/net8.0-windows/Design/WinForms/Server/DevExpress.Design.Routing.Win.Gantt.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.XtraGantt.v25.1.Design.dll", "lib/net8.0-windows/DevExpress.XtraGantt.v25.1.dll", "lib/net8.0-windows/DevExpress.XtraGantt.v25.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.Gauges/25.1.3": {"sha512": "sxpuFv28J9UhJUY65+dhRo+utZhud6EZ6iBZM/MnxaauUj3I5GqRgXgQ5ODWDSMccpLYFNO3Sfo8oirbn3vpPA==", "type": "package", "path": "devexpress.win.gauges/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.gauges.25.1.3.nupkg.sha512", "devexpress.win.gauges.nuspec", "lib/net462/DevExpress.XtraGauges.v25.1.Presets.dll", "lib/net462/DevExpress.XtraGauges.v25.1.Presets.xml", "lib/net462/DevExpress.XtraGauges.v25.1.Win.dll", "lib/net462/DevExpress.XtraGauges.v25.1.Win.xml", "lib/net8.0-windows/Design/WinForms/Server/DevExpress.Design.Routing.Win.Gauges.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.XtraGauges.v25.1.Design.Win.dll", "lib/net8.0-windows/DevExpress.XtraGauges.v25.1.Presets.dll", "lib/net8.0-windows/DevExpress.XtraGauges.v25.1.Presets.xml", "lib/net8.0-windows/DevExpress.XtraGauges.v25.1.Win.dll", "lib/net8.0-windows/DevExpress.XtraGauges.v25.1.Win.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.Grid/25.1.3": {"sha512": "GFExoYXCLvBfPirHU96yKqZg7vWAk2guaohSto8VTNBgxKpSl5tGtue0L9LhJc/DsFed7ZK2GPQmqeJ4ch2ktQ==", "type": "package", "path": "devexpress.win.grid/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.grid.25.1.3.nupkg.sha512", "devexpress.win.grid.nuspec", "lib/net462/DevExpress.XtraGrid.v25.1.dll", "lib/net462/DevExpress.XtraGrid.v25.1.xml", "lib/net8.0-windows/Design/WinForms/Server/DevExpress.Design.Routing.Win.Grid.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.XtraGrid.v25.1.Design.dll", "lib/net8.0-windows/DevExpress.XtraGrid.v25.1.dll", "lib/net8.0-windows/DevExpress.XtraGrid.v25.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.Map/25.1.3": {"sha512": "pCaGDAiGv7WCPVZovLFPQY66UaYiUjFWLAWHwEXjxXtUlPxjpMbZdZYUxHWNtWe2hKar3k8+3ApzZQ/ZsbILbA==", "type": "package", "path": "devexpress.win.map/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.map.25.1.3.nupkg.sha512", "devexpress.win.map.nuspec", "lib/net462/DevExpress.XtraMap.v25.1.dll", "lib/net462/DevExpress.XtraMap.v25.1.xml", "lib/net8.0-windows/Design/WinForms/Server/DevExpress.Design.Routing.Win.Map.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.XtraMap.v25.1.Design.dll", "lib/net8.0-windows/DevExpress.XtraMap.v25.1.dll", "lib/net8.0-windows/DevExpress.XtraMap.v25.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.Navigation/25.1.3": {"sha512": "CbMqmarleO7fqfc9k/SiVpua2HrMkuTsTe2cE8lCr5UQmyBzPwGlLnlcQE/btJh2cht8e9xRzy9S46UV2OQ5zQ==", "type": "package", "path": "devexpress.win.navigation/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "build/net8.0-windows/DevExpress.Win.Navigation.targets", "devexpress.win.navigation.25.1.3.nupkg.sha512", "devexpress.win.navigation.nuspec", "lib/net462/DevExpress.XtraBars.v25.1.dll", "lib/net462/DevExpress.XtraBars.v25.1.xml", "lib/net462/DevExpress.XtraEditors.v25.1.dll", "lib/net462/DevExpress.XtraEditors.v25.1.xml", "lib/net462/DevExpress.XtraLayout.v25.1.dll", "lib/net462/DevExpress.XtraLayout.v25.1.xml", "lib/net8.0-windows/Design/WinForms/DevExpress.Design.Protocol.RemoteClient.v25.1.dll", "lib/net8.0-windows/Design/WinForms/DevExpress.Design.RemoteClient.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/DevExpress.Design.Protocol.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/DevExpress.Design.Routing.Win.Navigation.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/DevExpress.Design.Surface.Core.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.Design.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.Dialogs.v25.1.Core.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.Images.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.Mvvm.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.PivotGrid.v25.1.Core.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.XtraBars.v25.1.Design.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.XtraDialogs.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.XtraEditors.v25.1.Design.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.XtraGrid.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.XtraLayout.v25.1.Design.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.XtraTreeList.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.XtraVerticalGrid.v25.1.dll", "lib/net8.0-windows/DevExpress.XtraBars.v25.1.dll", "lib/net8.0-windows/DevExpress.XtraBars.v25.1.xml", "lib/net8.0-windows/DevExpress.XtraEditors.v25.1.dll", "lib/net8.0-windows/DevExpress.XtraEditors.v25.1.xml", "lib/net8.0-windows/DevExpress.XtraLayout.v25.1.dll", "lib/net8.0-windows/DevExpress.XtraLayout.v25.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.PdfViewer/25.1.3": {"sha512": "a898xy8FWsa4Jtk6jp1b1AHm5Vr1smu1JfKBXJe+ZWUNyikvWviaSkO8ruoHeNhLiXh1a8mN5R0J5lPXiYukmw==", "type": "package", "path": "devexpress.win.pdfviewer/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.pdfviewer.25.1.3.nupkg.sha512", "devexpress.win.pdfviewer.nuspec", "lib/net462/DevExpress.XtraPdfViewer.v25.1.dll", "lib/net462/DevExpress.XtraPdfViewer.v25.1.xml", "lib/net8.0-windows/Design/WinForms/Server/DevExpress.Design.Routing.Win.PdfViewer.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.XtraPdfViewer.v25.1.Design.dll", "lib/net8.0-windows/DevExpress.XtraPdfViewer.v25.1.dll", "lib/net8.0-windows/DevExpress.XtraPdfViewer.v25.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.PivotGrid/25.1.3": {"sha512": "861XPEIYKEcR9WYVcNQS6cDW+m1HX58n7QWjF4RAzOgzgGRtOiGa5rGwwIAcQ0ludNgxA6aLjqB9Nk391IRRaQ==", "type": "package", "path": "devexpress.win.pivotgrid/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.pivotgrid.25.1.3.nupkg.sha512", "devexpress.win.pivotgrid.nuspec", "lib/net462/DevExpress.XtraPivotGrid.v25.1.dll", "lib/net462/DevExpress.XtraPivotGrid.v25.1.xml", "lib/net8.0-windows/Design/WinForms/Server/DevExpress.Design.Routing.Win.PivotGrid.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.XtraPivotGrid.v25.1.Design.dll", "lib/net8.0-windows/DevExpress.XtraPivotGrid.v25.1.dll", "lib/net8.0-windows/DevExpress.XtraPivotGrid.v25.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.Printing/25.1.3": {"sha512": "M0KxYuupoyeFQ/x105yXTUIXkp9qPoKl1q/3g2V2UeLkyqEufoOtYEL+nVg08S62JKYfjJfaT2EsMXgc1gP4Pg==", "type": "package", "path": "devexpress.win.printing/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.printing.25.1.3.nupkg.sha512", "devexpress.win.printing.nuspec", "lib/net462/DevExpress.XtraPrinting.v25.1.dll", "lib/net462/DevExpress.XtraPrinting.v25.1.xml", "lib/net8.0-windows/DevExpress.XtraPrinting.v25.1.dll", "lib/net8.0-windows/DevExpress.XtraPrinting.v25.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.Reporting/25.1.3": {"sha512": "nuC1GH2rdlns2h8QcSCz1IwrqQLBr0nzeMwoxdg78PwgZUGg1BF0XK9M9yPebIwfG8j0Lo/B0upNaiagD/UzLQ==", "type": "package", "path": "devexpress.win.reporting/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.reporting.25.1.3.nupkg.sha512", "devexpress.win.reporting.nuspec", "lib/net462/DevExpress.XtraReports.v25.1.Extensions.dll", "lib/net462/DevExpress.XtraReports.v25.1.Extensions.xml", "lib/net8.0-windows/Design/WinForms/Server/DevExpress.Design.Routing.Win.Reporting.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.XtraPrinting.v25.1.Design.dll", "lib/net8.0-windows/DevExpress.XtraReports.v25.1.Extensions.dll", "lib/net8.0-windows/DevExpress.XtraReports.v25.1.Extensions.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.RichEdit/25.1.3": {"sha512": "9nqVv3kZPAYpQNMcHNYtTXX2/DEcdZH+I6Dkp1PZMwLvyosAqLzX6Tro2t7pG2A2c9/bs544mBWU/3b51cxKHg==", "type": "package", "path": "devexpress.win.richedit/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.richedit.25.1.3.nupkg.sha512", "devexpress.win.richedit.nuspec", "lib/net462/DevExpress.XtraRichEdit.v25.1.dll", "lib/net462/DevExpress.XtraRichEdit.v25.1.xml", "lib/net8.0-windows/Design/WinForms/Server/DevExpress.Design.Routing.Win.RichEdit.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.XtraRichEdit.v25.1.Design.dll", "lib/net8.0-windows/DevExpress.XtraRichEdit.v25.1.dll", "lib/net8.0-windows/DevExpress.XtraRichEdit.v25.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.Scheduler/25.1.3": {"sha512": "sstYnTlz/E07NCaxoQ3u4a/qkYN4I9XoajHT5n8Y4bNO1vTFvGBvIGBKcNIA3mIRfHhZI4glJDO6IFgbWY2org==", "type": "package", "path": "devexpress.win.scheduler/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.scheduler.25.1.3.nupkg.sha512", "devexpress.win.scheduler.nuspec", "lib/net462/DevExpress.XtraScheduler.v25.1.dll", "lib/net462/DevExpress.XtraScheduler.v25.1.xml", "lib/net8.0-windows/DevExpress.XtraScheduler.v25.1.dll", "lib/net8.0-windows/DevExpress.XtraScheduler.v25.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.SchedulerExtensions/25.1.3": {"sha512": "VztwjvgJND15t+TlUKYIfLzsVcyVjMXnYxLMOQCEUN4VErog7bEwiSWQuwSgw+vtGKn24g7BpbfMgJXtRvnXOw==", "type": "package", "path": "devexpress.win.schedulerextensions/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.schedulerextensions.25.1.3.nupkg.sha512", "devexpress.win.schedulerextensions.nuspec", "lib/net462/DevExpress.XtraScheduler.v25.1.Extensions.dll", "lib/net462/DevExpress.XtraScheduler.v25.1.Extensions.xml", "lib/net462/DevExpress.XtraScheduler.v25.1.Reporting.Extensions.dll", "lib/net462/DevExpress.XtraScheduler.v25.1.Reporting.Extensions.xml", "lib/net8.0-windows/Design/WinForms/Server/DevExpress.Design.Routing.Win.SchedulerExtensions.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.XtraScheduler.v25.1.Design.dll", "lib/net8.0-windows/DevExpress.XtraScheduler.v25.1.Extensions.dll", "lib/net8.0-windows/DevExpress.XtraScheduler.v25.1.Extensions.xml", "lib/net8.0-windows/DevExpress.XtraScheduler.v25.1.Reporting.Extensions.dll", "lib/net8.0-windows/DevExpress.XtraScheduler.v25.1.Reporting.Extensions.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.SchedulerReporting/25.1.3": {"sha512": "8IQ2yG35x33xFneZeuSYxVHdnzezSvebEgfDz/+wiEckMQ8rUKNH29F/H4nm/bHqqM9Nn5JMISOHjgX30AHd3g==", "type": "package", "path": "devexpress.win.schedulerreporting/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.schedulerreporting.25.1.3.nupkg.sha512", "devexpress.win.schedulerreporting.nuspec", "lib/net462/DevExpress.XtraScheduler.v25.1.Reporting.dll", "lib/net462/DevExpress.XtraScheduler.v25.1.Reporting.xml", "lib/net8.0-windows/DevExpress.XtraScheduler.v25.1.Reporting.dll", "lib/net8.0-windows/DevExpress.XtraScheduler.v25.1.Reporting.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.SpellChecker/25.1.3": {"sha512": "6HeiB09BUXnkY4JJuhQWLBgr/Si14Z6rIeBvblKXWM7boKbpB0Z3wrE6sh4Fbm28OzaQ8v5GUz02he/HahX8TQ==", "type": "package", "path": "devexpress.win.spellchecker/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.spellchecker.25.1.3.nupkg.sha512", "devexpress.win.spellchecker.nuspec", "lib/net462/DevExpress.XtraSpellChecker.v25.1.dll", "lib/net462/DevExpress.XtraSpellChecker.v25.1.xml", "lib/net8.0-windows/Design/WinForms/Server/DevExpress.Design.Routing.Win.SpellChecker.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.XtraSpellChecker.v25.1.Design.dll", "lib/net8.0-windows/DevExpress.XtraSpellChecker.v25.1.dll", "lib/net8.0-windows/DevExpress.XtraSpellChecker.v25.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.Spreadsheet/25.1.3": {"sha512": "mfw3u8b+mNv+CfOZYqLV+gbgKr2OizVO/VvIMj3OytWVXvZsjZe0QsSBsYi9+Sc6Qt5hTGRwXWrSfN6K7HSb+w==", "type": "package", "path": "devexpress.win.spreadsheet/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.spreadsheet.25.1.3.nupkg.sha512", "devexpress.win.spreadsheet.nuspec", "lib/net462/DevExpress.XtraSpreadsheet.v25.1.dll", "lib/net462/DevExpress.XtraSpreadsheet.v25.1.xml", "lib/net8.0-windows/Design/WinForms/Server/DevExpress.Design.Routing.Win.Spreadsheet.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.XtraSpreadsheet.v25.1.Design.dll", "lib/net8.0-windows/DevExpress.XtraSpreadsheet.v25.1.dll", "lib/net8.0-windows/DevExpress.XtraSpreadsheet.v25.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.TreeList/25.1.3": {"sha512": "SK2cwxV8vGOjqDr1VgmHMfMf2KM7S5psU6G/OJL70I5LZRn4q66aWZ2ryPe1lygHqDOUMC3PuOp5C75mFZRy9g==", "type": "package", "path": "devexpress.win.treelist/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.treelist.25.1.3.nupkg.sha512", "devexpress.win.treelist.nuspec", "lib/net462/DevExpress.XtraTreeList.v25.1.dll", "lib/net462/DevExpress.XtraTreeList.v25.1.xml", "lib/net8.0-windows/Design/WinForms/Server/DevExpress.Design.Routing.Win.TreeList.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.XtraTreeList.v25.1.Design.dll", "lib/net8.0-windows/DevExpress.XtraTreeList.v25.1.dll", "lib/net8.0-windows/DevExpress.XtraTreeList.v25.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.TreeMap/25.1.3": {"sha512": "o1MYYs2OiokGGQs3wLp4Qd1sEF3GUVEEVP68eYz66igx0oceYyTUmphHRJn9LeR3OEgtRmNz4XqC/8GLhAk+aQ==", "type": "package", "path": "devexpress.win.treemap/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.treemap.25.1.3.nupkg.sha512", "devexpress.win.treemap.nuspec", "lib/net462/DevExpress.XtraTreeMap.v25.1.UI.dll", "lib/net462/DevExpress.XtraTreeMap.v25.1.UI.xml", "lib/net8.0-windows/Design/WinForms/Server/DevExpress.Design.Routing.Win.TreeMap.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.XtraTreeMap.v25.1.Design.dll", "lib/net8.0-windows/DevExpress.XtraTreeMap.v25.1.UI.dll", "lib/net8.0-windows/DevExpress.XtraTreeMap.v25.1.UI.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Win.VerticalGrid/25.1.3": {"sha512": "MoXduZx8aj7e4vGMzkQ3x9uutC1UDMrYjqyxqiqmrGS2mu3a21WVwLN55rSz2PkppCwETScGOQzLxRoSfIkWgA==", "type": "package", "path": "devexpress.win.verticalgrid/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.win.verticalgrid.25.1.3.nupkg.sha512", "devexpress.win.verticalgrid.nuspec", "lib/net462/DevExpress.XtraVerticalGrid.v25.1.dll", "lib/net462/DevExpress.XtraVerticalGrid.v25.1.xml", "lib/net8.0-windows/Design/WinForms/Server/DevExpress.Design.Routing.Win.VerticalGrid.v25.1.dll", "lib/net8.0-windows/Design/WinForms/Server/lib/DevExpress.XtraVerticalGrid.v25.1.Design.dll", "lib/net8.0-windows/DevExpress.XtraVerticalGrid.v25.1.dll", "lib/net8.0-windows/DevExpress.XtraVerticalGrid.v25.1.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Xpo/25.1.3": {"sha512": "58+/07WZMmbDTbaTEI60+UsDKynHVVPV/FY4ancWpwQCTwF5UOUZxPLn3HBQ+QhdSsKDdtPBGzjAxpN5yJFQBg==", "type": "package", "path": "devexpress.xpo/25.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.xpo.25.1.3.nupkg.sha512", "devexpress.xpo.nuspec", "lib/net462/DevExpress.Xpo.v25.1.dll", "lib/net462/DevExpress.Xpo.v25.1.xml", "lib/net8.0/DevExpress.Xpo.v25.1.dll", "lib/net8.0/DevExpress.Xpo.v25.1.xml", "readme.txt", "tools/VisualStudioToolsManifest.xml"]}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"sha512": "BmANAnR5Xd4Oqw7yQ75xOAYODybZQRzdeNucg7kS5wWKd2PNnMdYtJ2Vciy0QLylRmv42DGl5+AFL9izA6F1Rw==", "type": "package", "path": "microsoft.extensions.dependencyinjection/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.8.0.1.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"sha512": "3iE7UF7MQkCv1cxzCahz+Y/guQbTqieyxyaWKhrRO91itI9cOKO76OHeQDahqG4MmW5umr3CcCvGmK92lWNlbg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.ObjectPool/8.0.10": {"sha512": "u7gAG7JgxF8VSJUGPSudAcPxOt+ymJKQCSxNRxiuKV+klCQbHljQR75SilpedCTfhPWDhtUwIJpnDVtspr9nMg==", "type": "package", "path": "microsoft.extensions.objectpool/8.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.Extensions.ObjectPool.dll", "lib/net462/Microsoft.Extensions.ObjectPool.xml", "lib/net8.0/Microsoft.Extensions.ObjectPool.dll", "lib/net8.0/Microsoft.Extensions.ObjectPool.xml", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.xml", "microsoft.extensions.objectpool.8.0.10.nupkg.sha512", "microsoft.extensions.objectpool.nuspec"]}, "Microsoft.NETCore.Platforms/3.1.0": {"sha512": "z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "type": "package", "path": "microsoft.netcore.platforms/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.3.1.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Win32.Registry/4.7.0": {"sha512": "KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "type": "package", "path": "microsoft.win32.registry/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.4.7.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/net472/Microsoft.Win32.Registry.dll", "ref/net472/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Win32.SystemEvents/8.0.0": {"sha512": "9opKRyOKMCi2xJ7Bj7kxtZ1r9vbzosMvRrdEhVhDz8j8MoBGgB+WmC94yH839NPH+BclAjtQ/pyagvi/8gDLkw==", "type": "package", "path": "microsoft.win32.systemevents/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Win32.SystemEvents.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Win32.SystemEvents.targets", "lib/net462/Microsoft.Win32.SystemEvents.dll", "lib/net462/Microsoft.Win32.SystemEvents.xml", "lib/net6.0/Microsoft.Win32.SystemEvents.dll", "lib/net6.0/Microsoft.Win32.SystemEvents.xml", "lib/net7.0/Microsoft.Win32.SystemEvents.dll", "lib/net7.0/Microsoft.Win32.SystemEvents.xml", "lib/net8.0/Microsoft.Win32.SystemEvents.dll", "lib/net8.0/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.8.0.0.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt"]}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"sha512": "9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "type": "package", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512", "runtime.native.system.data.sqlclient.sni.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "type": "package", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-arm64/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "type": "package", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-x64.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-x64/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "type": "package", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-x86.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-x86/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "System.CodeDom/4.4.0": {"sha512": "2sCCb7doXEwtYAbqzbF/8UAeDRMNmPaQbU2q50Psg1J9KzumyVVCgKQY8s53WIPTufNT0DpSe9QRvVjOzfDWBA==", "type": "package", "path": "system.codedom/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.dll", "ref/net461/System.CodeDom.dll", "ref/net461/System.CodeDom.xml", "ref/netstandard2.0/System.CodeDom.dll", "ref/netstandard2.0/System.CodeDom.xml", "system.codedom.4.4.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Configuration.ConfigurationManager/8.0.1": {"sha512": "gPYFPDyohW2gXNhdQRSjtmeS6FymL2crg4Sral1wtvEJ7DUqFCDWDVbbLobASbzxfic8U1hQEdC7hmg9LHncMw==", "type": "package", "path": "system.configuration.configurationmanager/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Configuration.ConfigurationManager.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "lib/net462/System.Configuration.ConfigurationManager.dll", "lib/net462/System.Configuration.ConfigurationManager.xml", "lib/net6.0/System.Configuration.ConfigurationManager.dll", "lib/net6.0/System.Configuration.ConfigurationManager.xml", "lib/net7.0/System.Configuration.ConfigurationManager.dll", "lib/net7.0/System.Configuration.ConfigurationManager.xml", "lib/net8.0/System.Configuration.ConfigurationManager.dll", "lib/net8.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.8.0.1.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.OleDb/8.0.1": {"sha512": "RO+/y2ggU5956uQDRXdjA1e2l5yJ4rTWNX76eZ+3sgtYGqGapCe2kQCyiUci+/y6Fyb21Irp4RQEdfrIiuYrxQ==", "type": "package", "path": "system.data.oledb/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Data.OleDb.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Data.OleDb.targets", "lib/net462/System.Data.OleDb.dll", "lib/net462/System.Data.OleDb.xml", "lib/net6.0/System.Data.OleDb.dll", "lib/net6.0/System.Data.OleDb.xml", "lib/net7.0/System.Data.OleDb.dll", "lib/net7.0/System.Data.OleDb.xml", "lib/net8.0/System.Data.OleDb.dll", "lib/net8.0/System.Data.OleDb.xml", "lib/netstandard2.0/System.Data.OleDb.dll", "lib/netstandard2.0/System.Data.OleDb.xml", "runtimes/win/lib/net6.0/System.Data.OleDb.dll", "runtimes/win/lib/net6.0/System.Data.OleDb.xml", "runtimes/win/lib/net7.0/System.Data.OleDb.dll", "runtimes/win/lib/net7.0/System.Data.OleDb.xml", "runtimes/win/lib/net8.0/System.Data.OleDb.dll", "runtimes/win/lib/net8.0/System.Data.OleDb.xml", "system.data.oledb.8.0.1.nupkg.sha512", "system.data.oledb.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.SqlClient/4.8.6": {"sha512": "2Ij/LCaTQRyAi5lAv7UUTV9R2FobC8xN9mE0fXBZohum/xLl8IZVmE98Rq5ugQHjCgTBRKqpXRb4ORulRdA6Ig==", "type": "package", "path": "system.data.sqlclient/4.8.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net451/System.Data.SqlClient.dll", "lib/net46/System.Data.SqlClient.dll", "lib/net461/System.Data.SqlClient.dll", "lib/net461/System.Data.SqlClient.xml", "lib/netcoreapp2.1/System.Data.SqlClient.dll", "lib/netcoreapp2.1/System.Data.SqlClient.xml", "lib/netstandard1.2/System.Data.SqlClient.dll", "lib/netstandard1.2/System.Data.SqlClient.xml", "lib/netstandard1.3/System.Data.SqlClient.dll", "lib/netstandard1.3/System.Data.SqlClient.xml", "lib/netstandard2.0/System.Data.SqlClient.dll", "lib/netstandard2.0/System.Data.SqlClient.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net451/System.Data.SqlClient.dll", "ref/net46/System.Data.SqlClient.dll", "ref/net461/System.Data.SqlClient.dll", "ref/net461/System.Data.SqlClient.xml", "ref/netcoreapp2.1/System.Data.SqlClient.dll", "ref/netcoreapp2.1/System.Data.SqlClient.xml", "ref/netstandard1.2/System.Data.SqlClient.dll", "ref/netstandard1.2/System.Data.SqlClient.xml", "ref/netstandard1.2/de/System.Data.SqlClient.xml", "ref/netstandard1.2/es/System.Data.SqlClient.xml", "ref/netstandard1.2/fr/System.Data.SqlClient.xml", "ref/netstandard1.2/it/System.Data.SqlClient.xml", "ref/netstandard1.2/ja/System.Data.SqlClient.xml", "ref/netstandard1.2/ko/System.Data.SqlClient.xml", "ref/netstandard1.2/ru/System.Data.SqlClient.xml", "ref/netstandard1.2/zh-hans/System.Data.SqlClient.xml", "ref/netstandard1.2/zh-hant/System.Data.SqlClient.xml", "ref/netstandard1.3/System.Data.SqlClient.dll", "ref/netstandard1.3/System.Data.SqlClient.xml", "ref/netstandard1.3/de/System.Data.SqlClient.xml", "ref/netstandard1.3/es/System.Data.SqlClient.xml", "ref/netstandard1.3/fr/System.Data.SqlClient.xml", "ref/netstandard1.3/it/System.Data.SqlClient.xml", "ref/netstandard1.3/ja/System.Data.SqlClient.xml", "ref/netstandard1.3/ko/System.Data.SqlClient.xml", "ref/netstandard1.3/ru/System.Data.SqlClient.xml", "ref/netstandard1.3/zh-hans/System.Data.SqlClient.xml", "ref/netstandard1.3/zh-hant/System.Data.SqlClient.xml", "ref/netstandard2.0/System.Data.SqlClient.dll", "ref/netstandard2.0/System.Data.SqlClient.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll", "runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.xml", "runtimes/unix/lib/netstandard1.3/System.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/win/lib/net451/System.Data.SqlClient.dll", "runtimes/win/lib/net46/System.Data.SqlClient.dll", "runtimes/win/lib/net461/System.Data.SqlClient.dll", "runtimes/win/lib/net461/System.Data.SqlClient.xml", "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll", "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.xml", "runtimes/win/lib/netstandard1.3/System.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/System.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/win/lib/uap10.0.16299/System.Data.SqlClient.dll", "runtimes/win/lib/uap10.0.16299/System.Data.SqlClient.xml", "system.data.sqlclient.4.8.6.nupkg.sha512", "system.data.sqlclient.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Diagnostics.EventLog/8.0.1": {"sha512": "n1ZP7NM2Gkn/MgD8+eOT5MulMj6wfeQMNS2Pizvq5GHCZfjlFMXV2irQlQmJhwA2VABC57M0auudO89Iu2uRLg==", "type": "package", "path": "system.diagnostics.eventlog/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "lib/net462/System.Diagnostics.EventLog.dll", "lib/net462/System.Diagnostics.EventLog.xml", "lib/net6.0/System.Diagnostics.EventLog.dll", "lib/net6.0/System.Diagnostics.EventLog.xml", "lib/net7.0/System.Diagnostics.EventLog.dll", "lib/net7.0/System.Diagnostics.EventLog.xml", "lib/net8.0/System.Diagnostics.EventLog.dll", "lib/net8.0/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.8.0.1.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.PerformanceCounter/8.0.1": {"sha512": "9RfEDiEjlUADeThs8IPdDVTXSnPRSqjfgTQJALpmGFPKC0k2mbdufOXnb/9JZ4I0TkmxOfy3VTJxrHOJSs8cXg==", "type": "package", "path": "system.diagnostics.performancecounter/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.PerformanceCounter.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.PerformanceCounter.targets", "lib/net462/System.Diagnostics.PerformanceCounter.dll", "lib/net462/System.Diagnostics.PerformanceCounter.xml", "lib/net6.0/System.Diagnostics.PerformanceCounter.dll", "lib/net6.0/System.Diagnostics.PerformanceCounter.xml", "lib/net7.0/System.Diagnostics.PerformanceCounter.dll", "lib/net7.0/System.Diagnostics.PerformanceCounter.xml", "lib/net8.0/System.Diagnostics.PerformanceCounter.dll", "lib/net8.0/System.Diagnostics.PerformanceCounter.xml", "lib/netstandard2.0/System.Diagnostics.PerformanceCounter.dll", "lib/netstandard2.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net7.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net7.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.xml", "system.diagnostics.performancecounter.8.0.1.nupkg.sha512", "system.diagnostics.performancecounter.nuspec", "useSharedDesignerContext.txt"]}, "System.Drawing.Common/8.0.15": {"sha512": "qe9ADU4LlSDEluFw6UCvnhUX43iu1rfIi4u3cpQ37DjFDbwyWGRrDM5gypYj1IQuAVRnMHqOyFbCjtCvijN2cA==", "type": "package", "path": "system.drawing.common/8.0.15", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Drawing.Common.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Drawing.Common.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Drawing.Common.dll", "lib/net462/System.Drawing.Common.pdb", "lib/net462/System.Drawing.Common.xml", "lib/net6.0/System.Drawing.Common.dll", "lib/net6.0/System.Drawing.Common.pdb", "lib/net6.0/System.Drawing.Common.xml", "lib/net7.0/System.Drawing.Common.dll", "lib/net7.0/System.Drawing.Common.pdb", "lib/net7.0/System.Drawing.Common.xml", "lib/net8.0/System.Drawing.Common.dll", "lib/net8.0/System.Drawing.Common.pdb", "lib/net8.0/System.Drawing.Common.xml", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.pdb", "lib/netstandard2.0/System.Drawing.Common.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "system.drawing.common.8.0.15.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.AccessControl/4.7.0": {"sha512": "JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "type": "package", "path": "system.security.accesscontrol/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/netstandard1.3/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.xml", "ref/netstandard1.3/System.Security.AccessControl.dll", "ref/netstandard1.3/System.Security.AccessControl.xml", "ref/netstandard1.3/de/System.Security.AccessControl.xml", "ref/netstandard1.3/es/System.Security.AccessControl.xml", "ref/netstandard1.3/fr/System.Security.AccessControl.xml", "ref/netstandard1.3/it/System.Security.AccessControl.xml", "ref/netstandard1.3/ja/System.Security.AccessControl.xml", "ref/netstandard1.3/ko/System.Security.AccessControl.xml", "ref/netstandard1.3/ru/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Security.AccessControl.xml", "ref/netstandard2.0/System.Security.AccessControl.dll", "ref/netstandard2.0/System.Security.AccessControl.xml", "ref/uap10.0.16299/_._", "runtimes/win/lib/net46/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.Security.AccessControl.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.accesscontrol.4.7.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.Pkcs/8.0.1": {"sha512": "CoCRHFym33aUSf/NtWSVSZa99dkd0Hm7OCZUxORBjRB16LNhIEOf8THPqzIYlvKM0nNDAPTRBa1FxEECrgaxxA==", "type": "package", "path": "system.security.cryptography.pkcs/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.Pkcs.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Pkcs.targets", "lib/net462/System.Security.Cryptography.Pkcs.dll", "lib/net462/System.Security.Cryptography.Pkcs.xml", "lib/net6.0/System.Security.Cryptography.Pkcs.dll", "lib/net6.0/System.Security.Cryptography.Pkcs.xml", "lib/net7.0/System.Security.Cryptography.Pkcs.dll", "lib/net7.0/System.Security.Cryptography.Pkcs.xml", "lib/net8.0/System.Security.Cryptography.Pkcs.dll", "lib/net8.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.xml", "system.security.cryptography.pkcs.8.0.1.nupkg.sha512", "system.security.cryptography.pkcs.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.ProtectedData/8.0.0": {"sha512": "+TUFINV2q2ifyXauQXRwy4CiBhqvDEDZeVJU7qfxya4aRYOKzVBpN+4acx25VcPB9ywUN6C0n8drWl110PhZEg==", "type": "package", "path": "system.security.cryptography.protecteddata/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.ProtectedData.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.ProtectedData.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Security.Cryptography.ProtectedData.dll", "lib/net462/System.Security.Cryptography.ProtectedData.xml", "lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "lib/net6.0/System.Security.Cryptography.ProtectedData.xml", "lib/net7.0/System.Security.Cryptography.ProtectedData.dll", "lib/net7.0/System.Security.Cryptography.ProtectedData.xml", "lib/net8.0/System.Security.Cryptography.ProtectedData.dll", "lib/net8.0/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "system.security.cryptography.protecteddata.8.0.0.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Xml/8.0.2": {"sha512": "aDM/wm0ZGEZ6ZYJLzgqjp2FZdHbDHh6/OmpGfb7AdZ105zYmPn/83JRU2xLIbwgoNz9U1SLUTJN0v5th3qmvjA==", "type": "package", "path": "system.security.cryptography.xml/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.Xml.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Xml.targets", "lib/net462/System.Security.Cryptography.Xml.dll", "lib/net462/System.Security.Cryptography.Xml.xml", "lib/net6.0/System.Security.Cryptography.Xml.dll", "lib/net6.0/System.Security.Cryptography.Xml.xml", "lib/net7.0/System.Security.Cryptography.Xml.dll", "lib/net7.0/System.Security.Cryptography.Xml.xml", "lib/net8.0/System.Security.Cryptography.Xml.dll", "lib/net8.0/System.Security.Cryptography.Xml.xml", "lib/netstandard2.0/System.Security.Cryptography.Xml.dll", "lib/netstandard2.0/System.Security.Cryptography.Xml.xml", "system.security.cryptography.xml.8.0.2.nupkg.sha512", "system.security.cryptography.xml.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Principal.Windows/4.7.0": {"sha512": "ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "type": "package", "path": "system.security.principal.windows/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.4.7.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ServiceModel.Http/8.1.2": {"sha512": "2HoneRUCHB8kKQcuAladR16mIHuyi5f9DrrNnTjkaICv5X7YuwoI7GSrp9GTkRSxC4ddNcjMdwE01Rm0sPgoMw==", "type": "package", "path": "system.servicemodel.http/8.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.ServiceModel.Http.dll", "lib/net462/System.ServiceModel.Http.pdb", "lib/net8.0/System.ServiceModel.Http.dll", "lib/net8.0/System.ServiceModel.Http.pdb", "lib/net8.0/cs/System.ServiceModel.Http.resources.dll", "lib/net8.0/de/System.ServiceModel.Http.resources.dll", "lib/net8.0/es/System.ServiceModel.Http.resources.dll", "lib/net8.0/fr/System.ServiceModel.Http.resources.dll", "lib/net8.0/it/System.ServiceModel.Http.resources.dll", "lib/net8.0/ja/System.ServiceModel.Http.resources.dll", "lib/net8.0/ko/System.ServiceModel.Http.resources.dll", "lib/net8.0/pl/System.ServiceModel.Http.resources.dll", "lib/net8.0/pt-BR/System.ServiceModel.Http.resources.dll", "lib/net8.0/ru/System.ServiceModel.Http.resources.dll", "lib/net8.0/tr/System.ServiceModel.Http.resources.dll", "lib/net8.0/zh-Hans/System.ServiceModel.Http.resources.dll", "lib/net8.0/zh-Hant/System.ServiceModel.Http.resources.dll", "lib/netstandard2.0/System.ServiceModel.Http.dll", "lib/netstandard2.0/System.ServiceModel.Http.pdb", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/net462/System.ServiceModel.Http.dll", "ref/net8.0/System.ServiceModel.Http.dll", "ref/netcoreapp2.0/_._", "ref/netstandard2.0/System.ServiceModel.Http.dll", "system.servicemodel.http.8.1.2.nupkg.sha512", "system.servicemodel.http.nuspec"]}, "System.ServiceModel.NetFramingBase/8.1.2": {"sha512": "XDNsiI4Lrv4scb10salZNbUDJNYj3Qm39KeN9uulKXbgcxF/fNC+hCrLDAhNz/rIqxOeRB/JCfQ4A/Ha0hFAWg==", "type": "package", "path": "system.servicemodel.netframingbase/8.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net8.0/System.ServiceModel.NetFramingBase.dll", "lib/net8.0/System.ServiceModel.NetFramingBase.pdb", "lib/net8.0/cs/System.ServiceModel.NetFramingBase.resources.dll", "lib/net8.0/de/System.ServiceModel.NetFramingBase.resources.dll", "lib/net8.0/es/System.ServiceModel.NetFramingBase.resources.dll", "lib/net8.0/fr/System.ServiceModel.NetFramingBase.resources.dll", "lib/net8.0/it/System.ServiceModel.NetFramingBase.resources.dll", "lib/net8.0/ja/System.ServiceModel.NetFramingBase.resources.dll", "lib/net8.0/ko/System.ServiceModel.NetFramingBase.resources.dll", "lib/net8.0/pl/System.ServiceModel.NetFramingBase.resources.dll", "lib/net8.0/pt-BR/System.ServiceModel.NetFramingBase.resources.dll", "lib/net8.0/ru/System.ServiceModel.NetFramingBase.resources.dll", "lib/net8.0/tr/System.ServiceModel.NetFramingBase.resources.dll", "lib/net8.0/zh-Hans/System.ServiceModel.NetFramingBase.resources.dll", "lib/net8.0/zh-Hant/System.ServiceModel.NetFramingBase.resources.dll", "ref/net8.0/System.ServiceModel.NetFramingBase.dll", "system.servicemodel.netframingbase.8.1.2.nupkg.sha512", "system.servicemodel.netframingbase.nuspec"]}, "System.ServiceModel.NetTcp/8.1.2": {"sha512": "+H4KpATeOPxGfl8JlMpv+a7h6ZIItIhHXJRr+0/KKAP49TjHXybaNwFrUbpasAMVE+D+8M96GxRevzcqvahfAQ==", "type": "package", "path": "system.servicemodel.nettcp/8.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.ServiceModel.NetTcp.dll", "lib/net462/System.ServiceModel.NetTcp.pdb", "lib/net8.0/System.ServiceModel.NetTcp.dll", "lib/net8.0/System.ServiceModel.NetTcp.pdb", "lib/net8.0/cs/System.ServiceModel.NetTcp.resources.dll", "lib/net8.0/de/System.ServiceModel.NetTcp.resources.dll", "lib/net8.0/es/System.ServiceModel.NetTcp.resources.dll", "lib/net8.0/fr/System.ServiceModel.NetTcp.resources.dll", "lib/net8.0/it/System.ServiceModel.NetTcp.resources.dll", "lib/net8.0/ja/System.ServiceModel.NetTcp.resources.dll", "lib/net8.0/ko/System.ServiceModel.NetTcp.resources.dll", "lib/net8.0/pl/System.ServiceModel.NetTcp.resources.dll", "lib/net8.0/pt-BR/System.ServiceModel.NetTcp.resources.dll", "lib/net8.0/ru/System.ServiceModel.NetTcp.resources.dll", "lib/net8.0/tr/System.ServiceModel.NetTcp.resources.dll", "lib/net8.0/zh-Hans/System.ServiceModel.NetTcp.resources.dll", "lib/net8.0/zh-Hant/System.ServiceModel.NetTcp.resources.dll", "lib/netstandard2.0/System.ServiceModel.NetTcp.dll", "lib/netstandard2.0/System.ServiceModel.NetTcp.pdb", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/net462/System.ServiceModel.NetTcp.dll", "ref/net8.0/System.ServiceModel.NetTcp.dll", "ref/netcoreapp2.0/_._", "ref/netstandard2.0/System.ServiceModel.NetTcp.dll", "system.servicemodel.nettcp.8.1.2.nupkg.sha512", "system.servicemodel.nettcp.nuspec"]}, "System.ServiceModel.Primitives/8.1.2": {"sha512": "xlJ07FAUSDjPL/GhvVY1/KVPWn2ce056X4nHPwqAa5rkNLiNN5rqh6VcgMUoF6J7ckwhkVJ1vVx/K/47nyyR9g==", "type": "package", "path": "system.servicemodel.primitives/8.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.ServiceModel.Duplex.dll", "lib/net462/System.ServiceModel.Duplex.pdb", "lib/net462/System.ServiceModel.Primitives.dll", "lib/net462/System.ServiceModel.Primitives.pdb", "lib/net462/System.ServiceModel.Security.dll", "lib/net462/System.ServiceModel.Security.pdb", "lib/net8.0/System.ServiceModel.Duplex.dll", "lib/net8.0/System.ServiceModel.Duplex.pdb", "lib/net8.0/System.ServiceModel.Primitives.dll", "lib/net8.0/System.ServiceModel.Primitives.pdb", "lib/net8.0/System.ServiceModel.Security.dll", "lib/net8.0/System.ServiceModel.Security.pdb", "lib/net8.0/System.ServiceModel.dll", "lib/net8.0/System.ServiceModel.pdb", "lib/net8.0/cs/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/de/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/es/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/fr/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/it/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/ja/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/ko/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/pl/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/pt-BR/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/ru/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/tr/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/zh-Hans/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/zh-Hant/System.ServiceModel.Primitives.resources.dll", "lib/netstandard2.0/System.ServiceModel.Duplex.dll", "lib/netstandard2.0/System.ServiceModel.Duplex.pdb", "lib/netstandard2.0/System.ServiceModel.Primitives.dll", "lib/netstandard2.0/System.ServiceModel.Primitives.pdb", "lib/netstandard2.0/System.ServiceModel.Security.dll", "lib/netstandard2.0/System.ServiceModel.Security.pdb", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/net462/System.ServiceModel.Duplex.dll", "ref/net462/System.ServiceModel.Primitives.dll", "ref/net462/System.ServiceModel.Security.dll", "ref/net8.0/System.ServiceModel.Duplex.dll", "ref/net8.0/System.ServiceModel.Primitives.dll", "ref/net8.0/System.ServiceModel.Security.dll", "ref/net8.0/System.ServiceModel.dll", "ref/netcoreapp2.0/_._", "ref/netstandard2.0/System.ServiceModel.Duplex.dll", "ref/netstandard2.0/System.ServiceModel.Primitives.dll", "ref/netstandard2.0/System.ServiceModel.Security.dll", "system.servicemodel.primitives.8.1.2.nupkg.sha512", "system.servicemodel.primitives.nuspec"]}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["DevExpress.Document.Processor >= 25.1.3", "DevExpress.Win.BonusSkins >= 25.1.3", "DevExpress.Win.Design >= 25.1.3", "System.Data.OleDb >= 8.0.1"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\DevExpress Demos 25.1\\Components\\Office File API\\CS\\OfficeFileAPIDemos\\OfficeFileAPIDemos.NetCore.Desktop.csproj", "projectName": "OfficeFileAPIDemos", "projectPath": "C:\\Users\\<USER>\\Documents\\DevExpress Demos 25.1\\Components\\Office File API\\CS\\OfficeFileAPIDemos\\OfficeFileAPIDemos.NetCore.Desktop.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\DevExpress Demos 25.1\\Components\\Office File API\\CS\\OfficeFileAPIDemos\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 25.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 25.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 25.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"DevExpress.Document.Processor": {"target": "Package", "version": "[25.1.3, )"}, "DevExpress.Win.BonusSkins": {"target": "Package", "version": "[25.1.3, )"}, "DevExpress.Win.Design": {"target": "Package", "version": "[25.1.3, )"}, "System.Data.OleDb": {"target": "Package", "version": "[8.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}