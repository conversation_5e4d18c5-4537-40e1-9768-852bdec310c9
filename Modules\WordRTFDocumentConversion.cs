﻿using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using DevExpress.DXperience.Demos;
using DevExpress.XtraPrintingLinks;
using DevExpress.XtraRichEdit;

namespace DevExpress.Docs.Demos {
    public partial class WordRTFDocumentConversion : TutorialControlBase {
        const string openFileDialogFilter =
            "All Supported Files (*.rtf;*.doc;*.docx;*.html;*.mht;*.txt;*.xml;*.odt;*.epub)|*.rtf;*.doc;*.docx;*.html;*.mht;*.txt;*.xml;*.odt;*.epub|" +
            "Word Documents (*.docx)|*.docx|" +
            "Word 97-2003 Documents (*.doc)|*.doc|" +
            "Rich Text Format (*.rtf)|*.rtf|" +
            "HTML Files (*.html)|*.html|" +
            "MHT Files (*.mht)|*.mht|" +
            "Text Files (*.txt)|*.txt|" +
            "XML Files (*.xml)|*.xml|" +
            "OpenDocument Text (*.odt)|*.odt|" +
            "Electronic Publication (*.epub)|*.epub|" +
            "All files (*.*)|*.*";

        readonly PrintableComponentLinkBase link;
        RichEditDocumentServer documentServer;
        public WordRTFDocumentConversion() {
            InitializeComponent();

            printPreviewControl.PrintingSystem = new DevExpress.XtraPrinting.PrintingSystem();
            link = new DevExpress.XtraPrintingLinks.PrintableComponentLinkBase(printPreviewControl.PrintingSystem);
            edtFilePath.Text = DemoUtils.GetRelativePath("Scientific.docx");
            edtSaveTo.Text = DevExpress.Data.Utils.SafeEnvironment.MyDocuments;
            LoadDocument();
        }
        void btnExport_Click(object sender, EventArgs e) {
            string format = ((Control)sender).Tag.ToString();
            string filePath = edtFilePath.Text;
            string fileName = Path.GetFileNameWithoutExtension(filePath);
            string pathString = Path.GetFullPath(Path.Combine(edtSaveTo.Text, fileName));
            string resultFilePath = string.Empty;
            if(format == "rtf") {
                resultFilePath = pathString + ".rtf";
                documentServer.SaveDocument(resultFilePath, DocumentFormat.Rtf);
            }
            else if(format == "txt") {
                resultFilePath = pathString + ".txt";
                documentServer.SaveDocument(resultFilePath, DocumentFormat.PlainText);
            }
            else if(format == "html") {
                resultFilePath = pathString + ".html";
                documentServer.SaveDocument(resultFilePath, DocumentFormat.Html);
            }
            else if(format == "mht") {
                resultFilePath = pathString + ".mht";
                documentServer.SaveDocument(resultFilePath, DocumentFormat.Mht);
            }
            else if(format == "docx") {
                resultFilePath = pathString + ".docx";
                documentServer.SaveDocument(resultFilePath, DocumentFormat.OpenXml);
            }
            else if(format == "odt") {
                resultFilePath = pathString + ".odt";
                documentServer.SaveDocument(resultFilePath, DocumentFormat.OpenDocument);
            }
            else if(format == "xml") {
                resultFilePath = pathString + ".xml";
                documentServer.SaveDocument(resultFilePath, DocumentFormat.WordML);
            }
            else if(format == "epub") {
                resultFilePath = pathString + ".epub";
                documentServer.SaveDocument(resultFilePath, DocumentFormat.ePub);
            }
            else if(format == "doc") {
                resultFilePath = pathString + ".doc";
                documentServer.SaveDocument(resultFilePath, DocumentFormat.Doc);
            }
            else if(format == "pdf") {
                resultFilePath = pathString + ".pdf";
                documentServer.ExportToPdf(resultFilePath);
            }
            if(!string.IsNullOrEmpty(resultFilePath))
                DemoUtils.PreviewDocument(resultFilePath);
        }
        void LoadDocument() {
            if(documentServer == null) {
                documentServer = new RichEditDocumentServer();
                new RichEditDemoExceptionsHandler(documentServer).Install();
            }
            string path = edtFilePath.Text;
            documentServer.LoadDocument(path);
            link.Component = documentServer;
            link.CreateDocument();
        }
        void edtFilePath_ButtonClick(object sender, XtraEditors.Controls.ButtonPressedEventArgs e) {
            ChooseFileToOpen(String.Empty);
        }
        void ChooseFileToOpen(string initialPath) {
            using(OpenFileDialog openFileDialog = new OpenFileDialog()) {
                openFileDialog.Filter = openFileDialogFilter;
                if(!String.IsNullOrEmpty(initialPath))
                    openFileDialog.InitialDirectory = initialPath;
                if(openFileDialog.ShowDialog() != DialogResult.OK)
                    return;
                edtFilePath.Text = openFileDialog.FileName;
                LoadDocument();
            }
        }
        void edtFilePath_KeyUp(object sender, KeyEventArgs e) {
            if(e.KeyCode != Keys.Enter)
                return;
            FileInfo fileInfo = new FileInfo(edtFilePath.Text);
            if(fileInfo.Exists) {
                LoadDocument();
                return;
            }
            ChooseFileToOpen(edtFilePath.Text);
        }
        void ChooseFolderToSave() {
            using(FolderBrowserDialog openFileDialog = new FolderBrowserDialog()) {
                if(openFileDialog.ShowDialog() != DialogResult.OK)
                    return;
                edtSaveTo.Text = openFileDialog.SelectedPath;
            }
        }
        void edtSaveTo_ButtonClick(object sender, XtraEditors.Controls.ButtonPressedEventArgs e) {
            ChooseFolderToSave();
        }
    }

    sealed class RichEditDemoExceptionsHandler {
        readonly RichEditDocumentServer documentServer;
        public RichEditDemoExceptionsHandler(RichEditDocumentServer documentServer) {
            this.documentServer = documentServer;
        }
        public void Install() {
            if(documentServer != null)
                documentServer.UnhandledException += OnRichEditControlUnhandledException;
        }
        void OnRichEditControlUnhandledException(object sender, RichEditUnhandledExceptionEventArgs e) {
            try {
                if(e.Exception != null)
                    throw e.Exception;
            }
            catch(RichEditUnsupportedFormatException ex) {
                DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Error);
                e.Handled = true;
            }
            catch(ExternalException ex) {
                DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Error);
                e.Handled = true;
            }
            catch(IOException ex) {
                DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Error);
                e.Handled = true;
            }
        }
    }
}
