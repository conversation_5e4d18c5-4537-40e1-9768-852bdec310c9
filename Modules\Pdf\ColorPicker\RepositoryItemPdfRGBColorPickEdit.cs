﻿using System.Drawing;
using DevExpress.Accessibility;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Drawing;
using DevExpress.XtraEditors.Registrator;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraEditors.ViewInfo;

namespace DevExpress.Docs.Demos {
    [UserRepositoryItem("RegisterPdfRGBColorPickEdit")]
    public class RepositoryItemPdfRGBColorPickEdit : RepositoryItemColorPickEdit {
        static RepositoryItemPdfRGBColorPickEdit() { RegisterCustomEdit(); }
        public RepositoryItemPdfRGBColorPickEdit() { }
        public const string CustomEditName = "PdfRGBColorPickEdit";
        public override string EditorTypeName { get { return CustomEditName; } }
        public static void RegisterCustomEdit() {
            EditorRegistrationInfo.Default.Editors.Add(new EditorClassInfo(CustomEditName, typeof(PdfRGBColorPickEdit), typeof(RepositoryItemPdfRGBColorPickEdit), typeof(PdfRGBColorPickEditViewInfo), new ColorEditPainter(), true, null, typeof(TextEditAccessible)));
        }
        public override BaseEdit CreateEditor() {
            return new PdfRGBColorPickEdit();
        }
        public override BaseEditViewInfo CreateViewInfo() {
            return new PdfRGBColorPickEditViewInfo(this);
        }
        protected override string GetColorDisplayText(Color editValue) {
            return editValue.ToString();
        }
    }
}
