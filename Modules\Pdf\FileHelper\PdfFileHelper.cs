﻿using System;
using System.IO;
using System.Security.Cryptography;
using System.Windows.Forms;
using DevExpress.Pdf;
using DevExpress.XtraEditors;

namespace DevExpress.Docs.Demos {
    public abstract class PdfFileHelper {
        const string SaveErrorMessage = "Unable to save the PDF document.\r\n{0}";
        public const string DemoOpeningErrorMessage = "The demo data has been corrupted.";

        protected static string ShowFileDialog<T>() where T : FileDialog, new() {
            using(T fileDialog = new T()) {
                fileDialog.Filter = "PDF Files (*.pdf)|*.pdf";
                fileDialog.RestoreDirectory = true;
                if(fileDialog.ShowDialog() == DialogResult.OK)
                    return fileDialog.FileName;
                return null;
            }
        }

        protected abstract string Creator { get; set; }
        protected abstract string Producer { get; set; }

        public bool LoadDemoDocument(string documentName) {
            return LoadDemoDocument(documentName, false);
        }
        public bool LoadDemoDocument(string documentName, bool detach) {
            try {
                LoadDocument(DemoUtils.GetRelativePath(documentName), detach);
                return true;
            }
            catch {
                XtraMessageBox.Show(DemoOpeningErrorMessage, "Error");
            }
            return false;
        }
        public bool SaveDocument() {
            return SaveDocument(new PdfSaveOptions());
        }
        public bool SaveDocument(PdfSaveOptions options) {
            string fileName = ShowFileDialog<SaveFileDialog>();
            if(!String.IsNullOrEmpty(fileName))
                try {
                    Creator = "PDF Document Processor Demo";
                    Producer = "Developer Express Inc., " + AssemblyInfo.Version;
                    SaveDocument(fileName, options);
                    return true;
                }
                catch(CryptographicException exception) {
                    XtraMessageBox.Show(exception.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                catch {
                    XtraMessageBox.Show(String.Format(SaveErrorMessage, fileName), "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            return false;
        }
        public void LoadDocument(string path) {
            LoadDocument(path, false);
        }

        public abstract void LoadDocument(string path, bool detach);
        public abstract void LoadDocument(Stream stream);
        protected abstract void SaveDocument(string filePath, PdfSaveOptions options);
    }
}
