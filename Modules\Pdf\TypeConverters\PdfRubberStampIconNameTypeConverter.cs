﻿using System.Collections.Generic;
using System.ComponentModel;
using DevExpress.Pdf;

namespace DevExpress.Docs.Demos {
    public class PdfRubberStampIconNameTypeConverter : TypeConverter {
        public override bool GetStandardValuesSupported(ITypeDescriptorContext context) {
            return true;
        }
        public override bool GetStandardValuesExclusive(ITypeDescriptorContext context) {
            return true;
        }
        public override StandardValuesCollection GetStandardValues(ITypeDescriptorContext context) {
            return new StandardValuesCollection(new List<string>() { PdfRubberStampAnnotationIconName.Draft, PdfRubberStampAnnotationIconName.Approved, PdfRubberStampAnnotationIconName.AsIs, PdfRubberStampAnnotationIconName.Confidential, PdfRubberStampAnnotationIconName.DApproved, PdfRubberStampAnnotationIconName.DConfidential, PdfRubberStampAnnotationIconName.Departmental, PdfRubberStampAnnotationIconName.DReceived, PdfRubberStampAnnotationIconName.DReviewed, PdfRubberStampAnnotationIconName.DRevised, PdfRubberStampAnnotationIconName.Experimental, PdfRubberStampAnnotationIconName.Expired, PdfRubberStampAnnotationIconName.Final, PdfRubberStampAnnotationIconName.ForComment, PdfRubberStampAnnotationIconName.ForPublicRelease, PdfRubberStampAnnotationIconName.NotApproved, PdfRubberStampAnnotationIconName.NotForPublicRelease, PdfRubberStampAnnotationIconName.SHAccepted, PdfRubberStampAnnotationIconName.SHInitialHere, PdfRubberStampAnnotationIconName.SHRejected, PdfRubberStampAnnotationIconName.SHSignHere, PdfRubberStampAnnotationIconName.SHWitness, PdfRubberStampAnnotationIconName.Sold, PdfRubberStampAnnotationIconName.TopSecret });
        }
    }
}
