﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using DevExpress.Docs.Demos.Pdf.Forms;
using DevExpress.DXperience.Demos;
using DevExpress.Pdf;
using DevExpress.Utils.Drawing;
using DevExpress.XtraBars;
using DevExpress.XtraPdfViewer.Commands;
using DevExpress.XtraSplashScreen;
using DevExpress.XtraTreeList;
using DevExpress.XtraTreeList.Nodes;

namespace DevExpress.Docs.Demos {
    public partial class PdfTextMarkupAnnotations : TutorialControlBase {
        class TreeListItem {
            public int _id;
            readonly int _pageNumber;
            readonly PdfMarkupAnnotationData _annotationData;

            public int Id { get { return _id; } }
            public int PageNumber { get { return _pageNumber; } }
            public PdfMarkupAnnotationData AnnotationData { get { return _annotationData; } }
            public string Title {
                get {
                    if(_annotationData == null)
                        return "Page " + _id;
                    if(!String.IsNullOrEmpty(_annotationData.Name))
                        return _annotationData.Name;
                    if(!String.IsNullOrEmpty(_annotationData.Subject))
                        return _annotationData.Subject;
                    if(!String.IsNullOrEmpty(_annotationData.Contents))
                        return _annotationData.Contents;
                    if(!String.IsNullOrEmpty(_annotationData.Author))
                        return _annotationData.Author;
                    return String.Format("X = {0:0}, Y = {1:0}, Width = {2:0}, Height = {3:0}",
                        _annotationData.Bounds.Left, _annotationData.Bounds.Top, _annotationData.Bounds.Width, _annotationData.Bounds.Height);
                }
            }

            public TreeListItem(int pageNumber) {
                _id = pageNumber;
            }
            public TreeListItem(int id, int pageNumber, PdfMarkupAnnotationData annotationData) {
                _id = id;
                _pageNumber = pageNumber;
                _annotationData = annotationData;
            }
        }

        const string documentName = "TextMarkup.pdf";
        const string annotationDataPropertyName = "AnnotationData";

        static PdfTextMarkupAnnotations() {
            TypeDescriptor.AddAttributes(typeof(PdfRectangle), new TypeConverterAttribute(typeof(PdfRectangleTypeConverter)));
            TypeDescriptor.AddAttributes(typeof(PdfRGBColor), new TypeConverterAttribute(typeof(PdfRGBColorTypeConverter)));
        }

        readonly PdfDocumentProcessor _documentProcessor = new PdfDocumentProcessor();
        readonly PdfDocumentProcessorAndViewerFileHelper _fileHelper;
        bool _cancelAnnotationsRetrieving;
        readonly string _tempFileName;

        public override bool NoGap { get { return true; } }

        public PdfTextMarkupAnnotations() {
            InitializeComponent();
            _tempFileName = Path.GetTempPath() + documentName;
            _fileHelper = new PdfDocumentProcessorAndViewerFileHelper(_documentProcessor, pdfViewer);
            Enabled = _fileHelper.LoadDemoDocument(documentName);
            pdfViewer.Paint += OnPdfViewerPaint;
            propertyGridControl.DataSourceChanged += OnPropertyGridControlDataSourceChanged;
            UpdateTreeList();
        }
        void OnPropertyGridControlDataSourceChanged(object sender, EventArgs e) {
            string[] fieldNames = new string[] { "Quads", "Replies", "Reviews" };
            foreach(string fieldName in fieldNames) {
                XtraVerticalGrid.Rows.BaseRow row = propertyGridControl.GetRowByFieldName(fieldName);
                if(row != null)
                    row.Visible = false;
            }
        }
        public void CancelAnnotationsRetrieving() {
            _cancelAnnotationsRetrieving = true;
        }
        void UpdateSelectedAnnotation() {
            TreeListNode node = treeList.FocusedNode;
            if(node != null) {
                PdfTextMarkupAnnotationData data = node[annotationDataPropertyName] as PdfTextMarkupAnnotationData;
                if(data != null) {
                    int pageNumber = (int)node.ParentNode["Id"];
                    PdfDocumentPosition bottomRight = new PdfDocumentPosition(pageNumber, data.Bounds.BottomRight);
                    pdfViewer.EnsureVisibility(bottomRight);
                    pdfViewer.Invalidate();
                }
            }
        }
        void UpdateTreeList() {
            try {
                treeList.DataSource = null;
                SplashScreenManager.ShowForm(this, typeof(PdfProgressForm), true, true, ParentFormState.Locked);
                SplashScreenManager manager = SplashScreenManager.Default;
                manager.SetWaitFormCaption("Retrieving annotations...");
                int pageCount = _documentProcessor.Document.Pages.Count;
                List<TreeListItem> dataSource = new List<TreeListItem>();
                int id = pageCount + 1;
                _cancelAnnotationsRetrieving = false;
                for(int pageNumber = 1; pageNumber <= pageCount && !_cancelAnnotationsRetrieving; pageNumber++) {
                    IList<PdfTextMarkupAnnotationData> annotationData = _documentProcessor.GetMarkupAnnotationData(pageNumber)
                        .OfType<PdfTextMarkupAnnotationData>()
                        .ToList();
                    if(annotationData.Count > 0) {
                        dataSource.Add(new TreeListItem(pageNumber));
                        foreach(PdfMarkupAnnotationData item in annotationData)
                            dataSource.Add(new TreeListItem(id++, pageNumber, item));
                    }
                    manager.SendCommand(WaitFormCommand.CmdId, Convert.ToInt32(100.0 * pageNumber / pageCount));
                    manager.SendCommand(WaitFormCommand.CancelId, this);
                }
                if(dataSource.Count == 0)
                    propertyGridControl.Hide();
                else
                    propertyGridControl.Show();
                if(!_cancelAnnotationsRetrieving) {
                    treeList.DataSource = dataSource;
                    treeList.ExpandAll();
                }
            }
            finally {
                SplashScreenManager.CloseForm();
            }
        }
        void ReloadDocument() {
            float horizontalPosition = pdfViewer.HorizontalScrollPosition;
            float verticalPosition = pdfViewer.VerticalScrollPosition;
            try {
                if(_documentProcessor.Document != null) {
                    pdfViewer.CloseDocument();
                    _documentProcessor.SaveDocument(_tempFileName);
                }
                pdfViewer.LoadDocument(_tempFileName);
            }
            catch {
            }
            pdfViewer.HorizontalScrollPosition = horizontalPosition;
            pdfViewer.VerticalScrollPosition = verticalPosition;
        }
        void DeleteAnnotation() {
            TreeListNode node = treeList.FocusedNode;
            if(node != null) {
                PdfTextMarkupAnnotationData data = node[annotationDataPropertyName] as PdfTextMarkupAnnotationData;
                if(data != null) {
                    _documentProcessor.DeleteMarkupAnnotation(data);
                    UpdateTreeList();
                    ReloadDocument();
                }
            }
        }
        void OnButtonOpenClick(object sender, EventArgs e) {
            _fileHelper.LoadDocumentWithDialog();
            UpdateTreeList();
        }
        void OnButtonFindClick(object sender, EventArgs e) {
            PdfFindAndMarkupForm form = new PdfFindAndMarkupForm();
            if(form.ShowDialog(this) == DialogResult.OK) {
                string textToFind = form.TextToFind;
                if(!String.IsNullOrEmpty(textToFind)) {
                    PdfTextMarkupAnnotationType markupType = form.MarkupType;
                    PdfTextSearchResults searchResults = _documentProcessor.FindText(textToFind);
                    if(searchResults.Status == PdfTextSearchStatus.Found) {
                        while(searchResults.Status == PdfTextSearchStatus.Found) {
                            _documentProcessor.AddTextMarkupAnnotation(searchResults.PageNumber, searchResults.Rectangles, markupType);
                            searchResults = _documentProcessor.FindText(textToFind);
                        }
                        UpdateTreeList();
                        ReloadDocument();
                    }
                }
            }
        }
        void OnPdfViewerPaint(object sender, PaintEventArgs e) {
            TreeListNode node = treeList.FocusedNode;
            if(node != null) {
                PdfTextMarkupAnnotationData data = node[annotationDataPropertyName] as PdfTextMarkupAnnotationData;
                if(data != null) {
                    int pageNumber = (int)node.ParentNode["Id"];
                    PdfDocumentPosition topLeft = new PdfDocumentPosition(pageNumber, data.Bounds.TopLeft);
                    PdfDocumentPosition bottomRight = new PdfDocumentPosition(pageNumber, data.Bounds.BottomRight);
                    PointF point1 = pdfViewer.GetClientPoint(topLeft);
                    PointF point2 = pdfViewer.GetClientPoint(bottomRight);
                    using(GraphicsCache cache = new GraphicsCache(e))
                        cache.FillRectangle(Color.FromArgb(128, SystemColors.Highlight), RectangleF.FromLTRB(point1.X, point1.Y, point2.X, point2.Y));
                }
            }
        }
        void OnButtonSaveAsClick(object sender, EventArgs e) {
            new PdfSaveAsFileCommand(pdfViewer).Execute();
        }
        void OnFocusedNodeChanged(object sender, FocusedNodeChangedEventArgs e) {
            if(e.Node != null) {
                propertyGridControl.SelectedObject = e.Node[annotationDataPropertyName] as PdfTextMarkupAnnotationData;
                UpdateSelectedAnnotation();
            }
            else
                propertyGridControl.SelectedObject = null;
        }
        void OnTreeListKeyDown(object sender, KeyEventArgs e) {
            if(e.KeyCode == Keys.Delete)
                DeleteAnnotation();
        }
        void OnTreeListMouseDown(object sender, MouseEventArgs e) {
            if(e.Button == System.Windows.Forms.MouseButtons.Right) {
                TreeListHitInfo hitInfo = treeList.CalcHitInfo(e.Location);
                if(hitInfo.Node != null) {
                    treeList.SetFocusedNode(hitInfo.Node);
                    popupMenu.ShowPopup(Control.MousePosition);
                }
            }
        }
        void OnCellValueChanged(object sender, XtraVerticalGrid.Events.CellValueChangedEventArgs e) {
            ReloadDocument();
            UpdateSelectedAnnotation();
        }
        void OnDeleteAnnotationItemClick(object sender, ItemClickEventArgs e) {
            DeleteAnnotation();
        }
        protected override void Dispose(bool disposing) {
            if(disposing) {
                if(components != null)
                    components.Dispose();
                _documentProcessor.Dispose();
                _fileHelper.Dispose();
                pdfViewer.Paint -= OnPdfViewerPaint;
                propertyGridControl.DataSourceChanged -= OnPropertyGridControlDataSourceChanged;
            }
            base.Dispose(disposing);
        }
    }
}
