﻿using System;
using System.ComponentModel;
using System.Globalization;
using DevExpress.Pdf;

namespace DevExpress.Docs.Demos {
    public class PdfAcroFormButtonStyleConverter : NullableConverter {
        static readonly string NullValue = "(none)";

        public PdfAcroFormButtonStyleConverter() : base(typeof(PdfAcroFormButtonStyle?)) { }
        public override object ConvertTo(ITypeDescriptorContext context, CultureInfo culture, object value, Type destinationType) {
            if(value == null)
                return NullValue;
            return base.ConvertTo(context, culture, value, destinationType);
        }
        public override object ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, object value) {
            if(value.ToString() == NullValue)
                return null;
            return base.ConvertFrom(context, culture, value);
        }
    }
}
