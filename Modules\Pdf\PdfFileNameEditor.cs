﻿using System;
using System.ComponentModel;
using System.Drawing.Design;
using System.Windows.Forms;

namespace DevExpress.Docs.Demos {
    public class PdfFileNameEditor : UITypeEditor {
        string customFilter = "PDF files(*.pdf)|*.pdf";

        public PdfFileNameEditor() {
        }

        [DefaultValue("PDF files(*.pdf)|*.pdf")]
        public string CustomFilter {
            get { return customFilter; }
            set { customFilter = value; }
        }
        public override UITypeEditorEditStyle GetEditStyle(ITypeDescriptorContext context) {
            return UITypeEditorEditStyle.Modal;
        }
        public override object EditValue(ITypeDescriptorContext context, IServiceProvider provider, object value) {
            using(OpenFileDialog dialog = new OpenFileDialog()) {
                dialog.Filter = CustomFilter;
                if(dialog.ShowDialog() == DialogResult.OK)
                    value = dialog.FileName;
            }
            return value;
        }
    }
}
