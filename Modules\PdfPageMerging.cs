﻿using System;
using System.IO;
using DevExpress.DXperience.Demos;
using DevExpress.Pdf;

namespace DevExpress.Docs.Demos {
    public partial class PdfPageMerging : TutorialControlBase {
        const string largeFileMessage = "Not enough memory to append a file.";

        readonly PdfDocumentProcessor documentProcessor = new PdfDocumentProcessor();
        readonly PdfDocumentProcessorAndViewerFileHelper fileHelper;

        public override bool NoGap { get { return true; } }

        public PdfPageMerging() {
            InitializeComponent();
            fileHelper = new PdfDocumentProcessorAndViewerFileHelper(documentProcessor, pdfViewer);
            Enabled = fileHelper.LoadDemoDocument("PageMerging.pdf", true);
        }
        void OnButtonAppendClick(object sender, EventArgs e) {
            if(fileHelper.AppendDocument()) {
                pdfViewer.ScrollVertical(Int32.MaxValue);
                saveButton.Enabled = true;
            }
        }
        void OnButtonOpenClick(object sender, EventArgs e) {
            fileHelper.LoadDocumentWithDialog();
            saveButton.Enabled = true;
        }
        void OnSaveButtonClick(object sender, EventArgs e) {
            fileHelper.SaveDocument();
        }
        void OnNewButtonClick(object sender, EventArgs e) {
            using(MemoryStream stream = new MemoryStream()) {
                documentProcessor.CreateEmptyDocument(stream);
                documentProcessor.CloseDocument();
                stream.Position = 0;
                pdfViewer.LoadDocument(stream);
                saveButton.Enabled = false;
            }
        }
        protected override void Dispose(bool disposing) {
            if(disposing) {
                if(components != null)
                    components.Dispose();
                documentProcessor.Dispose();
                fileHelper.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
