﻿using System.Drawing;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraEditors.ViewInfo;

namespace DevExpress.Docs.Demos {
    public class PdfRGBColorPickEditViewInfo : ColorEditViewInfo {
        public PdfRGBColorPickEditViewInfo(RepositoryItem item) : base(item) { }

        public override Color Color {
            get { return PdfRGBColorConverter.ToColor(EditValue); }
        }
        public override string DisplayText {
            get { return PdfRGBColorConverter.ToString(Color); }
        }
        public override void UpdateEditValue() {
            if(OwnerEdit != null) {
                EditValue = OwnerEdit.EditValue;
                if(RefreshDisplayText) {
                    OnEditValueChanged();
                }
            }
        }

    }
}
