﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using DevExpress.Pdf;
using DevExpress.XtraEditors;

namespace DevExpress.Docs.Demos {
    public partial class SignatureAppearanceBuilderForm : XtraForm {
        static readonly Dictionary<string, string> dateTimeFormats = new Dictionary<string, string>() {
            { "Short Date", "d" },
            { "Long Date", "D" },
            { "Long Date Short Time", "f" },
            { "Full Date Time", "F" },
            { "Short Date Short Time", "g" },
            { "Short Date Long Time", "G" },
            { "Year Month", "y" },
            { "RFC1123", "r" },
            { "Sortable Date Time", "s" },
            { "Universal Sortable Date Time", "u" }
        };

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public byte[] ImageData { get; set; }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public byte[] BackgroundImageData { get; set; }

        readonly PdfSignatureBuilder builder;
        public SignatureAppearanceBuilderForm(PdfSignatureBuilder builder) {
            this.builder = builder;
            InitializeComponent();
            foreach(string key in dateTimeFormats.Keys)
                cbDateFormat.Properties.Items.Add(key);
        }
        protected override void OnShown(EventArgs e) {
            base.OnShown(e);
            BuildAppearance();
        }
        void BuildAppearance() {
            builder.SetSignatureAppearance(CreateSignatureAppearance());
            Bitmap image = PdfDocumentSigner.CreateBitmap(builder, signatureImageEdit.Size);
            image.MakeTransparent();
            signatureImageEdit.Image = image;
        }
        public Image GetImage() {
            return signatureImageEdit.Image;
        }
        public PdfSignatureAppearance CreateSignatureAppearance() {
            PdfSignatureAppearance appearance = new PdfSignatureAppearance();
            if(ImageData != null)
                appearance.SetImageData(ImageData);
            if(BackgroundImageData != null)
                appearance.SetBackgroundImageData(BackgroundImageData);
            appearance.AppearanceType = importedGraphic.Checked
                ? PdfSignatureAppearanceType.Image
                : noGraphic.Checked ? PdfSignatureAppearanceType.None : PdfSignatureAppearanceType.Name;
            appearance.ShowDate = cbDate.Checked;
            appearance.ShowName = cbName.Checked;
            appearance.ShowLocation = cbLocation.Checked;
            appearance.ShowReason = cbReason.Checked;
            appearance.ShowDistinguishedName = cbDistinguishedName.Checked;
            appearance.RightToLeftTextDirection = cbRightToLeft.Checked;
            string format = null;
            if(!dateTimeFormats.TryGetValue(cbDateFormat.Text, out format))
                format = cbDateFormat.Text;
            appearance.DateTimeFormat = format;
            appearance.ShowLabels = cbLabels.Checked;
            return appearance;
        }
        void CheckedChanged(object sender, EventArgs e) {
            BuildAppearance();
        }
        byte[] OpenImageDialog() {
            using(OpenFileDialog ofd = new OpenFileDialog()) {
                ImageCodecInfo[] codecs = ImageCodecInfo.GetImageEncoders();
                ofd.Filter = string.Format("All image files({1})|{1}|{0}|All files|*",
                    string.Join("|", codecs.Select(codec =>
                    string.Format("{0} ({1})|{1}", codec.FormatDescription, codec.FilenameExtension)).ToArray()),
                    string.Join(";", codecs.Select(codec => codec.FilenameExtension).ToArray()));
                if(ofd.ShowDialog() == DialogResult.OK) {
                    return File.ReadAllBytes(ofd.FileName);
                }
                return null;
            }
        }
        void ImageFileClick(object sender, EventArgs e) {
            ImageData = OpenImageDialog() ?? ImageData;
            importedGraphic.Checked = true;
            BuildAppearance();
        }
        void backgroundImageButton_Click(object sender, EventArgs e) {
            BackgroundImageData = OpenImageDialog() ?? BackgroundImageData;
            BuildAppearance();
        }
        void cbDateFormat_SelectedIndexChanged(object sender, EventArgs e) {
            BuildAppearance();
        }
    }
}
