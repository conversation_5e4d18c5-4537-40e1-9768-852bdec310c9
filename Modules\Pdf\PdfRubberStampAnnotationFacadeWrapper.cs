﻿using System;
using System.ComponentModel;
using System.Drawing.Design;
using System.IO;
using DevExpress.Pdf;

namespace DevExpress.Docs.Demos {
    public class PdfRubberStampAnnotationFacadeWrapper {
        readonly PdfRubberStampAnnotationFacade annotation;
        string customIcon;

        [Category("Annotation Properties")]
        public double Opacity { get { return annotation.Opacity; } set { annotation.Opacity = value; } }

        [TypeConverter(typeof(PdfRubberStampIconNameTypeConverter))]
        [Category("Annotation Properties")]
        public string IconName {
            get { return annotation.IconName; }
            set {
                annotation.IconName = value;
                customIcon = "";
            }
        }

        [Category("Annotation Properties")]
        public bool KeepAspectRatio {
            get { return annotation.KeepAspectRatio; }
            set { annotation.KeepAspectRatio = value; }
        }
        [Category("Annotation Properties")]
        public string Author {
            get { return annotation.Author; }
            set { annotation.Author = value; }
        }
        [Category("Annotation Properties")]
        public DateTimeOffset? CreationDate {
            get { return annotation.CreationDate; }
            set { annotation.CreationDate = value; }
        }

        [TypeConverter(typeof(PdfRectangleTypeConverter))]
        [Category("Annotation Properties")]
        public PdfRectangle Rectangle { get { return annotation.Rectangle; } set { annotation.Rectangle = value; } }

        [Editor(typeof(PdfFileNameEditor), typeof(UITypeEditor))]
        [DisplayName("Set custom icon")]
        [Category("Annotation Properties")]
        public string CustomIcon {
            get { return customIcon; }
            set {
                if(File.Exists(value) && value.EndsWith(".pdf")) {
                    customIcon = value;
                    annotation.SetCustomIcon(customIcon);
                }
            }
        }

        public PdfRubberStampAnnotationFacadeWrapper(PdfRubberStampAnnotationFacade annotation) {
            this.annotation = annotation;
        }
    }
}
