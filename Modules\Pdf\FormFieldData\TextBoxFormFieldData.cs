﻿using System.ComponentModel;
using System.ComponentModel.Design;
using System.Drawing.Design;
using DevExpress.Pdf;

namespace DevExpress.Docs.Demos {
    public class TextBoxFormFieldData : FormFieldData {
        string fText;
        bool fMultiline;
        PdfAcroFormTextFieldType fType;

        [Category(AppearanceCategory), Editor(typeof(MultilineStringEditor), typeof(UITypeEditor))]
        public string Text {
            get { return fText; }
            set {
                fText = value;
                UpdateModel();
            }
        }

        [Category(BehaviorCategory)]
        public bool Multiline {
            get { return fMultiline; }
            set {
                fMultiline = value;
                UpdateModel();
            }
        }

        [Category(AppearanceCategory)]
        public PdfAcroFormTextFieldType Type {
            get { return fType; }
            set {
                fType = value;
                UpdateModel();
            }
        }

        public TextBoxFormFieldData(PdfDocumentPosition position, DocumentFormController controller)
            : base(position, controller) {
        }
        protected override PdfAcroFormCommonVisualField CreateVisualFormField() {
            return new PdfAcroFormTextBoxField(Name, PageNumber, Rectangle.InnerRectangle) {
                Text = Text,
                Multiline = Multiline,
                Type = Type
            };
        }
    }
}
