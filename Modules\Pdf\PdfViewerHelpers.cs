﻿using System.Windows.Forms;
using DevExpress.XtraPdfViewer;

namespace DevExpress.Docs.Demos {
    internal static class PdfViewerHelpers {
        public static void EnableCustomToolMouseNavigation(PdfViewer viewer) {
            viewer.MouseWheel += OnViewerMouseWheel;
        }
        static void OnViewerMouseWheel(object sender, MouseEventArgs e) {
            PdfViewer viewer = ((PdfViewer)sender);
            if(Control.ModifierKeys.HasFlag(Keys.Control))
                viewer.ZoomFactor += e.Delta / 100;
            else
                viewer.ScrollVertical(-e.Delta);
        }
    }
}
