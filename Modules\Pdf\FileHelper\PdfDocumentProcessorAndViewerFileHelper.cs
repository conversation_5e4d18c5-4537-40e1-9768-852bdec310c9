﻿using System;
using System.IO;
using System.Windows.Forms;
using DevExpress.Data.Utils.Security;
using DevExpress.Pdf;
using DevExpress.XtraEditors;
using DevExpress.XtraPdfViewer;

namespace DevExpress.Docs.Demos {
    public class PdfDocumentProcessorAndViewerFileHelper : PdfFileHelper, IDisposable {
        readonly PdfDocumentProcessorFileHelper processorDocument;
        readonly PdfViewerFileHelper viewerDocument;
        readonly PdfDocumentProcessor documentProcessor;
        readonly PdfViewer viewer;
        readonly SensitiveData passwordData = SensitiveData.CreateForCurrentUser();
        public PdfDocumentProcessorAndViewerFileHelper(PdfDocumentProcessor documentProcessor, PdfViewer viewer) {
            this.documentProcessor = documentProcessor;
            this.viewer = viewer;
            processorDocument = new PdfDocumentProcessorFile<PERSON>elper(documentProcessor);
            viewerDocument = new PdfViewerFileHelper(viewer);
            documentProcessor.PasswordRequested += OnDocumentServerPasswordRequested;
            viewer.PasswordRequested += OnViewerPasswordRequested;
        }
        public void Dispose() {
            documentProcessor.PasswordRequested -= OnDocumentServerPasswordRequested;
            viewer.PasswordRequested -= OnViewerPasswordRequested;
        }
        protected override string Creator {
            get { return documentProcessor.Document.Creator; }
            set { documentProcessor.Document.Creator = value; }
        }
        protected override string Producer {
            get { return documentProcessor.Document.Producer; }
            set { documentProcessor.Document.Producer = value; }
        }
        public bool LoadDocumentWithDialog() {
            return PerformDocumentFromOpenDialog(LoadDocument);
        }
        public bool AppendDocument() {
            try {
                using(MemoryStream stream = new MemoryStream()) {
                    if(PerformDocumentFromOpenDialog(AppendDocument)) {
                        documentProcessor.SaveDocument(stream, true);
                        stream.Position = 0;
                        viewerDocument.LoadDocument(stream);
                        return true;
                    }
                }
            }
            catch {
                XtraMessageBox.Show("Not enough memory to append a document.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
            return false;
        }
        public override void LoadDocument(string path, bool detach) {
            processorDocument.LoadDocument(path, detach);
            viewerDocument.LoadDocument(path, detach);
        }
        public override void LoadDocument(Stream stream) {
            processorDocument.LoadDocument(stream);
            viewerDocument.LoadDocument(stream);
        }
        protected override void SaveDocument(string filePath, PdfSaveOptions options) {
            documentProcessor.SaveDocument(filePath, options);
        }
        void AppendDocument(string appendDocumentPath) {
            documentProcessor.AppendDocument(appendDocumentPath);
        }
        bool PerformDocumentFromOpenDialog(Action<string> action) {
            string filePath = ShowFileDialog<OpenFileDialog>();
            if(!string.IsNullOrEmpty(filePath)) {
                while(true) {
                    try {
                        action(filePath);
                        return true;
                    }
                    catch(PdfIncorrectPasswordException) {
                        if(passwordData.Text == null)
                            break;
                        XtraMessageBox.Show("The password is incorrect. Please make sure that Caps Lock is not enabled.", filePath, MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch(OutOfMemoryException) {
                        XtraMessageBox.Show(string.Format("Not enough memory to load the file.\r\n{0}", filePath), "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        break;
                    }
                    catch {
                        string message = "Unable to load the PDF document because the following file is not available or it is not a valid PDF document.\r\n{0}\r\nPlease ensure that the application can access this file and that it is valid, or specify a different file.";
                        XtraMessageBox.Show(string.Format(message, filePath), "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        break;
                    }
                }
            }
            return false;
        }
        void OnDocumentServerPasswordRequested(object sender, PdfPasswordRequestedEventArgs e) {
            using(PasswordForm form = new PasswordForm(Path.GetFileName(e.FileName))) {
                form.StartPosition = FormStartPosition.CenterParent;
                if(form.ShowDialog() == DialogResult.OK) {
                    e.PasswordString = form.Password;
                    passwordData.Text = form.Password;
                }
                else passwordData.Text = null;
            }
        }
        void OnViewerPasswordRequested(object sender, PdfPasswordRequestedEventArgs e) {
            e.PasswordString = passwordData.Text;
        }
    }
}
