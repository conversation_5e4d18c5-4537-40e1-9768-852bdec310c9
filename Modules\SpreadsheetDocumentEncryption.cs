﻿using System;
using System.Windows.Forms;
using DevExpress.DXperience.Demos;
using DevExpress.Spreadsheet;
using DevExpress.XtraEditors;

namespace DevExpress.Docs.Demos {
    public partial class SpreadsheetDocumentEncryption : TutorialControlBase {
        const string saveFileDialogFilter =
             "Excel Workbook (*.xlsx)|*.xlsx|" +
             "Excel Macro-Enabled Workbook (*.xlsm)|*.xlsm|" +
             "Excel Binary Workbook (*.xlsb)|*.xlsb|" +
             "Excel 97-2003 Workbook (*.xls)|*.xls";
        //
        readonly Workbook workbook;
        public SpreadsheetDocumentEncryption() {
            InitializeComponent();
            InitializeEncryptionOptions();
            workbook = new Workbook();
            workbook.LoadDocument(DemoUtils.GetRelativePath("ProfitAndLoss.xlsx"));
            workbook.CalculateFull();
            RefreshPreview();
        }
        void InitializeEncryptionOptions() {
            edPasswordToOpen.Text = "test";
            foreach(EncryptionType currentValue in EnumHelper.GetValues<EncryptionType>())
                edEncryptionType.Properties.Items.Add(currentValue.ToString());
            edEncryptionType.SelectedItem = EncryptionType.Strong.ToString();
        }
        void RefreshPreview() {
            spreadsheetPreview1.Workbook = workbook;
            spreadsheetPreview1.CanShowBorders = true;
            spreadsheetPreview1.UpdatePreview();
        }
        void SaveAs_Click(object sender, EventArgs e) {
            string fileName = DemoUtils.GetSaveFileName(saveFileDialogFilter, "Document");
            if(!string.IsNullOrEmpty(fileName)) {
                try {
                    workbook.DocumentSettings.Encryption.Password = edPasswordToOpen.Text;
                    workbook.DocumentSettings.Encryption.Type = (EncryptionType)Enum.Parse(typeof(EncryptionType), edEncryptionType.Text);
                    workbook.SaveDocument(fileName);
                    DemoUtils.ShowFile(fileName, this);
                }
                catch(Exception ex) {
                    XtraMessageBox.Show(ex.Message, Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
    }
}
