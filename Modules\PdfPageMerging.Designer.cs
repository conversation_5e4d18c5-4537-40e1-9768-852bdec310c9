﻿namespace DevExpress.Docs.Demos
{
    partial class PdfPageMerging
	{
		/// <summary> 
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		#region Component Designer generated code

		/// <summary> 
		/// Required method for Designer support - do not modify 
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            this.pdfViewer = new DevExpress.XtraPdfViewer.PdfViewer();
            this.buttonAppend = new DevExpress.XtraEditors.SimpleButton();
            this.buttonOpen = new DevExpress.XtraEditors.SimpleButton();
            this.saveButton = new DevExpress.XtraEditors.SimpleButton();
            this.newButton = new DevExpress.XtraEditors.SimpleButton();
            this.optionsSidePanel = new DevExpress.XtraEditors.SidePanel();
            this.optionsTablePanel = new DevExpress.Utils.Layout.TablePanel();
            this.optionsSidePanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.optionsTablePanel)).BeginInit();
            this.optionsTablePanel.SuspendLayout();
            this.SuspendLayout();
            // 
            // pdfViewer
            // 
            this.pdfViewer.DetachStreamAfterLoadComplete = true;
            this.pdfViewer.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pdfViewer.Location = new System.Drawing.Point(0, 52);
            this.pdfViewer.Name = "pdfViewer";
            this.pdfViewer.NavigationPaneInitialVisibility = DevExpress.XtraPdfViewer.PdfNavigationPaneVisibility.Hidden;
            this.pdfViewer.ReadOnly = true;
            this.pdfViewer.Size = new System.Drawing.Size(737, 512);
            this.pdfViewer.TabIndex = 5;
            this.pdfViewer.TabStop = false;
            this.pdfViewer.ZoomMode = DevExpress.XtraPdfViewer.PdfZoomMode.FitToWidth;
            // 
            // buttonAppend
            // 
            this.optionsTablePanel.SetColumn(this.buttonAppend, 1);
            this.buttonAppend.Location = new System.Drawing.Point(134, 14);
            this.buttonAppend.Name = "buttonAppend";
            this.optionsTablePanel.SetRow(this.buttonAppend, 0);
            this.buttonAppend.Size = new System.Drawing.Size(110, 23);
            this.buttonAppend.TabIndex = 2;
            this.buttonAppend.Text = "Append...";
            this.buttonAppend.Click += new System.EventHandler(this.OnButtonAppendClick);
            // 
            // buttonOpen
            // 
            this.optionsTablePanel.SetColumn(this.buttonOpen, 0);
            this.buttonOpen.Location = new System.Drawing.Point(18, 14);
            this.buttonOpen.Name = "buttonOpen";
            this.optionsTablePanel.SetRow(this.buttonOpen, 0);
            this.buttonOpen.Size = new System.Drawing.Size(110, 23);
            this.buttonOpen.TabIndex = 1;
            this.buttonOpen.Text = "Open...";
            this.buttonOpen.Click += new System.EventHandler(this.OnButtonOpenClick);
            // 
            // saveButton
            // 
            this.saveButton.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.optionsTablePanel.SetColumn(this.saveButton, 4);
            this.saveButton.Location = new System.Drawing.Point(609, 14);
            this.saveButton.Name = "saveButton";
            this.optionsTablePanel.SetRow(this.saveButton, 0);
            this.saveButton.Size = new System.Drawing.Size(110, 23);
            this.saveButton.TabIndex = 3;
            this.saveButton.Text = "Save as...";
            this.saveButton.Click += new System.EventHandler(this.OnSaveButtonClick);
            // 
            // newButton
            // 
            this.optionsTablePanel.SetColumn(this.newButton, 2);
            this.newButton.Location = new System.Drawing.Point(250, 14);
            this.newButton.Name = "newButton";
            this.optionsTablePanel.SetRow(this.newButton, 0);
            this.newButton.Size = new System.Drawing.Size(124, 23);
            this.newButton.TabIndex = 0;
            this.newButton.Text = "Create New Document";
            this.newButton.Click += new System.EventHandler(this.OnNewButtonClick);
            // 
            // optionsSidePanel
            // 
            this.optionsSidePanel.AllowResize = false;
            this.optionsSidePanel.Controls.Add(this.optionsTablePanel);
            this.optionsSidePanel.Dock = System.Windows.Forms.DockStyle.Top;
            this.optionsSidePanel.Location = new System.Drawing.Point(0, 0);
            this.optionsSidePanel.Name = "optionsSidePanel";
            this.optionsSidePanel.Size = new System.Drawing.Size(737, 52);
            this.optionsSidePanel.TabIndex = 6;
            this.optionsSidePanel.Text = "sidePanel1";
            // 
            // optionsTablePanel
            // 
            this.optionsTablePanel.AutoSize = true;
            this.optionsTablePanel.Columns.AddRange(new DevExpress.Utils.Layout.TablePanelColumn[] {
            new DevExpress.Utils.Layout.TablePanelColumn(DevExpress.Utils.Layout.TablePanelEntityStyle.AutoSize, 5F),
            new DevExpress.Utils.Layout.TablePanelColumn(DevExpress.Utils.Layout.TablePanelEntityStyle.AutoSize, 55F),
            new DevExpress.Utils.Layout.TablePanelColumn(DevExpress.Utils.Layout.TablePanelEntityStyle.AutoSize, 50F),
            new DevExpress.Utils.Layout.TablePanelColumn(DevExpress.Utils.Layout.TablePanelEntityStyle.Relative, 100F),
            new DevExpress.Utils.Layout.TablePanelColumn(DevExpress.Utils.Layout.TablePanelEntityStyle.AutoSize, 50F)});
            this.optionsTablePanel.Controls.Add(this.buttonAppend);
            this.optionsTablePanel.Controls.Add(this.buttonOpen);
            this.optionsTablePanel.Controls.Add(this.saveButton);
            this.optionsTablePanel.Controls.Add(this.newButton);
            this.optionsTablePanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.optionsTablePanel.Location = new System.Drawing.Point(0, 0);
            this.optionsTablePanel.Name = "optionsTablePanel";
            this.optionsTablePanel.Padding = new System.Windows.Forms.Padding(15, 0, 15, 0);
            this.optionsTablePanel.Rows.AddRange(new DevExpress.Utils.Layout.TablePanelRow[] {
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.AutoSize, 26F)});
            this.optionsTablePanel.Size = new System.Drawing.Size(737, 51);
            this.optionsTablePanel.TabIndex = 4;
            // 
            // PdfPageMerging
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.pdfViewer);
            this.Controls.Add(this.optionsSidePanel);
            this.Name = "PdfPageMerging";
            this.Size = new System.Drawing.Size(737, 564);
            this.optionsSidePanel.ResumeLayout(false);
            this.optionsSidePanel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.optionsTablePanel)).EndInit();
            this.optionsTablePanel.ResumeLayout(false);
            this.ResumeLayout(false);

		}

		#endregion
        private XtraPdfViewer.PdfViewer pdfViewer;
        private XtraEditors.SimpleButton buttonAppend;
        private XtraEditors.SimpleButton buttonOpen;
        private XtraEditors.SimpleButton saveButton;
        private XtraEditors.SimpleButton newButton;
        private XtraEditors.SidePanel optionsSidePanel;
        private Utils.Layout.TablePanel optionsTablePanel;
    }
}
