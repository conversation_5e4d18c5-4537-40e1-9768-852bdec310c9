﻿using System;
using System.Collections;
using System.ComponentModel;
using System.Globalization;
using DevExpress.Pdf;

namespace DevExpress.Docs.Demos {
    public class PdfRGBColorTypeConverter : ExpandableObjectConverter {
        public override object CreateInstance(ITypeDescriptorContext context, IDictionary propertyValues) {
            return new PdfRGBColor((double)propertyValues["R"], (double)propertyValues["G"], (double)propertyValues["B"]);
        }
        public override bool GetCreateInstanceSupported(ITypeDescriptorContext context) {
            return true;
        }
        public override object ConvertTo(ITypeDescriptorContext context, CultureInfo culture, object value, Type destinationType) {
            PdfRGBColor color = value as PdfRGBColor;
            if(destinationType == typeof(String) && color != null)
                return string.Format(culture, "R={0:0.000}, G={1:0.000}, B={2:0.000}", color.R, color.G, color.B);
            return base.ConvertTo(context, culture, value, destinationType);
        }
    }
}
