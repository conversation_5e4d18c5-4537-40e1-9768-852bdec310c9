﻿using System;
using System.Collections;
using System.ComponentModel;
using System.Globalization;
using DevExpress.Pdf;

namespace DevExpress.Docs.Demos {
    public class PdfRectangleTypeConverter : TypeConverter {
        public override PropertyDescriptorCollection GetProperties(ITypeDescriptorContext context, object value, Attribute[] attributes) {
            if(value is PdfRectangle) {
                PropertyDescriptorCollection collection = TypeDescriptor.GetProperties(value);
                return new PropertyDescriptorCollection(new PropertyDescriptor[] { collection["Left"], collection["Bottom"], collection["Right"], collection["Top"] });
            }
            return base.GetProperties(context, value, attributes);
        }
        public override object CreateInstance(ITypeDescriptorContext context, IDictionary propertyValues) {
            return new PdfRectangle((double)propertyValues["Left"], (double)propertyValues["Bottom"], (double)propertyValues["Right"], (double)propertyValues["Top"]);
        }
        public override bool GetCreateInstanceSupported(ITypeDescriptorContext context) {
            return true;
        }
        public override bool GetPropertiesSupported(ITypeDescriptorContext context) {
            return true;
        }
        public override object ConvertTo(ITypeDescriptorContext context, CultureInfo culture, object value, Type destinationType) {
            PdfRectangle rectangle = value as PdfRectangle;
            if(destinationType == typeof(String) && rectangle != null)
                return string.Format("Left={0}, Bottom={1}, Right={2}, Top={3}", (int)rectangle.Left, (int)rectangle.Bottom, (int)rectangle.Right, (int)rectangle.Top);
            return base.ConvertTo(context, culture, value, destinationType);
        }
    }
}
